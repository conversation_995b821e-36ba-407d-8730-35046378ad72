#!/bin/bash

# 最简单的Docker构建测试脚本

echo "=== 简单Docker构建测试 ==="

# 禁用BuildKit以避免复杂的网络问题
export DOCKER_BUILDKIT=0

echo "1. 清理旧镜像和缓存..."
docker rmi wb_backend:latest 2>/dev/null || true
docker system prune -f

echo "2. 测试网络连接..."
if ping -c 1 8.8.8.8 >/dev/null 2>&1; then
    echo "✅ 网络连接正常"
else
    echo "❌ 网络连接异常"
fi

echo "3. 使用简化版Dockerfile构建..."
if docker build -f Dockerfile.simple -t wb_backend:latest .; then
    echo "✅ 构建成功！"
    echo ""
    echo "镜像信息:"
    docker images wb_backend:latest
    echo ""
    echo "可以运行以下命令测试:"
    echo "docker run --rm -p 8000:8000 wb_backend:latest"
else
    echo "❌ 构建失败"
    echo ""
    echo "尝试更简单的方法..."
    echo "创建最小化Dockerfile..."
    
    cat > Dockerfile.minimal << 'EOF'
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF
    
    echo "使用最小化Dockerfile构建..."
    if docker build -f Dockerfile.minimal -t wb_backend:latest .; then
        echo "✅ 最小化构建成功！"
    else
        echo "❌ 最小化构建也失败"
        echo "请检查网络连接和Docker配置"
    fi
fi
