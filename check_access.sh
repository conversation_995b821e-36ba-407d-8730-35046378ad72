#!/bin/bash

# 检查服务访问问题

echo "=== 服务访问诊断 ==="

# 1. 检查容器状态
echo "1. 检查容器状态..."
docker-compose ps

echo ""
echo "2. 检查容器详细状态..."
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# 3. 检查端口监听
echo ""
echo "3. 检查端口监听..."
echo "系统端口监听状态:"
netstat -tlnp | grep -E ":8000|:3306|:6379|:80"

# 4. 检查服务健康状态
echo ""
echo "4. 检查服务健康状态..."

# 检查后端服务
echo "检查后端服务..."
if curl -s http://localhost:8000/health >/dev/null 2>&1; then
    echo "✅ 后端服务本地访问正常"
    echo "健康检查响应:"
    curl -s http://localhost:8000/health
else
    echo "❌ 后端服务本地访问异常"
    echo "尝试检查容器内部..."
    docker exec wb_backend curl -s http://localhost:8000/health 2>/dev/null || echo "容器内部也无法访问"
fi

# 检查API文档
echo ""
echo "检查API文档..."
if curl -s http://localhost:8000/docs >/dev/null 2>&1; then
    echo "✅ API文档本地访问正常"
else
    echo "❌ API文档本地访问异常"
fi

# 5. 检查防火墙状态
echo ""
echo "5. 检查防火墙状态..."
if systemctl is-active firewalld >/dev/null 2>&1; then
    echo "FirewallD状态: 运行中"
    echo "开放的端口:"
    firewall-cmd --list-ports 2>/dev/null || echo "无法获取端口列表"
    
    echo "检查8000端口是否开放:"
    if firewall-cmd --query-port=8000/tcp 2>/dev/null; then
        echo "✅ 8000端口已开放"
    else
        echo "❌ 8000端口未开放"
        echo "正在开放8000端口..."
        firewall-cmd --add-port=8000/tcp --permanent
        firewall-cmd --add-port=80/tcp --permanent
        firewall-cmd --reload
        echo "端口已开放"
    fi
else
    echo "FirewallD状态: 未运行"
fi

# 6. 检查iptables规则
echo ""
echo "6. 检查iptables规则..."
iptables -L INPUT | grep -E "8000|ACCEPT|DROP" | head -5

# 7. 检查容器日志
echo ""
echo "7. 检查容器日志..."
echo "后端容器日志 (最后10行):"
docker logs wb_backend --tail 10

# 8. 测试内部网络连接
echo ""
echo "8. 测试内部网络连接..."
echo "从MySQL容器ping后端容器:"
docker exec wb_mysql ping -c 2 wb_backend 2>/dev/null || echo "网络连接异常"

# 9. 检查应用启动状态
echo ""
echo "9. 检查应用启动状态..."
echo "后端容器进程:"
docker exec wb_backend ps aux | grep uvicorn || echo "uvicorn进程未找到"

# 10. 提供解决方案
echo ""
echo "10. 解决方案建议..."

# 检查本地访问是否正常
if curl -s http://localhost:8000/health >/dev/null 2>&1; then
    echo "✅ 本地访问正常，问题可能是:"
    echo "   1. 防火墙阻止外部访问"
    echo "   2. 云服务器安全组未开放8000端口"
    echo "   3. 网络配置问题"
    echo ""
    echo "解决步骤:"
    echo "   1. 开放防火墙端口: firewall-cmd --add-port=8000/tcp --permanent && firewall-cmd --reload"
    echo "   2. 检查云服务器安全组设置"
    echo "   3. 确认服务器IP地址: curl ifconfig.me"
else
    echo "❌ 本地访问异常，问题可能是:"
    echo "   1. 应用启动失败"
    echo "   2. 端口绑定问题"
    echo "   3. 容器网络问题"
    echo ""
    echo "解决步骤:"
    echo "   1. 重启后端容器: docker-compose restart backend"
    echo "   2. 检查应用日志: docker logs wb_backend"
    echo "   3. 检查端口绑定: docker port wb_backend"
fi

echo ""
echo "=== 诊断完成 ==="
