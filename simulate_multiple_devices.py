#!/usr/bin/env python3
"""
模拟多个设备连接WebSocket
"""

import asyncio
import websockets
import json
import time
import random

class DeviceSimulator:
    """设备模拟器"""
    
    def __init__(self, device_number):
        self.device_number = device_number
        self.uri = f"ws://localhost:8000/ws/{device_number}"
        self.connected = False
        
    async def connect_and_run(self):
        """连接并运行设备"""
        print(f"🔌 设备 {self.device_number} 尝试连接WebSocket: {self.uri}")
        
        try:
            async with websockets.connect(self.uri) as websocket:
                self.connected = True
                print(f"✅ 设备 {self.device_number} 已连接WebSocket")
                
                # 发送初始心跳
                await self.send_heartbeat(websocket)
                
                # 监听消息循环
                await self.message_loop(websocket)
                
        except Exception as e:
            print(f"❌ 设备 {self.device_number} 连接失败: {e}")
            self.connected = False
            
    async def send_heartbeat(self, websocket):
        """发送心跳消息"""
        heartbeat_msg = {
            "type": "heartbeat",
            "timestamp": time.time(),
            "device_number": self.device_number
        }
        
        await websocket.send(json.dumps(heartbeat_msg))
        print(f"💓 设备 {self.device_number} 发送心跳")
        
    async def message_loop(self, websocket):
        """消息监听循环"""
        print(f"📡 设备 {self.device_number} 开始监听消息...")
        
        while True:
            try:
                # 等待服务器消息（5秒超时）
                message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                await self.handle_message(websocket, message)
                
            except asyncio.TimeoutError:
                # 超时，发送心跳
                await self.send_heartbeat(websocket)
                
            except websockets.exceptions.ConnectionClosed:
                print(f"🔌 设备 {self.device_number} 连接已关闭")
                break
                
    async def handle_message(self, websocket, message):
        """处理收到的消息"""
        try:
            msg_data = json.loads(message)
            msg_type = msg_data.get('type')

            if msg_type == 'task':
                await self.handle_task(websocket, msg_data)
            elif msg_type == 'like':  # 新增：处理点赞任务
                await self.handle_like_task(websocket, msg_data)
            elif msg_type == 'heartbeat':
                await self.handle_heartbeat(websocket, msg_data)
            else:
                print(f"📨 设备 {self.device_number} 收到未知消息类型: {msg_type}")
                print(f"   消息内容: {message}")

        except json.JSONDecodeError:
            print(f"⚠️ 设备 {self.device_number} 无法解析消息: {message}")
            
    async def handle_task(self, websocket, task_data):
        """处理任务消息"""
        task_id = task_data.get('task_id')
        task_type = task_data.get('task_type', 'unknown')
        
        print(f"🚀 设备 {self.device_number} 收到任务: ID={task_id}, 类型={task_type}")
        
        # 模拟任务执行时间（1-3秒随机）
        execution_time = random.uniform(1, 3)
        await asyncio.sleep(execution_time)
        
        # 模拟任务成功率（90%成功）
        success = random.random() < 0.9
        status = "completed" if success else "failed"
        result = f"模拟执行{'成功' if success else '失败'}" 
        
        # 发送任务完成消息
        completion_msg = {
            "type": "task_completion",
            "task_id": task_id,
            "device_id": task_data.get('device_id'),  # 包含device_id
            "status": status,
            "result": result,
            "timestamp": time.time()
        }
        
        await websocket.send(json.dumps(completion_msg))
        print(f"✅ 设备 {self.device_number} 任务完成: {status} - {result}")

    async def handle_like_task(self, websocket, task_data):
        """处理点赞任务消息"""
        task_id = task_data.get('task_id')
        device_id = task_data.get('device_id')
        parameters = task_data.get('parameters', {})
        blogger_id = parameters.get('blogger_id')
        like_id = parameters.get('like_id')
        delay_click = parameters.get('delay_click', 500)

        print(f"🚀 设备 {self.device_number} 收到点赞任务:")
        print(f"   任务ID: {task_id}")
        print(f"   设备ID: {device_id}")
        print(f"   博主ID: {blogger_id}")
        print(f"   点赞ID: {like_id}")
        print(f"   点击延迟: {delay_click}ms")

        # 模拟任务执行时间（根据delay_click + 随机时间）
        execution_time = (delay_click / 1000.0) + random.uniform(0.5, 2.0)
        print(f"   模拟执行时间: {execution_time:.2f}秒")
        await asyncio.sleep(execution_time)

        # 模拟任务成功率（90%成功）
        success = random.random() < 0.9
        status = "completed" if success else "failed"
        result = f"点赞任务{'成功' if success else '失败'} - 博主:{blogger_id}, 点赞:{like_id}"

        # 发送任务完成消息
        completion_msg = {
            "type": "task_completion",
            "task_id": task_id,
            "device_id": device_id,
            "status": status,
            "result": result,
            "timestamp": time.time()
        }

        await websocket.send(json.dumps(completion_msg))
        print(f"✅ 设备 {self.device_number} 点赞任务完成: {status} - {result}")

    async def handle_heartbeat(self, websocket, heartbeat_data):
        """处理心跳消息"""
        # 回复心跳
        heartbeat_response = {
            "type": "heartbeat",
            "timestamp": time.time(),
            "device_number": self.device_number
        }
        await websocket.send(json.dumps(heartbeat_response))
        print(f"💓 设备 {self.device_number} 回复心跳")

async def simulate_multiple_devices():
    """模拟多个设备连接"""
    # 要模拟的设备列表
    device_numbers = [
        "device_test_01",
        "device_test_02", 
        "device_test_03",
        "device_test_04",
        "device_test_05",
        "device_test_06"
    ]
    
    print(f"🤖 启动 {len(device_numbers)} 个设备模拟器")
    print("="*50)
    
    # 创建设备模拟器
    simulators = [DeviceSimulator(device_num) for device_num in device_numbers]
    
    # 并发启动所有设备
    tasks = [simulator.connect_and_run() for simulator in simulators]
    
    try:
        await asyncio.gather(*tasks)
    except KeyboardInterrupt:
        print("\n👋 用户中断，停止所有设备模拟器")

async def main():
    """主函数"""
    print("🤖 多设备模拟器启动")
    print("="*50)
    
    try:
        await simulate_multiple_devices()
    except KeyboardInterrupt:
        print("\n🏁 多设备模拟器结束")

if __name__ == "__main__":
    asyncio.run(main())
