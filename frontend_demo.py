#!/usr/bin/env python3

"""
微博任务管理系统 - 前端演示
简单的Tkinter界面用于测试后端连接
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import json
from datetime import datetime
from api_client import api_client

class WBSystemGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("微博任务管理系统")
        self.root.geometry("1000x700")
        
        # 创建界面
        self.create_widgets()
        
        # 启动时测试连接
        self.test_connection()
    
    def create_widgets(self):
        """创建界面组件"""
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="微博任务管理系统", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 连接状态框架
        status_frame = ttk.LabelFrame(main_frame, text="连接状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        # 服务器地址
        ttk.Label(status_frame, text="服务器地址:").grid(row=0, column=0, sticky=tk.W)
        self.server_label = ttk.Label(status_frame, text="http://43.138.50.5", foreground="blue")
        self.server_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 连接状态
        ttk.Label(status_frame, text="连接状态:").grid(row=1, column=0, sticky=tk.W)
        self.status_label = ttk.Label(status_frame, text="检测中...", foreground="orange")
        self.status_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 0))
        
        # 测试连接按钮
        self.test_btn = ttk.Button(status_frame, text="测试连接", command=self.test_connection)
        self.test_btn.grid(row=0, column=2, rowspan=2, padx=(10, 0))
        
        # 功能按钮框架
        button_frame = ttk.LabelFrame(main_frame, text="功能测试", padding="10")
        button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # 功能按钮
        ttk.Button(button_frame, text="获取系统信息", command=self.get_system_info).grid(row=0, column=0, padx=5, pady=5)
        ttk.Button(button_frame, text="获取设备列表", command=self.get_devices).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(button_frame, text="获取任务列表", command=self.get_tasks).grid(row=0, column=2, padx=5, pady=5)
        ttk.Button(button_frame, text="获取分组列表", command=self.get_groups).grid(row=1, column=0, padx=5, pady=5)
        ttk.Button(button_frame, text="清空日志", command=self.clear_log).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(button_frame, text="刷新状态", command=self.refresh_status).grid(row=1, column=2, padx=5, pady=5)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.grid(row=2, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, width=60, height=20)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_bar = ttk.Label(main_frame, text="就绪", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        
        # 更新状态栏
        self.status_bar.config(text=message)
    
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("日志已清空")
    
    def test_connection(self):
        """测试连接"""
        def _test():
            self.log("正在测试后端连接...")
            self.status_label.config(text="检测中...", foreground="orange")
            
            try:
                # 测试健康检查
                health = api_client.health_check()
                
                if health.get("status") == "healthy":
                    self.status_label.config(text="连接正常", foreground="green")
                    self.log("✅ 后端连接成功")
                    self.log(f"服务器信息: {health.get('service', 'unknown')}")
                    self.log(f"版本: {health.get('version', 'unknown')}")
                else:
                    self.status_label.config(text="连接异常", foreground="red")
                    self.log("❌ 后端连接失败")
                    self.log(f"响应: {health}")
                    
            except Exception as e:
                self.status_label.config(text="连接失败", foreground="red")
                self.log(f"❌ 连接错误: {str(e)}")
        
        # 在后台线程中执行
        threading.Thread(target=_test, daemon=True).start()
    
    def get_system_info(self):
        """获取系统信息"""
        def _get():
            self.log("正在获取系统信息...")
            try:
                info = api_client.system_info()
                self.log("✅ 系统信息获取成功")
                self.log(f"主机名: {info.get('hostname', 'unknown')}")
                self.log(f"本地IP: {info.get('local_ip', 'unknown')}")
                self.log(f"Python版本: {info.get('python_version', 'unknown')}")
            except Exception as e:
                self.log(f"❌ 获取系统信息失败: {str(e)}")
        
        threading.Thread(target=_get, daemon=True).start()
    
    def get_devices(self):
        """获取设备列表"""
        def _get():
            self.log("正在获取设备列表...")
            try:
                devices = api_client.get_devices()
                if "error" in devices:
                    self.log(f"❌ 获取设备列表失败: {devices['error']}")
                else:
                    self.log("✅ 设备列表获取成功")
                    if isinstance(devices, list):
                        self.log(f"设备数量: {len(devices)}")
                    else:
                        self.log(f"响应: {devices}")
            except Exception as e:
                self.log(f"❌ 获取设备列表错误: {str(e)}")
        
        threading.Thread(target=_get, daemon=True).start()
    
    def get_tasks(self):
        """获取任务列表"""
        def _get():
            self.log("正在获取任务列表...")
            try:
                tasks = api_client.get_tasks()
                if "error" in tasks:
                    self.log(f"❌ 获取任务列表失败: {tasks['error']}")
                else:
                    self.log("✅ 任务列表获取成功")
                    if isinstance(tasks, list):
                        self.log(f"任务数量: {len(tasks)}")
                    else:
                        self.log(f"响应: {tasks}")
            except Exception as e:
                self.log(f"❌ 获取任务列表错误: {str(e)}")
        
        threading.Thread(target=_get, daemon=True).start()
    
    def get_groups(self):
        """获取分组列表"""
        def _get():
            self.log("正在获取分组列表...")
            try:
                groups = api_client.get_groups()
                if "error" in groups:
                    self.log(f"❌ 获取分组列表失败: {groups['error']}")
                else:
                    self.log("✅ 分组列表获取成功")
                    if isinstance(groups, list):
                        self.log(f"分组数量: {len(groups)}")
                    else:
                        self.log(f"响应: {groups}")
            except Exception as e:
                self.log(f"❌ 获取分组列表错误: {str(e)}")
        
        threading.Thread(target=_get, daemon=True).start()
    
    def refresh_status(self):
        """刷新状态"""
        self.test_connection()
        self.log("状态已刷新")

def main():
    """主函数"""
    root = tk.Tk()
    app = WBSystemGUI(root)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("程序被用户中断")

if __name__ == "__main__":
    main()
