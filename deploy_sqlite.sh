#!/bin/bash

# 微博任务管理系统 - SQLite版本（无需MySQL）

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

echo "========================================"
echo "  微博任务管理系统 - SQLite版本"
echo "========================================"
echo "使用SQLite数据库，避免MySQL配置问题"
echo ""

# 1. 清理之前的部署
log_step "1. 清理之前的部署"
docker-compose down 2>/dev/null || true
sudo systemctl stop wb-proxy 2>/dev/null || true
sudo systemctl stop wb-system 2>/dev/null || true
sudo systemctl stop wb-system-v2 2>/dev/null || true
sudo systemctl stop wb-proxy-v2 2>/dev/null || true
sudo pkill -f "uvicorn.*8000" 2>/dev/null || true
sudo pkill -f "proxy_server" 2>/dev/null || true

# 2. 创建应用目录
log_step "2. 创建应用目录"
APP_DIR="/opt/wb_system_sqlite"
sudo mkdir -p $APP_DIR
sudo chown -R $(whoami):$(whoami) $APP_DIR
cd $APP_DIR

# 3. 创建Python环境
log_step "3. 创建Python环境"
python3 -m venv venv
source venv/bin/activate

# 安装依赖
cat > requirements.txt << 'EOF'
fastapi==0.115.12
uvicorn[standard]==0.34.3
sqlalchemy==2.0.41
python-dotenv==1.1.0
pydantic==2.11.5
websockets==15.0.1
requests==2.32.3
python-multipart==0.0.20
aiofiles==24.1.0
EOF

pip install --upgrade pip
pip install -r requirements.txt

# 4. 创建应用结构
mkdir -p app/{models,routes,services,schemas,utils,websocket}
mkdir -p data

# 5. 创建SQLite数据库配置
cat > app/db.py << 'EOF'
from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
import logging

logger = logging.getLogger(__name__)

# 使用SQLite数据库
DB_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "wb.db")
DATABASE_URL = f"sqlite:///{DB_PATH}"

print(f"[INFO] 数据库路径: {DB_PATH}")
print(f"[INFO] 数据库URL: {DATABASE_URL}")

# 确保数据目录存在
os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)

engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False},  # SQLite特有配置
    echo=False
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def test_connection():
    """测试数据库连接"""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1 as test"))
            return True
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False

# 启动时测试连接
if test_connection():
    print("[SUCCESS] SQLite数据库连接成功")
else:
    print("[ERROR] SQLite数据库连接失败")
EOF

# 6. 创建主应用
cat > app/main.py << 'EOF'
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
from datetime import datetime
import socket
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="微博任务管理系统",
    description="微博任务管理系统API - SQLite版",
    version="1.0.0"
)

# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

@app.get("/")
async def root():
    return {
        "message": "微博任务管理系统",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "deployment": "sqlite-native",
        "database": "SQLite"
    }

@app.get("/health")
async def health_check():
    # 测试数据库连接
    try:
        from app.db import test_connection
        db_status = "connected" if test_connection() else "disconnected"
    except Exception as e:
        db_status = f"error: {str(e)}"
    
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "service": "wb_backend",
        "deployment": "sqlite-native",
        "server_ip": get_local_ip(),
        "database": db_status,
        "database_type": "SQLite"
    }

@app.get("/system")
async def system_info():
    return {
        "hostname": socket.gethostname(),
        "local_ip": get_local_ip(),
        "python_version": os.sys.version,
        "deployment": "sqlite-native",
        "database_type": "SQLite",
        "database_path": os.path.join(os.getcwd(), "data", "wb.db")
    }

# API端点
@app.get("/api/devices")
async def get_devices():
    return {"devices": [], "total": 0, "message": "设备列表API"}

@app.get("/api/tasks")
async def get_tasks():
    return {"tasks": [], "total": 0, "message": "任务列表API"}

@app.get("/api/groups")
async def get_groups():
    return {"groups": [], "total": 0, "message": "分组列表API"}

@app.post("/api/devices")
async def create_device():
    return {"message": "设备创建成功", "status": "success"}

@app.post("/api/tasks")
async def create_task():
    return {"message": "任务创建成功", "status": "success"}

@app.post("/api/groups")
async def create_group():
    return {"message": "分组创建成功", "status": "success"}

@app.on_event("startup")
async def startup_event():
    logger.info("微博任务管理系统启动中...")
    
    # 初始化数据库表
    try:
        from app.db import Base, engine
        Base.metadata.create_all(bind=engine)
        logger.info("SQLite数据库表初始化成功")
    except Exception as e:
        logger.warning(f"数据库表初始化失败: {e}")
    
    logger.info("应用启动完成")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

# 7. 创建启动脚本
cat > start.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate

echo "启动微博任务管理系统 (SQLite版)..."
echo "访问地址: http://localhost:8000"
echo "API文档: http://localhost:8000/docs"
echo ""

uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
EOF

chmod +x start.sh

# 8. 复制代理服务
cp /www/wwwroot/fastApiProject/proxy_server.py ./proxy_server.py 2>/dev/null || cat > proxy_server.py << 'EOF'
#!/usr/bin/env python3
import http.server
import socketserver
import urllib.request
from datetime import datetime

class ProxyHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        self.proxy_request()
    
    def do_POST(self):
        self.proxy_request()
    
    def proxy_request(self):
        try:
            target_url = f"http://127.0.0.1:8000{self.path}"
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length) if content_length > 0 else None
            
            req = urllib.request.Request(target_url, data=post_data, method=self.command)
            
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'content-length']:
                    req.add_header(header, value)
            
            with urllib.request.urlopen(req, timeout=30) as response:
                self.send_response(response.getcode())
                for header, value in response.headers.items():
                    if header.lower() not in ['server', 'date']:
                        self.send_header(header, value)
                self.end_headers()
                self.wfile.write(response.read())
                
        except Exception as e:
            self.send_error(502, f"Bad Gateway: {str(e)}")

if __name__ == "__main__":
    with socketserver.TCPServer(("", 80), ProxyHandler) as httpd:
        print("🚀 代理服务器启动在80端口")
        print("🔄 转发到: http://127.0.0.1:8000")
        print("🌐 访问地址: http://***********/")
        httpd.serve_forever()
EOF

chmod +x proxy_server.py

# 9. 创建系统服务
sudo tee /etc/systemd/system/wb-system-sqlite.service << EOF
[Unit]
Description=微博任务管理系统 SQLite版
After=network.target

[Service]
Type=simple
User=$(whoami)
WorkingDirectory=$APP_DIR
Environment=PATH=$APP_DIR/venv/bin
ExecStart=$APP_DIR/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

sudo tee /etc/systemd/system/wb-proxy-sqlite.service << EOF
[Unit]
Description=微博任务管理系统代理 SQLite版
After=network.target wb-system-sqlite.service

[Service]
Type=simple
User=root
WorkingDirectory=$APP_DIR
ExecStart=/usr/bin/python3 $APP_DIR/proxy_server.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl enable wb-system-sqlite
sudo systemctl enable wb-proxy-sqlite

echo ""
echo "========================================"
echo "  SQLite版本部署完成！"
echo "========================================"
echo ""
log_info "项目目录: $APP_DIR"
log_info "数据库: SQLite (无需MySQL配置)"
log_info "启动方式:"
echo "  手动启动: cd $APP_DIR && ./start.sh"
echo "  服务启动: sudo systemctl start wb-system-sqlite"
echo "  代理启动: sudo systemctl start wb-proxy-sqlite"
echo ""
log_info "访问地址:"
echo "  直接访问: http://***********:8000"
echo "  代理访问: http://***********"
echo "  API文档: http://***********:8000/docs"
echo ""
echo "🎉 SQLite版本部署完成！无需复杂的数据库配置"
