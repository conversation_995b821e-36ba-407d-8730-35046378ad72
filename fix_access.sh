#!/bin/bash

# 快速修复访问问题

echo "=== 快速修复访问问题 ==="

# 1. 开放防火墙端口
echo "1. 开放防火墙端口..."
if systemctl is-active firewalld >/dev/null 2>&1; then
    echo "开放8000和80端口..."
    firewall-cmd --add-port=8000/tcp --permanent
    firewall-cmd --add-port=80/tcp --permanent
    firewall-cmd --add-port=3306/tcp --permanent
    firewall-cmd --reload
    echo "✅ 防火墙端口已开放"
else
    echo "防火墙未运行，跳过"
fi

# 2. 检查容器状态
echo ""
echo "2. 检查容器状态..."
docker-compose ps

# 3. 重启后端容器（如果需要）
echo ""
echo "3. 重启后端容器..."
docker-compose restart backend
sleep 10

# 4. 检查服务状态
echo ""
echo "4. 检查服务状态..."
for i in {1..10}; do
    if curl -s http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ 后端服务正常"
        break
    fi
    echo "等待后端服务启动... ($i/10)"
    sleep 3
done

# 5. 显示访问信息
echo ""
echo "5. 访问信息..."
echo "服务器IP地址:"
curl -s ifconfig.me || curl -s ipinfo.io/ip || echo "无法获取公网IP"

echo ""
echo "本地测试:"
curl -s http://localhost:8000/health && echo " - 本地健康检查正常" || echo " - 本地健康检查失败"

echo ""
echo "端口监听状态:"
netstat -tlnp | grep :8000

echo ""
echo "容器端口映射:"
docker port wb_backend

# 6. 测试外部访问
echo ""
echo "6. 测试外部访问..."
SERVER_IP=$(curl -s ifconfig.me 2>/dev/null)
if [ -n "$SERVER_IP" ]; then
    echo "尝试从服务器内部访问外部IP..."
    if curl -s --connect-timeout 5 http://$SERVER_IP:8000/health >/dev/null 2>&1; then
        echo "✅ 外部IP访问正常"
    else
        echo "❌ 外部IP访问失败，可能是安全组问题"
    fi
fi

echo ""
echo "=== 修复完成 ==="
echo ""
echo "🌐 访问地址："
echo "  - 健康检查: http://$SERVER_IP:8000/health"
echo "  - API文档: http://$SERVER_IP:8000/docs"
echo "  - 后端API: http://$SERVER_IP:8000"
echo ""
echo "如果仍无法访问，请检查："
echo "1. 云服务器安全组是否开放8000端口"
echo "2. 网络运营商是否限制端口访问"
echo "3. 本地网络是否有防火墙限制"
