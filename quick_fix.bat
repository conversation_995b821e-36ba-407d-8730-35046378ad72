@echo off
REM 快速修复Docker构建问题

echo === 快速修复Docker构建问题 ===

REM 1. 清理所有Docker缓存
echo [1/5] 清理Docker缓存...
docker system prune -a -f
docker builder prune -a -f

REM 2. 重启Docker网络
echo [2/5] 重置Docker网络...
docker network prune -f

REM 3. 测试网络连接
echo [3/5] 测试网络连接...
echo 测试清华源...
curl -I https://pypi.tuna.tsinghua.edu.cn/simple/ 2>nul
echo 测试阿里云源...
curl -I https://mirrors.aliyun.com/pypi/simple/ 2>nul

REM 4. 使用最简单的构建方式
echo [4/5] 尝试简化构建...
set DOCKER_BUILDKIT=0
docker build --no-cache --network=host -t wb_backend:latest .

if !errorlevel! equ 0 (
    echo [SUCCESS] 构建成功！
    goto :end
)

REM 5. 如果还是失败，尝试离线方式
echo [5/5] 尝试离线构建...
echo 正在下载依赖包...
python download_wheels.py

if exist wheels (
    echo 使用离线Dockerfile构建...
    docker build -f Dockerfile.offline --no-cache -t wb_backend:latest .
    
    if !errorlevel! equ 0 (
        echo [SUCCESS] 离线构建成功！
    ) else (
        echo [ERROR] 离线构建也失败了
        echo 请检查网络设置或联系技术支持
    )
) else (
    echo [ERROR] 无法下载依赖包
    echo 请检查Python环境和网络连接
)

:end
pause
