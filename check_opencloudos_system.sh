#!/bin/bash

# OpenCloudOS 9系统检测脚本
# 检查系统环境和依赖

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统版本
check_system() {
    log_info "检查系统信息..."
    
    # 检查OpenCloudOS
    if [ -f /etc/opencloudos-release ]; then
        OPENCLOUDOS_VERSION=$(cat /etc/opencloudos-release)
        log_success "系统版本: $OPENCLOUDOS_VERSION"
        
        # 检查版本号
        if grep -q "OpenCloudOS release 9" /etc/opencloudos-release; then
            OPENCLOUDOS_MAJOR=9
            log_info "检测到OpenCloudOS 9"
        elif grep -q "OpenCloudOS release 8" /etc/opencloudos-release; then
            OPENCLOUDOS_MAJOR=8
            log_info "检测到OpenCloudOS 8"
        else
            log_warning "未识别的OpenCloudOS版本，将按OpenCloudOS 9处理"
            OPENCLOUDOS_MAJOR=9
        fi
    elif [ -f /etc/centos-release ]; then
        # 兼容CentOS检测
        CENTOS_VERSION=$(cat /etc/centos-release)
        log_success "兼容系统版本: $CENTOS_VERSION"
        OPENCLOUDOS_MAJOR=9
    elif [ -f /etc/redhat-release ]; then
        # 兼容RHEL检测
        REDHAT_VERSION=$(cat /etc/redhat-release)
        log_success "兼容系统版本: $REDHAT_VERSION"
        OPENCLOUDOS_MAJOR=9
    else
        log_error "不支持的系统，此脚本仅支持OpenCloudOS/CentOS/RHEL系列"
        exit 1
    fi
    
    # 显示系统信息
    echo "系统架构: $(uname -m)"
    echo "内核版本: $(uname -r)"
    echo "可用内存: $(free -h | grep Mem | awk '{print $2}')"
    echo "可用磁盘: $(df -h / | tail -1 | awk '{print $4}')"
    
    # 检查包管理器
    if command -v dnf >/dev/null 2>&1; then
        PACKAGE_MANAGER="dnf"
        log_success "包管理器: DNF"
    elif command -v yum >/dev/null 2>&1; then
        PACKAGE_MANAGER="yum"
        log_success "包管理器: YUM"
    else
        log_error "未找到包管理器"
        exit 1
    fi
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    
    # 检查基本网络连接
    if ping -c 1 8.8.8.8 >/dev/null 2>&1; then
        log_success "网络连接正常"
    else
        log_error "网络连接失败，请检查网络设置"
        exit 1
    fi
    
    # 检查DNS解析
    if nslookup docker.com >/dev/null 2>&1; then
        log_success "DNS解析正常"
    else
        log_warning "DNS解析可能有问题"
    fi
    
    # 检查腾讯云镜像源
    if curl -s --connect-timeout 5 http://mirrors.tencent.com >/dev/null; then
        log_success "腾讯云镜像源可访问"
    else
        log_warning "腾讯云镜像源不可访问，将使用默认源"
    fi
    
    # 检查Docker Hub连接
    if curl -s --connect-timeout 5 https://registry-1.docker.io >/dev/null; then
        log_success "Docker Hub可访问"
    else
        log_warning "Docker Hub访问较慢，建议配置镜像加速"
    fi
}

# 检查防火墙状态
check_firewall() {
    log_info "检查防火墙状态..."
    
    # 检查firewalld
    if systemctl is-active --quiet firewalld; then
        log_warning "firewalld正在运行，可能需要开放端口"
        echo "当前防火墙规则:"
        firewall-cmd --list-all 2>/dev/null || echo "无法获取防火墙规则"
    else
        log_info "firewalld未运行"
    fi
    
    # 检查iptables
    if command -v iptables >/dev/null 2>&1; then
        IPTABLES_RULES=$(iptables -L | wc -l)
        if [ $IPTABLES_RULES -gt 10 ]; then
            log_warning "检测到iptables规则，可能影响网络访问"
        else
            log_info "iptables规则正常"
        fi
    fi
    
    # 检查SELinux
    if command -v getenforce >/dev/null 2>&1; then
        SELINUX_STATUS=$(getenforce)
        if [ "$SELINUX_STATUS" = "Enforcing" ]; then
            log_warning "SELinux处于强制模式，可能影响Docker运行"
        else
            log_info "SELinux状态: $SELINUX_STATUS"
        fi
    fi
}

# 检查已安装的软件
check_installed_software() {
    log_info "检查已安装的软件..."
    
    # 检查Docker
    if command -v docker >/dev/null 2>&1; then
        DOCKER_VERSION=$(docker --version)
        log_success "Docker已安装: $DOCKER_VERSION"
        
        # 检查Docker服务状态
        if systemctl is-active --quiet docker; then
            log_success "Docker服务正在运行"
        else
            log_warning "Docker服务未运行"
        fi
        
        # 检查Docker用户组
        if groups $USER | grep -q docker; then
            log_success "当前用户已加入docker组"
        else
            log_warning "当前用户未加入docker组"
        fi
    else
        log_warning "Docker未安装"
    fi
    
    # 检查Docker Compose
    if command -v docker-compose >/dev/null 2>&1; then
        COMPOSE_VERSION=$(docker-compose --version)
        log_success "Docker Compose已安装: $COMPOSE_VERSION"
    else
        log_warning "Docker Compose未安装"
    fi
    
    # 检查Git
    if command -v git >/dev/null 2>&1; then
        GIT_VERSION=$(git --version)
        log_success "Git已安装: $GIT_VERSION"
    else
        log_warning "Git未安装"
    fi
    
    # 检查Python
    if command -v python3 >/dev/null 2>&1; then
        PYTHON_VERSION=$(python3 --version)
        log_success "Python3已安装: $PYTHON_VERSION"
    else
        log_warning "Python3未安装"
    fi
    
    # 检查curl和wget
    if command -v curl >/dev/null 2>&1; then
        log_success "curl已安装"
    else
        log_warning "curl未安装"
    fi
    
    if command -v wget >/dev/null 2>&1; then
        log_success "wget已安装"
    else
        log_warning "wget未安装"
    fi
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用情况..."
    
    PORTS=(80 443 3306 6379 8000)
    
    for port in "${PORTS[@]}"; do
        if netstat -tuln 2>/dev/null | grep ":$port " >/dev/null; then
            log_warning "端口 $port 已被占用"
            PROCESS=$(netstat -tulnp 2>/dev/null | grep ":$port " | awk '{print $7}' | head -1)
            if [ -n "$PROCESS" ]; then
                echo "占用进程: $PROCESS"
            fi
        else
            log_success "端口 $port 可用"
        fi
    done
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."
    
    # 检查根目录空间
    ROOT_AVAILABLE=$(df / | tail -1 | awk '{print $4}')
    ROOT_AVAILABLE_GB=$((ROOT_AVAILABLE / 1024 / 1024))
    
    if [ $ROOT_AVAILABLE_GB -lt 10 ]; then
        log_error "根目录可用空间不足10GB，当前: ${ROOT_AVAILABLE_GB}GB"
    elif [ $ROOT_AVAILABLE_GB -lt 20 ]; then
        log_warning "根目录可用空间较少，当前: ${ROOT_AVAILABLE_GB}GB，建议至少20GB"
    else
        log_success "根目录可用空间充足: ${ROOT_AVAILABLE_GB}GB"
    fi
    
    # 检查/var目录空间（Docker镜像存储位置）
    if mountpoint -q /var 2>/dev/null; then
        VAR_AVAILABLE=$(df /var | tail -1 | awk '{print $4}')
        VAR_AVAILABLE_GB=$((VAR_AVAILABLE / 1024 / 1024))
        log_info "/var目录可用空间: ${VAR_AVAILABLE_GB}GB"
    fi
    
    # 检查Docker根目录空间
    if [ -d /var/lib/docker ]; then
        DOCKER_AVAILABLE=$(df /var/lib/docker | tail -1 | awk '{print $4}')
        DOCKER_AVAILABLE_GB=$((DOCKER_AVAILABLE / 1024 / 1024))
        log_info "Docker存储空间: ${DOCKER_AVAILABLE_GB}GB"
    fi
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    # 检查CPU核心数
    CPU_CORES=$(nproc)
    if [ $CPU_CORES -lt 2 ]; then
        log_warning "CPU核心数较少: ${CPU_CORES}核，建议至少2核"
    else
        log_success "CPU核心数: ${CPU_CORES}核"
    fi
    
    # 检查内存
    MEMORY_GB=$(free -g | awk 'NR==2{print $2}')
    if [ $MEMORY_GB -lt 2 ]; then
        log_warning "内存不足4GB，当前: ${MEMORY_GB}GB，可能影响性能"
    else
        log_success "内存: ${MEMORY_GB}GB"
    fi
    
    # 检查负载
    LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    log_info "系统负载: $LOAD_AVG"
}

# 生成部署建议
generate_recommendations() {
    log_info "生成OpenCloudOS 9部署建议..."
    
    echo ""
    echo "=============================================="
    echo "🎯 OpenCloudOS 9 部署建议"
    echo "=============================================="
    echo ""
    
    echo "📋 OpenCloudOS 9 部署步骤:"
    echo ""
    echo "1. 配置软件源 (可选，提升下载速度):"
    echo "   # 配置腾讯云镜像源"
    echo "   sudo dnf config-manager --add-repo http://mirrors.tencent.com/docker-ce/linux/centos/docker-ce.repo"
    echo ""
    echo "2. 安装Docker CE:"
    echo "   sudo dnf update -y"
    echo "   sudo dnf install -y dnf-plugins-core curl wget git"
    echo "   sudo dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo"
    echo "   sudo dnf install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin"
    echo ""
    echo "3. 启动Docker服务:"
    echo "   sudo systemctl start docker"
    echo "   sudo systemctl enable docker"
    echo "   sudo usermod -aG docker \$USER"
    echo ""
    echo "4. 安装Docker Compose (独立版本):"
    echo "   sudo curl -L \"https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose"
    echo "   sudo chmod +x /usr/local/bin/docker-compose"
    echo "   sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose"
    echo ""
    echo "5. 配置防火墙:"
    echo "   sudo firewall-cmd --permanent --add-port=80/tcp"
    echo "   sudo firewall-cmd --permanent --add-port=443/tcp"
    echo "   sudo firewall-cmd --permanent --add-port=8000/tcp"
    echo "   sudo firewall-cmd --permanent --zone=trusted --add-interface=docker0"
    echo "   sudo firewall-cmd --reload"
    echo ""
    echo "6. 配置SELinux (如果需要):"
    echo "   sudo setsebool -P httpd_can_network_connect 1"
    echo "   sudo setsebool -P container_manage_cgroup 1"
    echo ""
    echo "7. 配置Docker镜像加速 (可选):"
    echo "   sudo mkdir -p /etc/docker"
    echo "   sudo tee /etc/docker/daemon.json <<EOF"
    echo "   {"
    echo "     \"registry-mirrors\": ["
    echo "       \"https://mirror.ccs.tencentyun.com\","
    echo "       \"https://docker.mirrors.ustc.edu.cn\""
    echo "     ]"
    echo "   }"
    echo "   EOF"
    echo "   sudo systemctl restart docker"
    echo ""
    echo "8. 部署应用:"
    echo "   chmod +x deploy_opencloudos.sh"
    echo "   sudo ./deploy_opencloudos.sh"
    echo ""
    
    echo "🔧 OpenCloudOS 9 特殊配置:"
    echo "• 使用DNF包管理器 (yum的升级版)"
    echo "• 支持Docker Compose Plugin"
    echo "• 兼容CentOS 8/RHEL 8软件包"
    echo "• 建议使用腾讯云镜像源加速"
    echo ""
    
    echo "⚡ 性能优化建议:"
    echo "• 配置Docker镜像加速器"
    echo "• 使用SSD存储提升I/O性能"
    echo "• 配置合适的内存和CPU资源"
    echo "• 定期清理Docker缓存"
    echo ""
}

# 主函数
main() {
    echo "🐧 OpenCloudOS 9系统检测和部署准备"
    echo "========================================"
    echo ""
    
    check_system
    echo ""
    check_network
    echo ""
    check_firewall
    echo ""
    check_installed_software
    echo ""
    check_ports
    echo ""
    check_disk_space
    echo ""
    check_system_resources
    echo ""
    generate_recommendations
}

# 运行主函数
main
