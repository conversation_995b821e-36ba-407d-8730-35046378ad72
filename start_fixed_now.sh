#!/bin/bash

echo "========================================"
echo "  微博任务管理系统 - 立即启动"
echo "========================================"

cd "$(dirname "$0")"

# 停止现有服务
pkill -f "uvicorn.*11234" 2>/dev/null || true

# 检查虚拟环境
if [ ! -d "venv_ultimate" ]; then
    echo "创建虚拟环境..."
    /usr/bin/python3 -m venv venv_ultimate
fi

# 激活虚拟环境
source venv_ultimate/bin/activate

# 安装缺失的依赖
echo "检查并安装依赖..."
python -m pip install --upgrade pip >/dev/null 2>&1
python -m pip install pytz >/dev/null 2>&1

# 测试数据库连接
echo "测试数据库连接..."
python3 << 'EOF'
import sys
sys.path.append('.')

try:
    from app.db import engine, Base
    from app.models import device, task, group, device_status, group_task_status
    
    print("✅ 模块导入成功")
    
    with engine.connect() as conn:
        conn.execute("SELECT 1")
    print("✅ 数据库连接成功")
    
    Base.metadata.create_all(bind=engine)
    print("✅ 数据库表创建成功")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    exit(1)
EOF

if [ $? -eq 0 ]; then
    echo ""
    echo "========================================"
    echo "  系统启动中..."
    echo "========================================"
    echo "Python版本: $(python --version)"
    echo "数据库: SQLite"
    echo "访问地址: http://***********:11234"
    echo "API文档: http://***********:11234/docs"
    echo "健康检查: http://***********:11234/health"
    echo "========================================"
    echo ""
    
    # 启动服务
    uvicorn app.main:app --host 0.0.0.0 --port 11234 --reload
else
    echo "❌ 数据库测试失败，请检查配置"
    exit 1
fi
