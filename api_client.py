#!/usr/bin/env python3

"""
API客户端 - 用于前端与后端通信
"""

import requests
import json
from typing import Dict, List, Optional, Any
from frontend_config import config

class APIClient:
    """API客户端类"""
    
    def __init__(self):
        self.base_url = config.BASE_URL
        self.timeout = config.REQUEST_TIMEOUT
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'WB-Frontend/1.0'
        })
    
    def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            response.raise_for_status()
            
            # 尝试解析JSON响应
            try:
                return response.json()
            except json.JSONDecodeError:
                return {"message": response.text}
                
        except requests.exceptions.RequestException as e:
            return {"error": str(e), "success": False}
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """GET请求"""
        return self._request("GET", endpoint, params=params)
    
    def post(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """POST请求"""
        return self._request("POST", endpoint, json=data)
    
    def put(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """PUT请求"""
        return self._request("PUT", endpoint, json=data)
    
    def delete(self, endpoint: str) -> Dict[str, Any]:
        """DELETE请求"""
        return self._request("DELETE", endpoint)
    
    # 健康检查
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return self.get("/health")
    
    def system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return self.get("/system")
    
    # 设备管理
    def get_devices(self) -> Dict[str, Any]:
        """获取设备列表"""
        return self.get("/api/devices")
    
    def get_device(self, device_id: str) -> Dict[str, Any]:
        """获取设备详情"""
        return self.get(f"/api/devices/{device_id}")
    
    def create_device(self, device_data: Dict) -> Dict[str, Any]:
        """创建设备"""
        return self.post("/api/devices", device_data)
    
    def update_device(self, device_id: str, device_data: Dict) -> Dict[str, Any]:
        """更新设备"""
        return self.put(f"/api/devices/{device_id}", device_data)
    
    def delete_device(self, device_id: str) -> Dict[str, Any]:
        """删除设备"""
        return self.delete(f"/api/devices/{device_id}")
    
    # 任务管理
    def get_tasks(self) -> Dict[str, Any]:
        """获取任务列表"""
        return self.get("/api/tasks")
    
    def get_task(self, task_id: str) -> Dict[str, Any]:
        """获取任务详情"""
        return self.get(f"/api/tasks/{task_id}")
    
    def create_task(self, task_data: Dict) -> Dict[str, Any]:
        """创建任务"""
        return self.post("/api/tasks", task_data)
    
    def update_task(self, task_id: str, task_data: Dict) -> Dict[str, Any]:
        """更新任务"""
        return self.put(f"/api/tasks/{task_id}", task_data)
    
    def delete_task(self, task_id: str) -> Dict[str, Any]:
        """删除任务"""
        return self.delete(f"/api/tasks/{task_id}")
    
    # 分组管理
    def get_groups(self) -> Dict[str, Any]:
        """获取分组列表"""
        return self.get("/api/groups")
    
    def get_group(self, group_id: str) -> Dict[str, Any]:
        """获取分组详情"""
        return self.get(f"/api/groups/{group_id}")
    
    def create_group(self, group_data: Dict) -> Dict[str, Any]:
        """创建分组"""
        return self.post("/api/groups", group_data)
    
    def update_group(self, group_id: str, group_data: Dict) -> Dict[str, Any]:
        """更新分组"""
        return self.put(f"/api/groups/{group_id}", group_data)
    
    def delete_group(self, group_id: str) -> Dict[str, Any]:
        """删除分组"""
        return self.delete(f"/api/groups/{group_id}")

# 创建全局API客户端实例
api_client = APIClient()

def test_connection():
    """测试连接"""
    print("测试后端连接...")
    
    # 测试健康检查
    health = api_client.health_check()
    print(f"健康检查: {health}")
    
    # 测试系统信息
    system = api_client.system_info()
    print(f"系统信息: {system}")
    
    return health.get("status") == "healthy"

if __name__ == "__main__":
    # 测试API客户端
    if test_connection():
        print("✅ 后端连接成功！")
    else:
        print("❌ 后端连接失败！")
