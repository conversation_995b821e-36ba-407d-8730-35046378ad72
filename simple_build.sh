#!/bin/bash

# 简化构建脚本 - 不修改Docker配置

echo "=== 简化Docker构建 ==="

# 1. 修复Docker服务
echo "1. 修复Docker服务..."

# 如果daemon.json存在且有问题，先备份并删除
if [ -f "/etc/docker/daemon.json" ]; then
    echo "备份现有daemon.json..."
    cp /etc/docker/daemon.json /etc/docker/daemon.json.backup.$(date +%Y%m%d_%H%M%S)
    rm -f /etc/docker/daemon.json
fi

# 重启Docker服务
echo "重启Docker服务..."
systemctl stop docker.service 2>/dev/null || true
sleep 3
systemctl start docker.service
sleep 5

# 检查Docker状态
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker服务仍有问题，尝试强制修复..."
    
    # 强制停止所有Docker进程
    pkill -f dockerd 2>/dev/null || true
    pkill -f docker-containerd 2>/dev/null || true
    sleep 3
    
    # 重新启动
    systemctl start docker.service
    sleep 10
    
    if ! docker info >/dev/null 2>&1; then
        echo "❌ Docker无法启动，请手动检查"
        echo "运行以下命令查看错误："
        echo "systemctl status docker.service"
        echo "journalctl -xeu docker.service"
        exit 1
    fi
fi

echo "✅ Docker服务正常"

# 2. 清理环境
echo "2. 清理Docker环境..."
docker system prune -f >/dev/null 2>&1

# 3. 在宿主机下载依赖
echo "3. 在宿主机下载Python依赖..."

# 确保有pip3
if ! command -v pip3 >/dev/null 2>&1; then
    echo "安装pip3..."
    yum install -y python3-pip 2>/dev/null || \
    dnf install -y python3-pip 2>/dev/null || \
    echo "请手动安装python3-pip"
fi

# 创建wheels目录
mkdir -p wheels
rm -rf wheels/*

# 下载依赖包
echo "下载依赖包到本地..."
if pip3 download -d wheels -r requirements.txt \
    --index-url https://pypi.tuna.tsinghua.edu.cn/simple \
    --trusted-host pypi.tuna.tsinghua.edu.cn; then
    echo "✅ 清华源下载成功"
elif pip3 download -d wheels -r requirements.txt \
    --index-url https://mirrors.aliyun.com/pypi/simple/ \
    --trusted-host mirrors.aliyun.com; then
    echo "✅ 阿里云源下载成功"
elif pip3 download -d wheels -r requirements.txt; then
    echo "✅ 默认源下载成功"
else
    echo "❌ 依赖下载失败"
    
    # 尝试逐个下载
    echo "尝试逐个下载..."
    packages=("fastapi==0.115.12" "uvicorn==0.34.3" "sqlalchemy==2.0.41" "pymysql==1.1.1" "websockets==15.0.1" "requests==2.32.3" "pydantic==2.11.5" "python-dotenv==1.1.0" "PyYAML==6.0.2")
    
    for pkg in "${packages[@]}"; do
        echo "下载 $pkg ..."
        pip3 download -d wheels --no-deps "$pkg" \
            --index-url https://pypi.tuna.tsinghua.edu.cn/simple \
            --trusted-host pypi.tuna.tsinghua.edu.cn 2>/dev/null || \
        pip3 download -d wheels --no-deps "$pkg" 2>/dev/null || \
        echo "❌ $pkg 下载失败"
    done
fi

# 检查下载结果
if [ -d "wheels" ] && [ "$(ls -A wheels)" ]; then
    echo "✅ 依赖包准备完成"
    echo "下载的包数量: $(ls wheels/*.whl 2>/dev/null | wc -l)"
else
    echo "❌ 没有下载到依赖包"
    echo "尝试在线构建..."
fi

# 4. 创建离线Dockerfile
echo "4. 创建离线Dockerfile..."
cat > Dockerfile.simple_offline << 'EOF'
FROM python:3.9-slim

WORKDIR /app

# 复制本地wheel文件
COPY wheels/ ./wheels/

# 复制requirements文件
COPY requirements.txt .

# 安装依赖 - 优先使用本地wheel，失败则在线安装
RUN if [ -d "wheels" ] && [ "$(ls -A wheels)" ]; then \
        echo "使用本地wheel文件安装..." && \
        pip install --no-index --find-links wheels -r requirements.txt; \
    else \
        echo "在线安装依赖..." && \
        pip install --no-cache-dir -r requirements.txt; \
    fi

# 复制应用代码
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF

# 5. 构建镜像
echo "5. 构建Docker镜像..."

# 禁用BuildKit
export DOCKER_BUILDKIT=0

# 尝试构建
if docker build -f Dockerfile.simple_offline -t wb_backend:latest .; then
    echo "✅ 构建成功！"
    
    # 显示镜像信息
    echo ""
    echo "镜像信息："
    docker images wb_backend:latest
    
    echo ""
    echo "🎉 构建完成！"
    echo ""
    echo "启动服务："
    echo "  docker run --rm -p 8000:8000 wb_backend:latest"
    echo ""
    echo "或使用docker-compose："
    echo "  docker-compose up -d"
    
    # 清理临时文件
    rm -f Dockerfile.simple_offline
    
else
    echo "❌ 构建失败"
    
    # 尝试最简单的在线构建
    echo ""
    echo "尝试最简单的在线构建..."
    
    cat > Dockerfile.ultra_simple << 'EOF'
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF
    
    if docker build -f Dockerfile.ultra_simple --network=host -t wb_backend:latest .; then
        echo "✅ 简单在线构建成功！"
        rm -f Dockerfile.ultra_simple Dockerfile.simple_offline
    else
        echo "❌ 所有构建方法都失败了"
        echo ""
        echo "请检查："
        echo "1. 网络连接: ping *******"
        echo "2. DNS解析: nslookup pypi.org"
        echo "3. Docker状态: docker info"
        
        rm -f Dockerfile.ultra_simple Dockerfile.simple_offline
        exit 1
    fi
fi

echo ""
echo "=== 构建完成 ==="
