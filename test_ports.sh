#!/bin/bash

# 测试不同端口的可用性

GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "========================================"
echo "  测试端口可用性"
echo "========================================"

# 测试端口列表
PORTS=(8080 9000 3000 5000 8888 7000 6000)

for PORT in "${PORTS[@]}"; do
    echo ""
    echo "测试端口 $PORT..."
    
    # 检查端口是否被占用
    if netstat -tlnp | grep -q ":$PORT "; then
        log_error "端口 $PORT 已被占用"
        continue
    fi
    
    # 启动测试服务
    cd "$(dirname "$0")"
    source venv/bin/activate
    
    export MYSQL_USER=root
    export MYSQL_PASSWORD=123456
    export MYSQL_HOST=127.0.0.1
    export MYSQL_PORT=3306
    export MYSQL_DB=wb
    
    echo "在端口 $PORT 启动服务..."
    uvicorn app.main:app --host 0.0.0.0 --port $PORT &
    PID=$!
    
    # 等待服务启动
    sleep 10
    
    # 测试访问
    if curl -s --connect-timeout 5 http://***********:$PORT/health >/dev/null; then
        log_success "端口 $PORT 可以正常访问！"
        echo "访问地址: http://***********:$PORT"
        echo "API文档: http://***********:$PORT/docs"
        
        # 杀死测试进程
        kill $PID 2>/dev/null
        
        # 创建该端口的启动脚本
        cat > start_backend_$PORT.sh << EOF
#!/bin/bash

cd "\$(dirname "\$0")"

if ! docker ps | grep -q wb_mysql; then
    echo "启动数据库容器..."
    docker-compose -f docker-compose-db.yml up -d
    echo "等待数据库启动..."
    sleep 10
fi

source venv/bin/activate

export MYSQL_USER=root
export MYSQL_PASSWORD=123456
export MYSQL_HOST=127.0.0.1
export MYSQL_PORT=3306
export MYSQL_DB=wb

echo "========================================"
echo "  微博任务管理系统启动"
echo "========================================"
echo "数据库: Docker MySQL容器"
echo "后端: 原生Python应用"
echo "访问地址: http://***********:$PORT"
echo "API文档: http://***********:$PORT/docs"
echo "========================================"
echo ""

uvicorn app.main:app --host 0.0.0.0 --port $PORT --reload
EOF
        
        chmod +x start_backend_$PORT.sh
        
        echo ""
        echo "🎉 找到可用端口！"
        echo "启动命令: ./start_backend_$PORT.sh"
        echo "前端配置: SERVER_URL = \"http://***********:$PORT\""
        break
    else
        log_error "端口 $PORT 无法访问"
        kill $PID 2>/dev/null
    fi
done

echo ""
echo "========================================"
echo "  端口测试完成"
echo "========================================"
