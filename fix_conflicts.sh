#!/bin/bash

# 修复端口冲突和服务问题

echo "=== 修复服务冲突和初始化问题 ==="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    log_error "请使用root权限运行此脚本"
    exit 1
fi

# 1. 停止Docker容器（释放端口）
log_info "1. 停止Docker容器释放端口..."
docker-compose down 2>/dev/null || true
docker stop $(docker ps -q) 2>/dev/null || true

# 等待端口释放
sleep 5

# 检查端口是否释放
log_info "检查端口状态..."
if netstat -tlnp | grep ":3306 " >/dev/null 2>&1; then
    log_warn "3306端口仍被占用:"
    netstat -tlnp | grep ":3306 "
    log_info "强制杀死占用进程..."
    pkill -f docker-proxy || true
    sleep 3
fi

if netstat -tlnp | grep ":6379 " >/dev/null 2>&1; then
    log_warn "6379端口仍被占用:"
    netstat -tlnp | grep ":6379 "
    log_info "强制杀死占用进程..."
    pkill -f docker-proxy || true
    sleep 3
fi

# 2. 初始化MySQL数据目录
log_info "2. 初始化MySQL数据目录..."

# 创建MySQL数据目录
if [ ! -d "/var/lib/mysql" ]; then
    log_info "创建MySQL数据目录..."
    mkdir -p /var/lib/mysql
    chown mysql:mysql /var/lib/mysql
    chmod 755 /var/lib/mysql
fi

# 初始化MySQL数据库
if [ ! -f "/var/lib/mysql/mysql/user.frm" ] && [ ! -f "/var/lib/mysql/mysql/user.MYD" ]; then
    log_info "初始化MySQL数据库..."
    
    # 尝试使用mysql_install_db
    if command -v mysql_install_db >/dev/null 2>&1; then
        mysql_install_db --user=mysql --datadir=/var/lib/mysql --force
    elif command -v mysqld >/dev/null 2>&1; then
        # 使用mysqld初始化
        mysqld --initialize-insecure --user=mysql --datadir=/var/lib/mysql
    else
        log_error "无法找到MySQL初始化命令"
    fi
    
    if [ $? -eq 0 ]; then
        log_info "MySQL数据库初始化成功"
    else
        log_error "MySQL数据库初始化失败"
    fi
fi

# 3. 修复Redis配置
log_info "3. 修复Redis配置..."

# 备份原配置
if [ -f "/etc/redis/redis.conf" ]; then
    cp /etc/redis/redis.conf /etc/redis/redis.conf.backup
fi

# 创建简化的Redis配置
cat > /etc/redis/redis.conf << 'EOF'
# Redis配置文件 - 简化版
bind 127.0.0.1
port 6379
timeout 0
tcp-keepalive 300
daemonize no
supervised systemd
pidfile /var/run/redis/redis-server.pid
loglevel notice
logfile /var/log/redis/redis-server.log
databases 16
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /var/lib/redis
maxmemory-policy noeviction
appendonly no
EOF

# 确保Redis目录权限正确
chown redis:redis /var/lib/redis
chmod 755 /var/lib/redis

# 创建Redis日志目录
mkdir -p /var/log/redis
chown redis:redis /var/log/redis

# 创建Redis运行目录
mkdir -p /var/run/redis
chown redis:redis /var/run/redis

# 4. 启动MySQL服务
log_info "4. 启动MySQL服务..."
systemctl start mysqld

# 等待MySQL启动
sleep 10

if systemctl is-active mysqld >/dev/null 2>&1; then
    log_info "✅ MySQL启动成功"
    
    # 测试连接
    if mysql -u root -e "SELECT 1;" >/dev/null 2>&1; then
        log_info "✅ MySQL连接测试成功"
        
        # 创建数据库和用户
        log_info "创建应用数据库和用户..."
        mysql -u root << 'EOF'
CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'wb_user'@'localhost' IDENTIFIED BY 'wb_password';
GRANT ALL PRIVILEGES ON wb.* TO 'wb_user'@'localhost';
FLUSH PRIVILEGES;
EOF
        
        if [ $? -eq 0 ]; then
            log_info "✅ 数据库和用户创建成功"
        fi
    else
        log_warn "MySQL连接需要密码，请稍后手动配置"
    fi
else
    log_error "❌ MySQL启动失败"
    systemctl status mysqld
fi

# 5. 启动Redis服务
log_info "5. 启动Redis服务..."
systemctl start redis

# 等待Redis启动
sleep 5

if systemctl is-active redis >/dev/null 2>&1; then
    log_info "✅ Redis启动成功"
    
    # 测试连接
    if redis-cli ping >/dev/null 2>&1; then
        log_info "✅ Redis连接测试成功"
    fi
else
    log_error "❌ Redis启动失败"
    systemctl status redis
fi

# 6. 显示最终状态
echo ""
log_info "=== 修复完成 ==="
echo ""
log_info "服务状态:"
echo "  - MySQL: $(systemctl is-active mysqld)"
echo "  - Redis: $(systemctl is-active redis)"

echo ""
log_info "端口状态:"
netstat -tlnp | grep -E ":3306|:6379" || echo "  端口未被占用"

echo ""
log_info "数据库信息:"
echo "  - 数据库名: wb"
echo "  - 用户名: wb_user"
echo "  - 密码: wb_password"
echo "  - 连接: mysql+pymysql://wb_user:wb_password@localhost:3306/wb"

echo ""
log_info "下一步:"
echo "1. 启动应用: cd /opt/wb_system && ./quick_start.sh"
echo "2. 或使用系统服务: systemctl start wb-system"
echo "3. 访问: http://***********:8000"
