#!/bin/bash

# 无数据库启动脚本 - 先让应用跑起来

echo "=== 启动应用（无数据库模式）==="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 检查应用目录
APP_DIR="/opt/wb_system"
if [ ! -d "$APP_DIR" ]; then
    log_warn "应用目录不存在，在当前目录创建..."
    APP_DIR=$(pwd)
fi

cd $APP_DIR

# 检查虚拟环境
if [ ! -d "venv" ]; then
    log_info "创建Python虚拟环境..."
    python3 -m venv venv
    source venv/bin/activate
    pip install fastapi uvicorn
else
    source venv/bin/activate
fi

# 创建简化的应用（不依赖数据库）
log_info "创建简化应用..."
mkdir -p app

cat > app/main.py << 'EOF'
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
from datetime import datetime
import socket
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="微博任务管理系统",
    description="微博任务管理系统API - 简化版",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

@app.get("/")
async def root():
    return {
        "message": "微博任务管理系统",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "deployment": "simplified",
        "note": "数据库功能暂未启用"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "service": "wb_backend",
        "deployment": "simplified",
        "server_ip": get_local_ip(),
        "database": "not_connected"
    }

@app.get("/system")
async def system_info():
    return {
        "hostname": socket.gethostname(),
        "local_ip": get_local_ip(),
        "python_version": os.sys.version,
        "working_directory": os.getcwd(),
        "environment": {
            "DATABASE_URL": "not_configured",
            "REDIS_URL": "not_configured"
        },
        "services": {
            "api": "running",
            "database": "not_connected",
            "redis": "not_connected"
        }
    }

@app.get("/test")
async def test_endpoint():
    return {
        "message": "测试端点",
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "data": {
            "test_1": "API正常工作",
            "test_2": "CORS已配置",
            "test_3": "服务器响应正常"
        }
    }

# 模拟一些API端点
@app.get("/api/devices")
async def get_devices():
    return {
        "message": "设备列表API",
        "devices": [],
        "note": "数据库未连接，返回空列表"
    }

@app.get("/api/tasks")
async def get_tasks():
    return {
        "message": "任务列表API", 
        "tasks": [],
        "note": "数据库未连接，返回空列表"
    }

@app.get("/api/groups")
async def get_groups():
    return {
        "message": "分组列表API",
        "groups": [],
        "note": "数据库未连接，返回空列表"
    }

@app.on_event("startup")
async def startup_event():
    logger.info("微博任务管理系统启动成功 (简化模式)")
    logger.info(f"服务运行在: {get_local_ip()}:8000")
    logger.info("注意: 数据库功能暂未启用")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

# 检查端口占用
if netstat -tlnp | grep :8000 >/dev/null 2>&1; then
    log_warn "端口8000已被占用，停止现有进程..."
    pkill -f "uvicorn.*8000" || true
    pkill -f "python.*8000" || true
    sleep 2
fi

# 启动应用
log_info "启动应用..."
echo ""
echo "🚀 正在启动微博任务管理系统（简化版）..."
echo "📍 应用目录: $APP_DIR"
echo "🌐 访问地址: http://$(curl -s ifconfig.me 2>/dev/null || echo 'localhost'):8000"
echo ""
echo "📋 可用的API端点:"
echo "  - 主页: /"
echo "  - 健康检查: /health"
echo "  - 系统信息: /system"
echo "  - 测试端点: /test"
echo "  - API文档: /docs"
echo "  - 设备API: /api/devices"
echo "  - 任务API: /api/tasks"
echo "  - 分组API: /api/groups"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 启动uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
