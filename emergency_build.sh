#!/bin/bash

# 紧急构建脚本 - 最简单的方式

echo "=== 紧急Docker构建 ==="

# 清理环境
echo "清理Docker环境..."
docker system prune -f >/dev/null 2>&1

# 禁用BuildKit
export DOCKER_BUILDKIT=0

echo "尝试方法1: 最简化Dockerfile..."
if docker build -f Dockerfile.minimal --no-cache -t wb_backend:latest .; then
    echo "✅ 成功！使用最简化Dockerfile构建完成"
    exit 0
fi

echo "尝试方法2: 简化版Dockerfile..."
if docker build -f Dockerfile.simple --no-cache -t wb_backend:latest .; then
    echo "✅ 成功！使用简化版Dockerfile构建完成"
    exit 0
fi

echo "尝试方法3: 原始Dockerfile但禁用网络优化..."
# 创建临时Dockerfile
cat > Dockerfile.temp << 'EOF'
FROM python:3.9-slim
WORKDIR /app
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
RUN apt-get update && apt-get install -y gcc default-libmysqlclient-dev pkg-config curl && rm -rf /var/lib/apt/lists/*
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF

if docker build -f Dockerfile.temp --no-cache -t wb_backend:latest .; then
    echo "✅ 成功！使用临时Dockerfile构建完成"
    rm -f Dockerfile.temp
    exit 0
fi

echo "❌ 所有方法都失败了"
echo "请检查："
echo "1. 网络连接: ping *******"
echo "2. Docker状态: docker info"
echo "3. 磁盘空间: df -h"
echo "4. 内存使用: free -h"

rm -f Dockerfile.temp
exit 1
