#!/bin/bash

# 使用现有镜像，直接运行简单应用

echo "=== 使用现有镜像运行简单应用 ==="

# 1. 停止所有容器
echo "1. 停止现有容器..."
docker-compose down

# 2. 直接运行一个简单的Python容器
echo "2. 运行简单的Python应用..."

# 创建简单的应用文件
cat > simple_app.py << 'EOF'
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import socket
from datetime import datetime

app = FastAPI(
    title="微博任务管理系统",
    description="微博任务管理系统API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

@app.get("/")
async def root():
    return {
        "message": "微博任务管理系统",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "service": "wb_backend"
    }

@app.get("/ip")
async def show_ip_info():
    return {
        "hostname": socket.gethostname(),
        "local_ip": get_local_ip(),
        "message": f"服务运行在: {get_local_ip()}:8000"
    }

@app.get("/test")
async def test_endpoint():
    return {
        "message": "测试端点",
        "status": "ok",
        "timestamp": datetime.now().isoformat()
    }

if __name__ == "__main__":
    print("启动微博任务管理系统...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

# 3. 使用Python镜像直接运行
echo "3. 启动简单应用容器..."

# 停止现有的后端容器
docker stop wb_backend 2>/dev/null || true
docker rm wb_backend 2>/dev/null || true

# 运行新的简单应用容器
docker run -d \
    --name wb_backend_simple \
    --network fastapiproject_wb_network \
    -p 8000:8000 \
    -v $(pwd):/app \
    -w /app \
    python:3.9-slim \
    bash -c "pip install fastapi uvicorn && python simple_app.py"

# 4. 等待容器启动
echo "4. 等待容器启动..."
sleep 15

# 5. 检查容器状态
echo "5. 检查容器状态..."
docker ps | grep wb_backend_simple

echo ""
echo "容器日志:"
docker logs wb_backend_simple --tail 10

# 6. 测试服务
echo ""
echo "6. 测试服务..."
for i in {1..10}; do
    if curl -s http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ 服务启动成功！"
        echo ""
        echo "健康检查响应:"
        curl -s http://localhost:8000/health
        break
    fi
    echo "等待服务启动... ($i/10)"
    sleep 3
done

echo ""
echo "=== 简单应用启动完成 ==="
echo ""
echo "现在可以访问:"
echo "  - 根路径: http://***********:8000/"
echo "  - 健康检查: http://***********:8000/health"
echo "  - IP信息: http://***********:8000/ip"
echo "  - 测试端点: http://***********:8000/test"
echo "  - API文档: http://***********:8000/docs"
echo ""
echo "管理命令:"
echo "  - 查看日志: docker logs wb_backend_simple"
echo "  - 停止服务: docker stop wb_backend_simple"
echo "  - 重启服务: docker restart wb_backend_simple"
