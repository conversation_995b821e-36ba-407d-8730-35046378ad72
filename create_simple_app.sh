#!/bin/bash

# 创建简单的应用版本

echo "=== 创建简单应用版本 ==="

# 1. 备份原文件
echo "1. 备份原文件..."
cp app/main.py app/main.py.original

# 2. 创建简单版本的main.py
echo "2. 创建简单版本..."
cat > app/main.py << 'EOF'
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
from datetime import datetime
import socket

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="微博任务管理系统",
    description="微博任务管理系统API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_local_ip():
    """获取本地IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "微博任务管理系统",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "service": "wb_backend",
        "database": "not_connected_yet"
    }

@app.get("/ip")
async def show_ip_info():
    """显示服务器IP信息"""
    return {
        "hostname": socket.gethostname(),
        "local_ip": get_local_ip(),
        "container_ip": get_local_ip(),
        "message": f"服务运行在: {get_local_ip()}:8000"
    }

@app.get("/info")
async def system_info():
    """系统信息"""
    import os
    return {
        "environment": {
            "DATABASE_URL": os.getenv("DATABASE_URL", "not_set"),
            "MYSQL_HOST": os.getenv("MYSQL_HOST", "not_set"),
            "REDIS_URL": os.getenv("REDIS_URL", "not_set"),
            "DEBUG": os.getenv("DEBUG", "false")
        },
        "system": {
            "hostname": socket.gethostname(),
            "local_ip": get_local_ip()
        }
    }

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("微博任务管理系统启动成功")
    logger.info(f"服务运行在: {get_local_ip()}:8000")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

# 3. 重新构建镜像
echo "3. 重新构建镜像..."
export DOCKER_BUILDKIT=0
docker build -t wb_backend:latest .

# 4. 重新启动服务
echo "4. 重新启动服务..."
docker-compose stop backend
docker-compose up -d backend

# 5. 等待并检查
echo "5. 等待服务启动..."
sleep 10

docker-compose ps
echo ""
echo "容器日志:"
docker logs wb_backend --tail 10

# 6. 测试服务
echo ""
echo "6. 测试服务..."
for i in {1..10}; do
    if curl -s http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ 服务启动成功！"
        break
    fi
    echo "等待服务启动... ($i/10)"
    sleep 2
done

echo ""
echo "=== 简单应用创建完成 ==="
echo ""
echo "现在可以访问:"
echo "  - 根路径: http://***********:8000/"
echo "  - 健康检查: http://***********:8000/health"
echo "  - IP信息: http://***********:8000/ip"
echo "  - 系统信息: http://***********:8000/info"
echo "  - API文档: http://***********:8000/docs"
