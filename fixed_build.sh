#!/bin/bash

# 修复的Docker构建脚本

echo "=== 修复的Docker构建 ==="

# 1. 检查Docker状态
echo "1. 检查Docker状态..."
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker服务异常"
    exit 1
fi
echo "✅ Docker服务正常"

# 2. 清理环境
echo "2. 清理Docker环境..."
docker system prune -f >/dev/null 2>&1

# 3. 检查wheels目录
echo "3. 检查wheels目录..."
if [ -d "wheels" ]; then
    wheel_count=$(ls wheels/*.whl 2>/dev/null | wc -l)
    echo "✅ wheels目录存在，包含 $wheel_count 个包"
    
    # 显示wheels目录内容
    echo "wheels目录内容:"
    ls -la wheels/ | head -10
    
    # 创建离线Dockerfile
    echo "4. 创建离线Dockerfile..."
    cat > Dockerfile.fixed_offline << 'EOF'
FROM python:3.9-slim

WORKDIR /app

# 复制本地wheel文件
COPY wheels/ ./wheels/

# 复制requirements文件
COPY requirements.txt .

# 从本地wheel文件安装依赖
RUN pip install --no-index --find-links wheels -r requirements.txt

# 复制应用代码
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF

    echo "5. 使用离线方式构建..."
    export DOCKER_BUILDKIT=0
    
    if docker build -f Dockerfile.fixed_offline -t wb_backend:latest .; then
        echo "✅ 离线构建成功！"
        
        # 显示镜像信息
        echo ""
        echo "镜像信息："
        docker images wb_backend:latest
        
        echo ""
        echo "🎉 构建完成！"
        echo ""
        echo "启动服务："
        echo "  docker run --rm -p 8000:8000 wb_backend:latest"
        echo ""
        echo "或使用docker-compose："
        echo "  docker-compose up -d"
        
        # 清理临时文件
        rm -f Dockerfile.fixed_offline
        exit 0
    else
        echo "❌ 离线构建失败"
    fi
else
    echo "❌ wheels目录不存在"
fi

# 4. 重新下载依赖
echo "4. 重新下载依赖..."
mkdir -p wheels
cd wheels

# 手动下载每个包
packages=(
    "fastapi==0.115.12"
    "uvicorn==0.34.3" 
    "sqlalchemy==2.0.41"
    "pymysql==1.1.1"
    "websockets==15.0.1"
    "requests==2.32.3"
    "pydantic==2.11.5"
    "python-dotenv==1.1.0"
    "PyYAML==6.0.2"
)

echo "下载核心包..."
for pkg in "${packages[@]}"; do
    echo "下载 $pkg ..."
    pip3 download --no-deps "$pkg" \
        --index-url https://pypi.tuna.tsinghua.edu.cn/simple \
        --trusted-host pypi.tuna.tsinghua.edu.cn 2>/dev/null || \
    pip3 download --no-deps "$pkg" 2>/dev/null || \
    echo "❌ $pkg 下载失败"
done

# 下载依赖包
echo "下载依赖包..."
pip3 download --no-deps \
    "starlette" "typing-extensions" "click" "h11" "greenlet" \
    "charset-normalizer" "idna" "urllib3" "certifi" \
    "annotated-types" "pydantic-core" "anyio" "sniffio" \
    --index-url https://pypi.tuna.tsinghua.edu.cn/simple \
    --trusted-host pypi.tuna.tsinghua.edu.cn 2>/dev/null || true

cd ..

# 5. 检查下载结果
if [ -d "wheels" ] && [ "$(ls -A wheels)" ]; then
    wheel_count=$(ls wheels/*.whl 2>/dev/null | wc -l)
    echo "✅ 重新下载完成，共 $wheel_count 个包"
    
    # 创建离线Dockerfile
    cat > Dockerfile.fixed_offline << 'EOF'
FROM python:3.9-slim

WORKDIR /app

# 复制本地wheel文件
COPY wheels/ ./wheels/

# 复制requirements文件  
COPY requirements.txt .

# 从本地wheel文件安装依赖
RUN pip install --no-index --find-links wheels -r requirements.txt

# 复制应用代码
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF

    echo "6. 使用离线方式构建..."
    if docker build -f Dockerfile.fixed_offline -t wb_backend:latest .; then
        echo "✅ 离线构建成功！"
        rm -f Dockerfile.fixed_offline
        exit 0
    fi
fi

# 6. 最后尝试在线构建
echo "6. 尝试在线构建..."
cat > Dockerfile.online_simple << 'EOF'
FROM python:3.9-slim

WORKDIR /app

# 复制requirements文件
COPY requirements.txt .

# 在线安装依赖
RUN pip install --no-cache-dir \
    --index-url https://pypi.tuna.tsinghua.edu.cn/simple \
    --trusted-host pypi.tuna.tsinghua.edu.cn \
    -r requirements.txt

# 复制应用代码
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF

if docker build -f Dockerfile.online_simple -t wb_backend:latest .; then
    echo "✅ 在线构建成功！"
    
    # 显示镜像信息
    echo ""
    echo "镜像信息："
    docker images wb_backend:latest
    
    echo ""
    echo "🎉 构建完成！"
    
    rm -f Dockerfile.online_simple Dockerfile.fixed_offline
else
    echo "❌ 在线构建也失败了"
    
    echo ""
    echo "请检查："
    echo "1. 网络连接: ping *******"
    echo "2. DNS解析: nslookup pypi.org"
    echo "3. Docker状态: docker info"
    
    rm -f Dockerfile.online_simple Dockerfile.fixed_offline
    exit 1
fi

echo ""
echo "=== 构建完成 ==="
