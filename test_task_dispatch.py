#!/usr/bin/env python3
"""
测试任务分发
"""

import requests
import json
import time

def test_task_dispatch():
    """测试任务分发"""
    print("🧪 测试任务分发")
    print("="*50)
    
    try:
        # 1. 获取在线设备
        print("📱 获取在线设备...")
        devices_response = requests.get('http://localhost:8000/devices/')
        if devices_response.status_code != 200:
            print(f"❌ 获取设备失败: {devices_response.text}")
            return
            
        devices = devices_response.json()
        online_devices = [d for d in devices if d.get('online_status') == 'online']
        print(f"✅ 在线设备数量: {len(online_devices)}")
        
        for device in online_devices:
            device_id = device['id']
            device_number = device['device_number']
            print(f"   🟢 设备{device_id}: {device_number}")
        
        if not online_devices:
            print("❌ 没有在线设备")
            return
            
        # 2. 选择第一个在线设备创建任务
        device = online_devices[0]
        device_id = device['id']
        device_number = device['device_number']
        print(f"\n🎯 选择设备: {device_number} (ID: {device_id})")
        
        # 3. 创建任务
        print("🚀 创建测试任务...")
        task_data = {
            'task_type': 'like',
            'target_scope': 'single',
            'target_id': device_id,
            'parameters': {
                'blogger_id': 'test_blogger_123',
                'like_id': 'test_like_456',
                'delay_click': 1000
            },
            'delay_group': 2000,
            'delay_like': 1000
        }
        
        response = requests.post('http://localhost:8000/tasks/', json=task_data)
        print(f"📊 任务创建状态码: {response.status_code}")
        
        if response.status_code == 200:
            task = response.json()
            task_id = task.get('id')
            print(f"✅ 任务创建成功: ID={task_id}")
            print(f"📋 任务类型: {task.get('task_type')}")
            print(f"🎯 目标类型: {task.get('target_type')}")
            print(f"⏱️ 创建时间: {task.get('create_time')}")
            
            # 4. 等待任务执行
            print(f"\n⏳ 等待任务执行...")
            for i in range(10):
                time.sleep(1)
                print(f"   等待中... {i+1}/10秒")
                
                # 检查任务状态
                task_response = requests.get(f'http://localhost:8000/tasks/{task_id}')
                if task_response.status_code == 200:
                    updated_task = task_response.json()
                    status = updated_task.get('status')
                    print(f"   📊 任务状态: {status}")
                    
                    if status in ['completed', 'failed']:
                        print(f"✅ 任务执行完成: {status}")
                        break
            else:
                print("⏰ 等待超时")
                
        else:
            print(f"❌ 任务创建失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_task_dispatch()
