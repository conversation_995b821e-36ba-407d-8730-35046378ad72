#!/bin/bash

# OpenCloudOS 9 原生部署脚本

echo "=== 微博任务管理系统 - 原生部署 ==="
echo "系统: $(cat /etc/os-release | grep PRETTY_NAME)"
echo "时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    log_error "请使用root权限运行此脚本"
    echo "sudo $0"
    exit 1
fi

# 1. 系统更新和基础软件安装
log_info "1. 更新系统和安装基础软件..."
dnf update -y
dnf install -y wget curl vim git unzip

# 2. 安装Python 3.9+
log_info "2. 安装Python环境..."
dnf install -y python3 python3-pip python3-devel
python3 --version

# 升级pip
python3 -m pip install --upgrade pip

# 3. 安装MySQL 8.0
log_info "3. 安装MySQL数据库..."
if ! command -v mysql >/dev/null 2>&1; then
    dnf install -y mysql-server mysql
    systemctl enable mysqld
    systemctl start mysqld
    
    # 获取临时密码
    TEMP_PASSWORD=$(grep 'temporary password' /var/log/mysqld.log | awk '{print $NF}' | tail -1)
    
    if [ -n "$TEMP_PASSWORD" ]; then
        log_info "MySQL临时密码: $TEMP_PASSWORD"
        log_warn "请记录此密码，稍后需要用于初始化"
    fi
else
    log_info "MySQL已安装"
    systemctl start mysqld
fi

# 4. 安装Redis
log_info "4. 安装Redis..."
if ! command -v redis-server >/dev/null 2>&1; then
    dnf install -y redis
    systemctl enable redis
    systemctl start redis
else
    log_info "Redis已安装"
    systemctl start redis
fi

# 5. 安装Nginx
log_info "5. 安装Nginx..."
if ! command -v nginx >/dev/null 2>&1; then
    dnf install -y nginx
    systemctl enable nginx
else
    log_info "Nginx已安装"
fi

# 6. 创建应用目录
log_info "6. 创建应用目录..."
APP_DIR="/opt/wb_system"
mkdir -p $APP_DIR
cd $APP_DIR

# 7. 创建Python虚拟环境
log_info "7. 创建Python虚拟环境..."
python3 -m venv venv
source venv/bin/activate

# 8. 安装Python依赖
log_info "8. 安装Python依赖..."
cat > requirements.txt << 'EOF'
fastapi==0.115.12
uvicorn==0.34.3
sqlalchemy==2.0.41
pymysql==1.1.1
websockets==15.0.1
requests==2.32.3
pydantic==2.11.5
python-dotenv==1.1.0
PyYAML==6.0.2
pytz==2024.2
alembic==1.13.3
EOF

pip install -r requirements.txt

# 9. 创建基础应用
log_info "9. 创建基础应用..."
mkdir -p app

cat > app/main.py << 'EOF'
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
from datetime import datetime
import socket
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="微博任务管理系统",
    description="微博任务管理系统API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

@app.get("/")
async def root():
    return {
        "message": "微博任务管理系统",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "deployment": "native"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "service": "wb_backend",
        "deployment": "native",
        "server_ip": get_local_ip()
    }

@app.get("/system")
async def system_info():
    return {
        "hostname": socket.gethostname(),
        "local_ip": get_local_ip(),
        "python_version": os.sys.version,
        "environment": {
            "DATABASE_URL": os.getenv("DATABASE_URL", "not_configured"),
            "REDIS_URL": os.getenv("REDIS_URL", "not_configured")
        }
    }

@app.on_event("startup")
async def startup_event():
    logger.info("微博任务管理系统启动成功 (原生部署)")
    logger.info(f"服务运行在: {get_local_ip()}:8000")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

# 10. 创建环境配置文件
log_info "10. 创建环境配置..."
cat > .env << 'EOF'
# 数据库配置
DATABASE_URL=mysql+pymysql://wb_user:wb_password@localhost:3306/wb
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=wb_user
MYSQL_PASSWORD=wb_password
MYSQL_DATABASE=wb

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 应用配置
DEBUG=false
LOG_LEVEL=info
SECRET_KEY=your-secret-key-change-in-production
EOF

# 11. 创建启动脚本
log_info "11. 创建启动脚本..."
cat > start.sh << 'EOF'
#!/bin/bash
cd /opt/wb_system
source venv/bin/activate
export $(cat .env | xargs)
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
EOF

chmod +x start.sh

# 12. 创建systemd服务
log_info "12. 创建系统服务..."
cat > /etc/systemd/system/wb-system.service << 'EOF'
[Unit]
Description=微博任务管理系统
After=network.target mysql.service redis.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/wb_system
Environment=PATH=/opt/wb_system/venv/bin
EnvironmentFile=/opt/wb_system/.env
ExecStart=/opt/wb_system/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable wb-system

# 13. 配置防火墙
log_info "13. 配置防火墙..."
if systemctl is-active firewalld >/dev/null 2>&1; then
    firewall-cmd --add-port=8000/tcp --permanent
    firewall-cmd --add-port=80/tcp --permanent
    firewall-cmd --add-port=443/tcp --permanent
    firewall-cmd --reload
    log_info "防火墙端口已开放"
fi

# 14. 显示部署结果
echo ""
echo "=== 部署完成 ==="
echo ""
log_info "服务状态:"
echo "  - MySQL: $(systemctl is-active mysqld)"
echo "  - Redis: $(systemctl is-active redis)"
echo "  - Nginx: $(systemctl is-active nginx)"
echo ""
log_info "应用目录: $APP_DIR"
log_info "配置文件: $APP_DIR/.env"
log_info "启动脚本: $APP_DIR/start.sh"
echo ""
log_info "下一步操作:"
echo "1. 配置MySQL数据库:"
echo "   mysql_secure_installation"
echo "   mysql -u root -p"
echo "   CREATE DATABASE wb;"
echo "   CREATE USER 'wb_user'@'localhost' IDENTIFIED BY 'wb_password';"
echo "   GRANT ALL PRIVILEGES ON wb.* TO 'wb_user'@'localhost';"
echo ""
echo "2. 启动应用:"
echo "   cd $APP_DIR"
echo "   ./start.sh"
echo "   # 或使用系统服务:"
echo "   systemctl start wb-system"
echo ""
echo "3. 访问应用:"
echo "   http://$(curl -s ifconfig.me):8000"
echo "   http://$(curl -s ifconfig.me):8000/health"
echo "   http://$(curl -s ifconfig.me):8000/docs"
echo ""
log_warn "请记得修改 .env 文件中的数据库密码和密钥！"
