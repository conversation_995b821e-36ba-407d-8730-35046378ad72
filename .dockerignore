# Git相关
.git
.gitignore
.gitattributes

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# Docker相关
Dockerfile
docker-compose.yml
docker-compose.*.yml
.dockerignore

# 部署脚本
deploy.sh
deploy.bat

# 文档
README.md
docs/

# 测试文件
test_*.py
*_test.py
tests/

# 前端文件
frontend/
node_modules/

# 配置文件
.env.example
.env.local
.env.development
.env.production

# 备份文件
*.bak
*.backup

# 上传文件
uploads/

# SSL证书
*.pem
*.key
*.crt

# Nginx配置
nginx/

# MySQL初始化脚本
mysql/
