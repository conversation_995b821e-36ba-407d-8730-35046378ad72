#!/bin/bash

# Docker部署脚本
# 用于快速部署微博设备管理系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs/nginx
    mkdir -p mysql/init
    mkdir -p nginx/ssl
    
    log_success "目录创建完成"
}

# 复制环境配置文件
setup_env() {
    log_info "设置环境配置..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            log_warning "已复制.env.example到.env，请根据需要修改配置"
        else
            log_error ".env.example文件不存在"
            exit 1
        fi
    else
        log_info ".env文件已存在，跳过复制"
    fi
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    docker-compose build --no-cache
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 先启动数据库
    log_info "启动数据库服务..."
    docker-compose up -d mysql redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 30
    
    # 启动后端服务
    log_info "启动后端服务..."
    docker-compose up -d backend
    
    # 等待后端启动
    log_info "等待后端服务启动..."
    sleep 20
    
    # 启动Nginx
    log_info "启动Nginx服务..."
    docker-compose up -d nginx
    
    log_success "所有服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    echo ""
    docker-compose ps
    echo ""
    
    # 检查健康状态
    log_info "检查服务健康状态..."
    
    # 等待服务完全启动
    sleep 10
    
    # 检查后端健康状态
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "后端服务健康检查通过"
    else
        log_warning "后端服务健康检查失败，请检查日志"
    fi
    
    # 检查Nginx
    if curl -f http://localhost/health > /dev/null 2>&1; then
        log_success "Nginx代理健康检查通过"
    else
        log_warning "Nginx代理健康检查失败，请检查配置"
    fi
}

# 显示访问信息
show_access_info() {
    log_success "部署完成！"
    echo ""
    echo "==================================="
    echo "🎉 微博设备管理系统部署成功！"
    echo "==================================="
    echo ""
    echo "📱 访问地址："
    echo "  - 主服务: http://localhost"
    echo "  - API文档: http://localhost/docs"
    echo "  - 后端直连: http://localhost:8000"
    echo "  - 健康检查: http://localhost/health"
    echo ""
    echo "🗄️ 数据库信息："
    echo "  - MySQL: localhost:3306"
    echo "  - 用户名: wb_user"
    echo "  - 密码: wb_password"
    echo "  - 数据库: wb"
    echo ""
    echo "📊 管理命令："
    echo "  - 查看日志: docker-compose logs -f"
    echo "  - 停止服务: docker-compose down"
    echo "  - 重启服务: docker-compose restart"
    echo "  - 查看状态: docker-compose ps"
    echo ""
    echo "⚙️ 前端配置："
    echo "  请在前端设置中配置服务器地址为："
    echo "  - 本地访问: localhost:8000"
    echo "  - 局域网访问: <你的IP地址>:8000"
    echo ""
}

# 主函数
main() {
    echo "🚀 开始部署微博设备管理系统..."
    echo ""
    
    check_docker
    create_directories
    setup_env
    build_images
    start_services
    check_services
    show_access_info
}

# 处理命令行参数
case "${1:-}" in
    "build")
        log_info "仅构建镜像..."
        check_docker
        build_images
        ;;
    "start")
        log_info "启动服务..."
        start_services
        ;;
    "stop")
        log_info "停止服务..."
        docker-compose down
        ;;
    "restart")
        log_info "重启服务..."
        docker-compose restart
        ;;
    "logs")
        log_info "查看日志..."
        docker-compose logs -f
        ;;
    "status")
        log_info "查看状态..."
        docker-compose ps
        ;;
    "clean")
        log_warning "清理所有容器和镜像..."
        docker-compose down -v --rmi all
        ;;
    *)
        main
        ;;
esac
