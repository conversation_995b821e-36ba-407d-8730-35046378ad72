#!/bin/bash

# 修复后端容器问题

echo "=== 修复后端容器问题 ==="

# 1. 停止问题容器
echo "1. 停止问题容器..."
docker-compose stop backend
docker rm wb_backend 2>/dev/null || true

# 2. 查看错误日志
echo ""
echo "2. 查看错误日志..."
docker logs wb_backend 2>/dev/null || echo "容器已删除，无法查看日志"

# 3. 检查应用文件
echo ""
echo "3. 检查应用文件..."
if [ ! -f "app/main.py" ]; then
    echo "❌ app/main.py 不存在，创建基础应用..."
    mkdir -p app
    
    cat > app/main.py << 'EOF'
from fastapi import FastAPI
from fastapi.responses import JSONResponse
import os

app = FastAPI(
    title="微博任务管理系统",
    description="微博任务管理系统API",
    version="1.0.0"
)

@app.get("/")
async def root():
    return {"message": "微博任务管理系统", "status": "running"}

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "wb_backend",
        "version": "1.0.0"
    }

@app.get("/info")
async def info():
    return {
        "database_url": os.getenv("DATABASE_URL", "not_configured"),
        "redis_url": os.getenv("REDIS_URL", "not_configured"),
        "debug": os.getenv("DEBUG", "false")
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF
    echo "✅ 创建了基础app/main.py"
fi

# 4. 检查其他必要文件
echo ""
echo "4. 检查其他必要文件..."

# 创建alembic目录和文件（如果不存在）
if [ ! -d "alembic" ]; then
    echo "创建alembic目录..."
    mkdir -p alembic/versions
    
    cat > alembic.ini << 'EOF'
[alembic]
script_location = alembic
sqlalchemy.url = mysql+pymysql://wb_user:wb_password@mysql:3306/wb

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S
EOF

    cat > alembic/env.py << 'EOF'
from logging.config import fileConfig
from sqlalchemy import engine_from_config
from sqlalchemy import pool
from alembic import context

config = context.config
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

target_metadata = None

def run_migrations_offline() -> None:
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
EOF

    cat > alembic/script.py.mako << 'EOF'
"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}

"""
from alembic import op
import sqlalchemy as sa
${imports if imports else ""}

# revision identifiers, used by Alembic.
revision = ${repr(up_revision)}
down_revision = ${repr(down_revision)}
branch_labels = ${repr(branch_labels)}
depends_on = ${repr(depends_on)}


def upgrade() -> None:
    ${upgrades if upgrades else "pass"}


def downgrade() -> None:
    ${downgrades if downgrades else "pass"}
EOF

    echo "✅ 创建了alembic配置"
fi

# 5. 重新构建镜像（使用简化版）
echo ""
echo "5. 重新构建镜像..."
cat > Dockerfile.fixed << 'EOF'
FROM python:3.9-slim

WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# 复制requirements文件
COPY requirements.txt .

# 安装依赖
RUN pip install --no-cache-dir \
    --index-url https://pypi.tuna.tsinghua.edu.cn/simple \
    --trusted-host pypi.tuna.tsinghua.edu.cn \
    -r requirements.txt

# 复制应用代码
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
EOF

echo "重新构建镜像..."
export DOCKER_BUILDKIT=0
if docker build -f Dockerfile.fixed -t wb_backend:latest .; then
    echo "✅ 镜像重新构建成功"
    rm -f Dockerfile.fixed
else
    echo "❌ 镜像构建失败"
    rm -f Dockerfile.fixed
    exit 1
fi

# 6. 重新启动服务
echo ""
echo "6. 重新启动后端服务..."
docker-compose up -d backend

# 7. 等待并检查状态
echo ""
echo "7. 等待服务启动..."
sleep 15

for i in {1..10}; do
    if curl -s http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ 后端服务启动成功"
        echo "健康检查响应:"
        curl -s http://localhost:8000/health | python3 -m json.tool 2>/dev/null || curl -s http://localhost:8000/health
        break
    fi
    echo "等待后端服务启动... ($i/10)"
    sleep 3
done

# 8. 显示最终状态
echo ""
echo "8. 最终状态..."
docker-compose ps

echo ""
echo "=== 修复完成 ==="
echo ""
echo "如果服务正常，可以访问:"
echo "  - 健康检查: http://***********:8000/health"
echo "  - API文档: http://***********:8000/docs"
echo "  - 系统信息: http://***********:8000/info"
