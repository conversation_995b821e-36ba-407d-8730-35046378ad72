#!/usr/bin/env python3
"""
测试设备离线时的任务分配问题
"""

import requests
import time

def check_device_status():
    """检查设备连接状态"""
    print('🔍 检查设备连接状态...')
    try:
        response = requests.get('http://localhost:8000/devices/', timeout=10)
        if response.status_code == 200:
            devices = response.json()
            print(f'设备总数: {len(devices)}')
            online_count = 0
            for device in devices:
                device_id = device.get('id')
                device_number = device.get('device_number')
                online_status = device.get('online_status')
                status_icon = '🟢' if online_status == 'online' else '🔴'
                print(f'  {status_icon} 设备{device_id}: {device_number} - {online_status}')
                if online_status == 'online':
                    online_count += 1
            print(f'在线设备数量: {online_count}/{len(devices)}')
            return online_count > 0
        else:
            print(f'获取设备失败: {response.text}')
            return False
    except Exception as e:
        print(f'请求异常: {e}')
        return False

def create_test_task():
    """创建测试任务"""
    print('\n📤 创建测试任务...')
    try:
        task_data = {
            'task_type': 'like',
            'target_scope': 'single',
            'target_id': 3,  # 使用离线设备
            'parameters': {
                'blogger_id': 'offline_test_blogger',
                'like_id': 'offline_test_like',
                'delay_click': 1000
            },
            'delay_group': 2000,
            'delay_like': 1000
        }
        
        response = requests.post('http://localhost:8000/tasks/', json=task_data, timeout=10)
        print(f'📊 创建状态码: {response.status_code}')
        
        if response.status_code == 200:
            task = response.json()
            task_id = task.get('id')
            initial_status = task.get('status')
            print(f'✅ 任务创建成功: ID={task_id}')
            print(f'初始状态: {initial_status}')
            return task_id
        else:
            print(f'❌ 任务创建失败: {response.text}')
            return None
    except Exception as e:
        print(f'❌ 创建异常: {e}')
        return None

def monitor_task_status(task_id, duration=60):
    """监控任务状态变化"""
    print(f'\n⏳ 监控任务{task_id}状态变化（{duration}秒）...')
    
    for i in range(duration):
        try:
            response = requests.get(f'http://localhost:8000/tasks/{task_id}')
            if response.status_code == 200:
                task = response.json()
                status = task.get('status')
                print(f'  第{i+1}秒: 状态={status}')
                
                if status == 'running':
                    print(f'  ⚠️ 问题发现: 设备离线但任务变为running状态!')
                elif status == 'failed':
                    print(f'  ❌ 任务失败: 可能是超时导致的')
                    break
                elif status in ['completed', 'done']:
                    print(f'  ✅ 任务完成: {status}')
                    break
            else:
                print(f'  第{i+1}秒: 无法获取任务状态')
                
        except Exception as e:
            print(f'  第{i+1}秒: 请求异常: {e}')
            
        time.sleep(1)

def main():
    """主函数"""
    print('🐛 测试设备离线时的任务分配问题')
    print('='*50)
    
    # 1. 检查设备状态
    has_online_devices = check_device_status()
    
    if has_online_devices:
        print('\n⚠️ 有设备在线，请先断开所有设备连接再测试')
        return
    
    print('\n✅ 确认所有设备都离线，开始测试...')
    
    # 2. 创建任务
    task_id = create_test_task()
    if not task_id:
        return
    
    # 3. 监控任务状态
    monitor_task_status(task_id, 60)
    
    print('\n📋 测试结论:')
    print('如果任务状态变为running，说明存在问题：')
    print('- 设备离线时不应该分配任务')
    print('- 任务应该保持pending状态等待设备上线')

if __name__ == "__main__":
    main()
