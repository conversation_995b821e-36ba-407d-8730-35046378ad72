#!/bin/bash

# 微博任务管理系统 - 完整部署脚本
# 支持服务器和本地部署

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检测环境
detect_environment() {
    if [ -f /.dockerenv ]; then
        echo "docker"
    elif command -v systemctl >/dev/null 2>&1; then
        echo "server"
    else
        echo "local"
    fi
}

ENV_TYPE=$(detect_environment)

echo "========================================"
echo "  微博任务管理系统 - 完整部署"
echo "========================================"
echo "检测到环境类型: $ENV_TYPE"
echo ""

# 1. 环境准备
log_step "1. 环境准备"

# 安装必要的软件
if command -v dnf >/dev/null 2>&1; then
    log_info "使用dnf安装依赖..."
    sudo dnf install -y python3 python3-pip python3-venv mysql-server redis git curl
elif command -v apt >/dev/null 2>&1; then
    log_info "使用apt安装依赖..."
    sudo apt update
    sudo apt install -y python3 python3-pip python3-venv mysql-server redis-server git curl
fi

# 2. 创建项目目录结构
log_step "2. 创建项目目录结构"

PROJECT_DIR="/opt/wb_system"
if [ "$ENV_TYPE" = "local" ]; then
    PROJECT_DIR="./wb_system"
fi

sudo mkdir -p $PROJECT_DIR
sudo chown -R $(whoami):$(whoami) $PROJECT_DIR 2>/dev/null || true
cd $PROJECT_DIR

# 3. 创建Python虚拟环境
log_step "3. 创建Python虚拟环境"

if [ ! -d "venv" ]; then
    python3 -m venv venv
    log_info "虚拟环境创建成功"
fi

source venv/bin/activate

# 4. 安装Python依赖
log_step "4. 安装Python依赖"

cat > requirements.txt << 'EOF'
fastapi==0.115.12
uvicorn[standard]==0.34.3
sqlalchemy==2.0.41
pymysql==1.1.1
redis==5.2.1
python-dotenv==1.1.0
pydantic==2.11.5
websockets==15.0.1
requests==2.32.3
alembic==1.13.3
python-multipart==0.0.20
jinja2==3.1.5
aiofiles==24.1.0
EOF

pip install --upgrade pip
pip install -r requirements.txt

# 5. 创建应用目录结构
log_step "5. 创建应用目录结构"

mkdir -p app/{models,routes,services,schemas,utils,websocket}
mkdir -p logs
mkdir -p data

# 6. 创建数据库配置
log_step "6. 创建数据库配置"

cat > app/db.py << 'EOF'
from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
import time
import logging

logger = logging.getLogger(__name__)

# 数据库配置
MYSQL_HOST = os.getenv("MYSQL_HOST", "localhost")
MYSQL_PORT = int(os.getenv("MYSQL_PORT", "3306"))
MYSQL_USER = os.getenv("MYSQL_USER", "wb_user")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD", "wb_password")
MYSQL_DATABASE = os.getenv("MYSQL_DATABASE", "wb")

DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}?charset=utf8mb4"

engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_timeout=20,
    echo=False
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def test_db_connection():
    """测试数据库连接"""
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        return True
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        return False

def wait_for_db(max_retries=30):
    """等待数据库可用"""
    for i in range(max_retries):
        if test_db_connection():
            logger.info("数据库连接成功")
            return True
        logger.info(f"等待数据库连接... ({i+1}/{max_retries})")
        time.sleep(2)
    return False
EOF

# 7. 创建主应用文件
log_step "7. 创建主应用文件"

cat > app/main.py << 'EOF'
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import logging
import asyncio
from datetime import datetime
import socket
import os

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="微博任务管理系统",
    description="微博任务管理系统API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局状态
DATABASE_CONNECTED = False
REDIS_CONNECTED = False

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

@app.get("/")
async def root():
    return {
        "message": "微博任务管理系统",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "database_connected": DATABASE_CONNECTED,
        "redis_connected": REDIS_CONNECTED
    }

@app.get("/health")
async def health_check():
    global DATABASE_CONNECTED, REDIS_CONNECTED
    
    # 检查数据库
    try:
        from app.db import test_db_connection
        DATABASE_CONNECTED = test_db_connection()
    except:
        DATABASE_CONNECTED = False
    
    # 检查Redis
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, decode_responses=True)
        r.ping()
        REDIS_CONNECTED = True
    except:
        REDIS_CONNECTED = False
    
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "service": "wb_backend",
        "server_ip": get_local_ip(),
        "database": "connected" if DATABASE_CONNECTED else "disconnected",
        "redis": "connected" if REDIS_CONNECTED else "disconnected",
        "components": {
            "database": DATABASE_CONNECTED,
            "redis": REDIS_CONNECTED
        }
    }

@app.get("/system")
async def system_info():
    return {
        "hostname": socket.gethostname(),
        "local_ip": get_local_ip(),
        "python_version": os.sys.version,
        "working_directory": os.getcwd(),
        "environment": {
            "MYSQL_HOST": os.getenv("MYSQL_HOST", "localhost"),
            "MYSQL_USER": os.getenv("MYSQL_USER", "wb_user"),
            "MYSQL_DATABASE": os.getenv("MYSQL_DATABASE", "wb")
        }
    }

# 基础API端点
@app.get("/api/devices")
async def get_devices():
    return {"devices": [], "total": 0, "message": "设备列表API"}

@app.get("/api/tasks")
async def get_tasks():
    return {"tasks": [], "total": 0, "message": "任务列表API"}

@app.get("/api/groups")
async def get_groups():
    return {"groups": [], "total": 0, "message": "分组列表API"}

@app.post("/api/devices")
async def create_device():
    return {"message": "设备创建API", "status": "success"}

@app.post("/api/tasks")
async def create_task():
    return {"message": "任务创建API", "status": "success"}

@app.post("/api/groups")
async def create_group():
    return {"message": "分组创建API", "status": "success"}

async def init_services():
    """初始化服务"""
    global DATABASE_CONNECTED
    
    try:
        from app.db import wait_for_db, Base, engine
        
        if wait_for_db():
            Base.metadata.create_all(bind=engine)
            DATABASE_CONNECTED = True
            logger.info("数据库初始化成功")
        else:
            logger.warning("数据库连接失败，使用模拟模式")
            
    except Exception as e:
        logger.error(f"服务初始化失败: {e}")

@app.on_event("startup")
async def startup_event():
    logger.info("微博任务管理系统启动中...")
    asyncio.create_task(init_services())
    logger.info("应用启动完成")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

# 8. 创建环境配置文件
log_step "8. 创建环境配置文件"

cat > .env << 'EOF'
# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=wb_user
MYSQL_PASSWORD=wb_password
MYSQL_DATABASE=wb

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# 应用配置
DEBUG=false
LOG_LEVEL=info
SECRET_KEY=your-secret-key-change-in-production
EOF

# 9. 配置数据库
log_step "9. 配置数据库"

if command -v mysql >/dev/null 2>&1; then
    log_info "配置MySQL数据库..."
    
    # 启动MySQL服务
    if command -v systemctl >/dev/null 2>&1; then
        sudo systemctl start mysqld 2>/dev/null || sudo systemctl start mysql 2>/dev/null || true
        sudo systemctl enable mysqld 2>/dev/null || sudo systemctl enable mysql 2>/dev/null || true
    fi
    
    # 创建数据库和用户
    mysql -u root -p << 'EOF' 2>/dev/null || log_warn "请手动配置数据库"
CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'wb_user'@'localhost' IDENTIFIED BY 'wb_password';
GRANT ALL PRIVILEGES ON wb.* TO 'wb_user'@'localhost';
FLUSH PRIVILEGES;
EOF
else
    log_warn "MySQL未安装，将使用SQLite作为备选"
fi

# 10. 配置Redis
log_step "10. 配置Redis"

if command -v redis-server >/dev/null 2>&1; then
    if command -v systemctl >/dev/null 2>&1; then
        sudo systemctl start redis 2>/dev/null || sudo systemctl start redis-server 2>/dev/null || true
        sudo systemctl enable redis 2>/dev/null || sudo systemctl enable redis-server 2>/dev/null || true
    fi
else
    log_warn "Redis未安装，某些功能可能受限"
fi

# 11. 创建启动脚本
log_step "11. 创建启动脚本"

cat > start.sh << 'EOF'
#!/bin/bash

cd "$(dirname "$0")"
source venv/bin/activate
export $(cat .env | grep -v '^#' | xargs) 2>/dev/null || true

echo "启动微博任务管理系统..."
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
EOF

chmod +x start.sh

# 12. 创建系统服务（仅服务器环境）
if [ "$ENV_TYPE" = "server" ]; then
    log_step "12. 创建系统服务"
    
    sudo tee /etc/systemd/system/wb-system.service << EOF
[Unit]
Description=微博任务管理系统
After=network.target mysql.service redis.service

[Service]
Type=simple
User=$(whoami)
WorkingDirectory=$PROJECT_DIR
Environment=PATH=$PROJECT_DIR/venv/bin
EnvironmentFile=$PROJECT_DIR/.env
ExecStart=$PROJECT_DIR/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable wb-system
fi

# 13. 创建代理服务
log_step "13. 创建代理服务"

cat > proxy.py << 'EOF'
#!/usr/bin/env python3
import http.server
import socketserver
import urllib.request
import urllib.parse
from datetime import datetime

class ProxyHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        self.proxy_request()
    
    def do_POST(self):
        self.proxy_request()
    
    def proxy_request(self):
        try:
            target_url = f"http://127.0.0.1:8000{self.path}"
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length) if content_length > 0 else None
            
            req = urllib.request.Request(target_url, data=post_data, method=self.command)
            
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'content-length']:
                    req.add_header(header, value)
            
            with urllib.request.urlopen(req, timeout=30) as response:
                self.send_response(response.getcode())
                for header, value in response.headers.items():
                    if header.lower() not in ['server', 'date']:
                        self.send_header(header, value)
                self.end_headers()
                self.wfile.write(response.read())
                
        except Exception as e:
            self.send_error(502, f"Bad Gateway: {str(e)}")

if __name__ == "__main__":
    with socketserver.TCPServer(("", 80), ProxyHandler) as httpd:
        print(f"代理服务器启动在80端口")
        httpd.serve_forever()
EOF

chmod +x proxy.py

echo ""
echo "========================================"
echo "  部署完成！"
echo "========================================"
echo ""
log_info "项目目录: $PROJECT_DIR"
log_info "启动命令: ./start.sh"
log_info "代理命令: sudo python3 proxy.py"
echo ""

if [ "$ENV_TYPE" = "server" ]; then
    log_info "系统服务管理:"
    echo "  启动: sudo systemctl start wb-system"
    echo "  停止: sudo systemctl stop wb-system"
    echo "  状态: sudo systemctl status wb-system"
    echo "  日志: sudo journalctl -u wb-system -f"
fi

echo ""
log_info "访问地址:"
echo "  - 直接访问: http://localhost:8000"
echo "  - 代理访问: http://localhost (需要sudo权限)"
if [ "$ENV_TYPE" = "server" ]; then
    echo "  - 外部访问: http://$(curl -s ifconfig.me 2>/dev/null || echo 'YOUR_SERVER_IP')"
fi

echo ""
log_info "测试命令:"
echo "  curl http://localhost:8000/health"
echo "  curl http://localhost:8000/docs"

echo ""
echo "🎉 部署完成！请运行 ./start.sh 启动服务"
