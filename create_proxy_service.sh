#!/bin/bash

# 创建代理服务的systemd服务

echo "=== 创建代理服务 ==="

# 创建systemd服务文件
sudo tee /etc/systemd/system/wb-proxy.service << EOF
[Unit]
Description=微博任务管理系统代理服务
After=network.target wb-system.service

[Service]
Type=simple
User=root
WorkingDirectory=$(pwd)
ExecStart=/usr/bin/python3 $(pwd)/proxy_server.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# 重新加载systemd
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable wb-proxy

echo "✅ 代理服务已创建"
echo ""
echo "管理命令："
echo "  启动: sudo systemctl start wb-proxy"
echo "  停止: sudo systemctl stop wb-proxy"
echo "  状态: sudo systemctl status wb-proxy"
echo "  日志: sudo journalctl -u wb-proxy -f"
