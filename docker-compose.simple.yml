services:
  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: wb_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: wb
      MYSQL_USER: wb_user
      MYSQL_PASSWORD: wb_password
      MYSQL_ROOT_HOST: '%'
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - wb_network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123456"]
      timeout: 20s
      retries: 10

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: wb_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - wb_network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 20s
      retries: 10

  # 后端API服务
  backend:
    image: wb_backend:latest
    container_name: wb_backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      # 数据库配置
      DATABASE_URL: mysql+pymysql://wb_user:wb_password@mysql:3306/wb
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_USER: wb_user
      MYSQL_PASSWORD: wb_password
      MYSQL_DATABASE: wb
      
      # Redis配置
      REDIS_URL: redis://redis:6379/0
      
      # 应用配置
      DEBUG: "false"
      LOG_LEVEL: "info"
      
      # 安全配置
      SECRET_KEY: "your-secret-key-change-in-production"
      ACCESS_TOKEN_EXPIRE_MINUTES: 30
      
    volumes:
      - ./logs:/app/logs
    networks:
      - wb_network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      timeout: 20s
      retries: 10
      start_period: 30s

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  wb_network:
    driver: bridge
