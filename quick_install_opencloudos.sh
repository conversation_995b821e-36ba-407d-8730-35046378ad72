#!/bin/bash

# OpenCloudOS 9快速安装脚本
# 简化版一键安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo "======================================================="
    echo "🚀 微博设备管理系统 - OpenCloudOS 9快速安装"
    echo "======================================================="
    echo ""
    echo "检测到您的系统："
    cat /etc/opencloudos-release 2>/dev/null || cat /etc/redhat-release 2>/dev/null || echo "Linux系统"
    echo ""
    echo "本脚本将自动安装："
    echo "✅ Docker CE 和 Docker Compose"
    echo "✅ 微博设备管理系统"
    echo "✅ 配置防火墙和服务"
    echo ""
    
    read -p "是否继续安装？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "安装已取消"
        exit 0
    fi
}

# 检查root权限
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
}

# 更新系统
update_system() {
    log_info "更新系统..."
    dnf update -y
    dnf install -y dnf-plugins-core curl wget git vim net-tools
    log_success "系统更新完成"
}

# 安装Docker
install_docker() {
    log_info "安装Docker..."
    
    if command -v docker >/dev/null 2>&1; then
        log_warning "Docker已安装，跳过"
        return
    fi
    
    # 卸载旧版本
    dnf remove -y docker docker-client docker-client-latest docker-common docker-latest docker-latest-logrotate docker-logrotate docker-engine podman runc 2>/dev/null || true
    
    # 添加Docker仓库
    dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
    
    # 安装Docker
    dnf install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    
    # 启动Docker
    systemctl start docker
    systemctl enable docker
    
    log_success "Docker安装完成"
}

# 安装Docker Compose
install_docker_compose() {
    log_info "安装Docker Compose..."
    
    if docker compose version >/dev/null 2>&1; then
        log_success "Docker Compose Plugin已安装"
        # 创建兼容性软链接
        if [ ! -f /usr/local/bin/docker-compose ]; then
            cat > /usr/local/bin/docker-compose << 'EOF'
#!/bin/bash
docker compose "$@"
EOF
            chmod +x /usr/local/bin/docker-compose
            ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
        fi
        return
    fi
    
    # 下载独立版本
    COMPOSE_VERSION="v2.24.0"
    curl -L "https://github.com/docker/compose/releases/download/${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    log_success "Docker Compose安装完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    if systemctl is-active --quiet firewalld; then
        firewall-cmd --permanent --add-port=80/tcp
        firewall-cmd --permanent --add-port=443/tcp
        firewall-cmd --permanent --add-port=8000/tcp
        firewall-cmd --permanent --zone=trusted --add-interface=docker0
        firewall-cmd --permanent --zone=trusted --add-masquerade
        firewall-cmd --reload
        log_success "防火墙配置完成"
    else
        log_warning "防火墙未运行"
    fi
}

# 创建项目目录
create_project_dir() {
    log_info "创建项目目录..."
    
    mkdir -p /opt/wb_system
    mkdir -p /opt/wb_system/logs/nginx
    mkdir -p /opt/wb_system/data/mysql
    mkdir -p /opt/wb_system/data/redis
    mkdir -p /opt/wb_system/backups
    
    log_success "项目目录创建完成"
}

# 复制项目文件
copy_project_files() {
    log_info "复制项目文件..."
    
    # 检查必要文件
    if [ ! -f "docker-compose.yml" ]; then
        log_error "未找到docker-compose.yml文件"
        log_info "请确保在项目根目录运行此脚本"
        exit 1
    fi
    
    # 复制文件
    cp -r app/ /opt/wb_system/ 2>/dev/null || log_warning "app目录不存在"
    cp -r alembic/ /opt/wb_system/ 2>/dev/null || log_warning "alembic目录不存在"
    cp -r nginx/ /opt/wb_system/ 2>/dev/null || log_warning "nginx目录不存在"
    cp docker-compose.yml /opt/wb_system/
    cp requirements.txt /opt/wb_system/ 2>/dev/null || log_warning "requirements.txt不存在"
    cp Dockerfile /opt/wb_system/ 2>/dev/null || log_warning "Dockerfile不存在"
    cp .env.example /opt/wb_system/ 2>/dev/null || log_warning ".env.example不存在"
    cp alembic.ini /opt/wb_system/ 2>/dev/null || log_warning "alembic.ini不存在"
    cp .dockerignore /opt/wb_system/ 2>/dev/null || log_warning ".dockerignore不存在"
    
    log_success "项目文件复制完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    cd /opt/wb_system
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
        else
            # 创建基本的.env文件
            cat > .env << EOF
# 数据库配置
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=wb_user
MYSQL_PASSWORD=wb_password_$(date +%s)
MYSQL_DATABASE=wb
DATABASE_URL=mysql+pymysql://wb_user:wb_password_$(date +%s)@mysql:3306/wb

# 应用配置
SECRET_KEY=$(openssl rand -hex 32)
DEBUG=false
LOG_LEVEL=info
EOF
        fi
        
        # 生成随机密码
        MYSQL_ROOT_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
        MYSQL_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
        SECRET_KEY=$(openssl rand -hex 32)
        
        # 更新配置
        sed -i "s/your-secret-key-change-in-production/$SECRET_KEY/" .env
        sed -i "s/123456/$MYSQL_ROOT_PASSWORD/" .env
        sed -i "s/wb_password.*/$MYSQL_PASSWORD/" .env
        
        # 保存密码
        echo "=== 微博设备管理系统数据库密码 ===" > passwords.txt
        echo "MySQL Root Password: $MYSQL_ROOT_PASSWORD" >> passwords.txt
        echo "MySQL User Password: $MYSQL_PASSWORD" >> passwords.txt
        echo "生成时间: $(date)" >> passwords.txt
        chmod 600 passwords.txt
        
        log_success "环境配置完成"
        log_info "数据库密码已保存到 /opt/wb_system/passwords.txt"
    else
        log_info ".env文件已存在"
    fi
}

# 部署服务
deploy_services() {
    log_info "部署服务..."
    
    cd /opt/wb_system
    
    # 拉取镜像
    log_info "拉取Docker镜像..."
    docker pull python:3.9-slim
    docker pull mysql:8.0
    docker pull redis:7-alpine
    docker pull nginx:alpine
    
    # 启动服务
    log_info "启动服务..."
    docker-compose up -d
    
    log_success "服务部署完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    cd /opt/wb_system
    
    sleep 30  # 等待服务启动
    
    echo "服务状态:"
    docker-compose ps
    
    # 检查健康状态
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        log_success "后端服务正常"
    else
        log_warning "后端服务可能还在启动中"
    fi
}

# 创建管理脚本
create_management_scripts() {
    log_info "创建管理脚本..."
    
    # 创建状态检查脚本
    cat > /usr/local/bin/wb-status << 'EOF'
#!/bin/bash
echo "=== 微博设备管理系统状态 ==="
echo "服务器IP: $(ip route get ******* | awk -F'src ' 'NR==1{split($2,a," ");print a[1]}')"
echo ""
cd /opt/wb_system
docker-compose ps
EOF
    chmod +x /usr/local/bin/wb-status
    
    # 创建日志查看脚本
    cat > /usr/local/bin/wb-logs << 'EOF'
#!/bin/bash
cd /opt/wb_system
docker-compose logs -f
EOF
    chmod +x /usr/local/bin/wb-logs
    
    log_success "管理脚本创建完成"
}

# 显示完成信息
show_completion_info() {
    SERVER_IP=$(ip route get ******* | awk -F"src " 'NR==1{split($2,a," ");print a[1]}')
    
    clear
    echo "======================================================="
    echo "🎉 OpenCloudOS 9 安装完成！"
    echo "======================================================="
    echo ""
    echo "📱 访问地址："
    echo "  http://$SERVER_IP"
    echo "  http://$SERVER_IP/docs (API文档)"
    echo ""
    echo "🗄️ 数据库信息："
    echo "  主机: $SERVER_IP:3306"
    echo "  用户: wb_user"
    echo "  密码: 查看 /opt/wb_system/passwords.txt"
    echo ""
    echo "📊 管理命令："
    echo "  wb-status    # 查看系统状态"
    echo "  wb-logs      # 查看系统日志"
    echo ""
    echo "⚙️ 前端配置："
    echo "  服务器地址: $SERVER_IP:8000"
    echo ""
    echo "📁 项目目录: /opt/wb_system"
    echo "🔑 密码文件: /opt/wb_system/passwords.txt"
    echo ""
    echo "======================================================="
}

# 主函数
main() {
    show_welcome
    check_root
    update_system
    install_docker
    install_docker_compose
    configure_firewall
    create_project_dir
    copy_project_files
    setup_environment
    deploy_services
    verify_deployment
    create_management_scripts
    show_completion_info
}

# 运行主函数
main
