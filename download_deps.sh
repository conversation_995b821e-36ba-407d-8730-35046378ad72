#!/bin/bash

# 手动下载Python依赖脚本

echo "=== 下载Python依赖包 ==="

# 创建wheels目录
mkdir -p wheels
cd wheels

echo "开始下载依赖包..."

# 手动下载每个包（使用宿主机的网络）
packages=(
    "fastapi==0.115.12"
    "uvicorn==0.34.3"
    "sqlalchemy==2.0.41"
    "pymysql==1.1.1"
    "websockets==15.0.1"
    "requests==2.32.3"
    "pydantic==2.11.5"
    "python-dotenv==1.1.0"
    "PyYAML==6.0.2"
)

# 尝试多个镜像源
mirrors=(
    "https://pypi.tuna.tsinghua.edu.cn/simple"
    "https://mirrors.aliyun.com/pypi/simple/"
    "https://pypi.douban.com/simple/"
    "https://pypi.python.org/simple/"
)

download_success=false

for mirror in "${mirrors[@]}"; do
    echo "尝试使用镜像源: $mirror"
    
    if pip3 download --no-deps -i "$mirror" --trusted-host "$(echo $mirror | cut -d'/' -f3)" "${packages[@]}"; then
        echo "✅ 使用 $mirror 下载成功"
        download_success=true
        break
    else
        echo "❌ $mirror 下载失败，尝试下一个..."
    fi
done

if [ "$download_success" = false ]; then
    echo "❌ 所有镜像源都失败，尝试逐个下载..."
    
    for package in "${packages[@]}"; do
        echo "下载 $package ..."
        for mirror in "${mirrors[@]}"; do
            if pip3 download --no-deps -i "$mirror" --trusted-host "$(echo $mirror | cut -d'/' -f3)" "$package"; then
                echo "✅ $package 下载成功"
                break
            fi
        done
    done
fi

cd ..

# 检查下载结果
if [ -d "wheels" ] && [ "$(ls -A wheels)" ]; then
    echo ""
    echo "✅ 依赖包下载完成！"
    echo "下载的文件："
    ls -la wheels/
    
    echo ""
    echo "现在创建离线Dockerfile..."
    
    cat > Dockerfile.offline_manual << 'EOF'
FROM python:3.9-slim

WORKDIR /app

# 复制本地wheel文件
COPY wheels/ ./wheels/

# 复制requirements文件
COPY requirements.txt .

# 从本地wheel文件安装依赖
RUN pip install --no-index --find-links wheels -r requirements.txt

# 复制应用代码
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF
    
    echo "离线Dockerfile已创建: Dockerfile.offline_manual"
    echo ""
    echo "现在可以运行离线构建："
    echo "docker build -f Dockerfile.offline_manual -t wb_backend:latest ."
    
else
    echo "❌ 依赖包下载失败"
    echo "请检查网络连接和Python环境"
fi
