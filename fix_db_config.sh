#!/bin/bash

# 修复Docker中的数据库连接配置

echo "=== 修复数据库连接配置 ==="

# 1. 检查当前数据库配置
echo "1. 检查当前数据库配置..."
if [ -f "app/db.py" ]; then
    echo "当前db.py内容:"
    head -20 app/db.py
else
    echo "db.py文件不存在，将创建"
fi

# 2. 创建正确的数据库配置
echo ""
echo "2. 创建正确的数据库配置..."

cat > app/db.py << 'EOF'
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

# 数据库配置
# 在Docker环境中，使用容器名作为主机名
MYSQL_HOST = os.getenv("MYSQL_HOST", "mysql")
MYSQL_PORT = os.getenv("MYSQL_PORT", "3306")
MYSQL_USER = os.getenv("MYSQL_USER", "wb_user")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD", "wb_password")
MYSQL_DATABASE = os.getenv("MYSQL_DATABASE", "wb")

# 构建数据库URL
DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}"

print(f"数据库连接URL: {DATABASE_URL}")

# 创建数据库引擎
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=False
)

# 创建会话
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基类
Base = declarative_base()

# 获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
EOF

echo "✅ 数据库配置已更新"

# 3. 修复main.py中的数据库初始化
echo ""
echo "3. 修复main.py中的数据库初始化..."

# 备份原文件
cp app/main.py app/main.py.backup

cat > app/main.py << 'EOF'
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
import asyncio
from datetime import datetime
import socket
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="微博任务管理系统",
    description="微博任务管理系统API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

@app.get("/")
async def root():
    return {
        "message": "微博任务管理系统",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "deployment": "docker"
    }

@app.get("/health")
async def health_check():
    try:
        # 尝试连接数据库
        from app.db import engine
        
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "service": "wb_backend",
            "deployment": "docker",
            "server_ip": get_local_ip(),
            "database": "connected"
        }
    except Exception as e:
        logger.warning(f"Database connection failed: {e}")
        return {
            "status": "partial",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "service": "wb_backend",
            "deployment": "docker",
            "server_ip": get_local_ip(),
            "database": "disconnected",
            "error": str(e)
        }

@app.get("/system")
async def system_info():
    return {
        "hostname": socket.gethostname(),
        "local_ip": get_local_ip(),
        "environment": {
            "DATABASE_URL": os.getenv("DATABASE_URL", "not_configured"),
            "MYSQL_HOST": os.getenv("MYSQL_HOST", "not_configured"),
            "REDIS_URL": os.getenv("REDIS_URL", "not_configured")
        },
        "deployment": "docker"
    }

# 延迟初始化数据库
async def init_database():
    """异步初始化数据库"""
    try:
        from app.db import engine, Base
        
        # 等待数据库准备就绪
        max_retries = 30
        for i in range(max_retries):
            try:
                with engine.connect() as conn:
                    conn.execute("SELECT 1")
                logger.info("数据库连接成功")
                break
            except Exception as e:
                if i < max_retries - 1:
                    logger.info(f"等待数据库启动... ({i+1}/{max_retries})")
                    await asyncio.sleep(2)
                else:
                    logger.error(f"数据库连接失败: {e}")
                    return
        
        # 创建表
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建成功")
        
        # 导入路由
        try:
            from app.routes import device, task, group, task_sync
            from app.websocket import ws_manager
            
            app.include_router(device.router, prefix="/api", tags=["Devices"])
            app.include_router(task.router, prefix="/api", tags=["Tasks"])
            app.include_router(group.router, prefix="/api", tags=["Groups"])
            app.include_router(task_sync.router, prefix="/api", tags=["Task Sync"])
            app.include_router(ws_manager.router, prefix="/ws", tags=["WebSocket"])
            
            logger.info("路由加载成功")
        except Exception as e:
            logger.warning(f"路由加载失败: {e}")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")

@app.on_event("startup")
async def startup_event():
    logger.info("微博任务管理系统启动中...")
    
    # 异步初始化数据库
    asyncio.create_task(init_database())
    
    logger.info("应用启动完成")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

echo "✅ main.py已更新"

# 4. 重新构建和启动
echo ""
echo "4. 重新构建和启动容器..."

# 停止后端容器
docker-compose stop backend

# 重新构建后端镜像
echo "重新构建后端镜像..."
export DOCKER_BUILDKIT=0
docker-compose build backend --no-cache

# 启动后端容器
echo "启动后端容器..."
docker-compose up -d backend

echo ""
echo "5. 等待服务启动..."
sleep 20

# 检查状态
echo "检查容器状态..."
docker-compose ps

echo ""
echo "检查后端日志..."
docker-compose logs backend --tail 10

echo ""
echo "=== 修复完成 ==="
echo ""
echo "测试访问:"
echo "  curl http://***********/health"
echo "  curl http://***********/docs"
