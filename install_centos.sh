#!/bin/bash

# CentOS一键安装脚本
# 自动检测系统环境并安装微博设备管理系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo "=================================================="
    echo "🚀 微博设备管理系统 - CentOS一键安装脚本"
    echo "=================================================="
    echo ""
    echo "本脚本将自动完成以下操作："
    echo "✅ 检测CentOS系统版本"
    echo "✅ 安装Docker和Docker Compose"
    echo "✅ 配置防火墙和SELinux"
    echo "✅ 部署微博设备管理系统"
    echo "✅ 配置系统服务"
    echo ""
    echo "⚠️  注意事项："
    echo "• 请确保以root用户或sudo权限运行"
    echo "• 请确保服务器有稳定的网络连接"
    echo "• 建议至少4GB内存和20GB磁盘空间"
    echo ""
    
    read -p "是否继续安装？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "安装已取消"
        exit 0
    fi
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户或sudo权限运行此脚本"
        echo "使用方法: sudo bash $0"
        exit 1
    fi
    
    # 检查是否为CentOS
    if [ ! -f /etc/centos-release ]; then
        log_error "此脚本仅支持CentOS系统"
        exit 1
    fi
    
    # 检查内存
    MEMORY_GB=$(free -g | awk 'NR==2{print $2}')
    if [ $MEMORY_GB -lt 2 ]; then
        log_warning "内存不足4GB，当前: ${MEMORY_GB}GB，可能影响性能"
    else
        log_success "内存检查通过: ${MEMORY_GB}GB"
    fi
    
    # 检查磁盘空间
    DISK_GB=$(df / | tail -1 | awk '{print int($4/1024/1024)}')
    if [ $DISK_GB -lt 10 ]; then
        log_error "磁盘空间不足，至少需要10GB，当前可用: ${DISK_GB}GB"
        exit 1
    else
        log_success "磁盘空间检查通过: ${DISK_GB}GB可用"
    fi
    
    # 检查网络连接
    if ! ping -c 1 ******* >/dev/null 2>&1; then
        log_error "网络连接失败，请检查网络设置"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 下载项目文件
download_project() {
    log_info "准备项目文件..."
    
    # 如果当前目录没有项目文件，提示用户
    if [ ! -f "docker-compose.yml" ] || [ ! -f "Dockerfile" ]; then
        log_warning "未找到项目文件"
        echo ""
        echo "请确保以下文件存在于当前目录："
        echo "• docker-compose.yml"
        echo "• Dockerfile"
        echo "• requirements.txt"
        echo "• app/ 目录"
        echo "• nginx/ 目录"
        echo ""
        echo "您可以："
        echo "1. 将项目文件上传到服务器当前目录"
        echo "2. 使用git克隆项目: git clone <项目地址>"
        echo "3. 使用scp上传项目文件"
        echo ""
        read -p "项目文件已准备好？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "请准备好项目文件后重新运行脚本"
            exit 1
        fi
    fi
    
    log_success "项目文件检查通过"
}

# 执行系统检测
run_system_check() {
    log_info "执行系统环境检测..."
    
    if [ -f "check_centos_system.sh" ]; then
        chmod +x check_centos_system.sh
        ./check_centos_system.sh
    else
        log_warning "系统检测脚本不存在，跳过详细检测"
    fi
    
    echo ""
    read -p "是否继续安装？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "安装已取消"
        exit 0
    fi
}

# 执行部署
run_deployment() {
    log_info "开始部署系统..."
    
    if [ -f "deploy_centos.sh" ]; then
        chmod +x deploy_centos.sh
        ./deploy_centos.sh
    else
        log_error "部署脚本不存在"
        exit 1
    fi
}

# 配置系统服务
configure_system_service() {
    log_info "配置系统服务..."
    
    # 创建systemd服务文件
    cat > /etc/systemd/system/wb-system.service << EOF
[Unit]
Description=微博设备管理系统
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/wb_system
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

    # 重载systemd配置
    systemctl daemon-reload
    systemctl enable wb-system.service
    
    log_success "系统服务配置完成"
    log_info "可以使用以下命令管理服务："
    echo "  启动: systemctl start wb-system"
    echo "  停止: systemctl stop wb-system"
    echo "  重启: systemctl restart wb-system"
    echo "  状态: systemctl status wb-system"
}

# 创建管理脚本
create_management_scripts() {
    log_info "创建管理脚本..."
    
    # 创建管理脚本目录
    mkdir -p /usr/local/bin/wb-system
    
    # 创建状态检查脚本
    cat > /usr/local/bin/wb-system/status.sh << 'EOF'
#!/bin/bash
cd /opt/wb_system
echo "=== 服务状态 ==="
docker-compose ps
echo ""
echo "=== 系统资源 ==="
docker stats --no-stream
echo ""
echo "=== 磁盘使用 ==="
df -h /opt/wb_system
EOF

    # 创建日志查看脚本
    cat > /usr/local/bin/wb-system/logs.sh << 'EOF'
#!/bin/bash
cd /opt/wb_system
if [ "$1" ]; then
    docker-compose logs -f "$1"
else
    docker-compose logs -f
fi
EOF

    # 创建备份脚本
    cat > /usr/local/bin/wb-system/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/wb_system/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p "$BACKUP_DIR"
cd /opt/wb_system

echo "开始备份数据库..."
docker-compose exec -T mysql mysqldump -u root -p$(grep MYSQL_ROOT_PASSWORD .env | cut -d'=' -f2) wb > "$BACKUP_DIR/wb_backup_$DATE.sql"

echo "备份完成: $BACKUP_DIR/wb_backup_$DATE.sql"

# 清理7天前的备份
find "$BACKUP_DIR" -name "wb_backup_*.sql" -mtime +7 -delete
EOF

    # 设置执行权限
    chmod +x /usr/local/bin/wb-system/*.sh
    
    # 创建软链接
    ln -sf /usr/local/bin/wb-system/status.sh /usr/local/bin/wb-status
    ln -sf /usr/local/bin/wb-system/logs.sh /usr/local/bin/wb-logs
    ln -sf /usr/local/bin/wb-system/backup.sh /usr/local/bin/wb-backup
    
    log_success "管理脚本创建完成"
    log_info "可以使用以下命令："
    echo "  查看状态: wb-status"
    echo "  查看日志: wb-logs [服务名]"
    echo "  备份数据: wb-backup"
}

# 显示安装完成信息
show_completion_info() {
    # 获取服务器IP
    SERVER_IP=$(ip route get ******* | awk -F"src " 'NR==1{split($2,a," ");print a[1]}')
    
    clear
    echo "=================================================="
    echo "🎉 安装完成！"
    echo "=================================================="
    echo ""
    echo "📱 访问地址："
    echo "  http://$SERVER_IP"
    echo "  http://$SERVER_IP/docs (API文档)"
    echo ""
    echo "🗄️ 数据库信息："
    echo "  主机: $SERVER_IP:3306"
    echo "  用户: wb_user"
    echo "  密码: 查看 /opt/wb_system/passwords.txt"
    echo ""
    echo "📊 管理命令："
    echo "  wb-status    # 查看系统状态"
    echo "  wb-logs      # 查看系统日志"
    echo "  wb-backup    # 备份数据库"
    echo ""
    echo "🔧 系统服务："
    echo "  systemctl start wb-system    # 启动服务"
    echo "  systemctl stop wb-system     # 停止服务"
    echo "  systemctl restart wb-system  # 重启服务"
    echo ""
    echo "📁 项目目录: /opt/wb_system"
    echo "🔑 密码文件: /opt/wb_system/passwords.txt"
    echo ""
    echo "⚙️ 前端配置："
    echo "  服务器地址: $SERVER_IP:8000"
    echo ""
    echo "=================================================="
    echo "安装完成！请在前端应用中配置服务器地址。"
    echo "=================================================="
}

# 主函数
main() {
    show_welcome
    check_requirements
    download_project
    run_system_check
    run_deployment
    configure_system_service
    create_management_scripts
    show_completion_info
}

# 运行主函数
main
