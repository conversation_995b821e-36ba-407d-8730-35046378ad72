# Docker构建优化指南

## 问题分析

您遇到的Docker构建失败主要原因：

1. **网络连接问题**：DNS解析失败，无法连接到PyPI镜像源
2. **版本冲突**：Dockerfile中硬编码的版本与requirements.txt不匹配
3. **超时设置**：网络超时时间过短
4. **镜像源不稳定**：单一镜像源可能不可用

## 解决方案

### 方案1：优化后的标准构建（推荐）

使用优化后的Dockerfile和构建脚本：

```bash
# 给构建脚本执行权限
chmod +x build.sh

# 运行优化构建
./build.sh
```

**优化内容：**
- 增加多个DNS服务器
- 延长超时时间和重试次数
- 使用多个备用镜像源
- 添加网络诊断工具
- 自动重试机制

### 方案2：离线构建

当网络问题严重时，使用离线构建：

```bash
# 1. 预下载依赖包
python download_wheels.py

# 2. 使用离线Dockerfile构建
docker build -f Dockerfile.offline -t wb_backend:latest .
```

### 方案3：手动网络配置

如果仍有问题，手动配置网络：

```bash
# 1. 配置DNS
echo "nameserver 8.8.8.8" | sudo tee /etc/resolv.conf
echo "nameserver 114.114.114.114" | sudo tee -a /etc/resolv.conf

# 2. 测试网络连接
curl -I https://pypi.tuna.tsinghua.edu.cn/simple/

# 3. 使用代理构建（如果有代理）
docker build --build-arg HTTP_PROXY=http://proxy:port \
             --build-arg HTTPS_PROXY=http://proxy:port \
             -t wb_backend:latest .
```

## 构建命令对比

### 原始构建（可能失败）
```bash
docker-compose build
```

### 优化构建（推荐）
```bash
# 使用优化脚本
./build.sh

# 或手动优化构建
export DOCKER_BUILDKIT=1
docker build --no-cache --network=host -t wb_backend:latest .
```

### 离线构建
```bash
# 预下载
python download_wheels.py

# 离线构建
docker build -f Dockerfile.offline -t wb_backend:latest .
```

## 故障排除

### 1. DNS问题
```bash
# 测试DNS解析
nslookup pypi.tuna.tsinghua.edu.cn
nslookup mirrors.aliyun.com

# 更换DNS服务器
sudo systemctl restart systemd-resolved
```

### 2. 网络连接问题
```bash
# 测试网络连接
curl -I https://pypi.tuna.tsinghua.edu.cn/simple/
curl -I https://mirrors.aliyun.com/pypi/simple/

# 检查防火墙
sudo ufw status
```

### 3. Docker网络问题
```bash
# 重启Docker网络
docker network prune
sudo systemctl restart docker

# 使用主机网络
docker build --network=host -t wb_backend:latest .
```

### 4. 代理设置
如果在公司网络环境中：

```bash
# 设置代理环境变量
export HTTP_PROXY=http://proxy.company.com:8080
export HTTPS_PROXY=http://proxy.company.com:8080
export NO_PROXY=localhost,127.0.0.1

# 构建时传递代理参数
docker build --build-arg HTTP_PROXY=$HTTP_PROXY \
             --build-arg HTTPS_PROXY=$HTTPS_PROXY \
             -t wb_backend:latest .
```

## 验证构建结果

构建成功后，验证镜像：

```bash
# 查看镜像
docker images wb_backend:latest

# 测试运行
docker run --rm -p 8000:8000 wb_backend:latest

# 检查健康状态
curl http://localhost:8000/health
```

## 完整部署流程

```bash
# 1. 构建镜像
./build.sh

# 2. 启动所有服务
docker-compose up -d

# 3. 查看服务状态
docker-compose ps

# 4. 查看日志
docker-compose logs -f backend

# 5. 测试API
curl http://localhost:8000/health
```

## 性能优化建议

1. **使用多阶段构建**：减少最终镜像大小
2. **缓存优化**：合理安排COPY指令顺序
3. **并行构建**：使用BuildKit加速构建
4. **镜像源选择**：根据地理位置选择最快的源

## 常见错误及解决方案

| 错误 | 原因 | 解决方案 |
|------|------|----------|
| DNS解析失败 | 网络配置问题 | 更换DNS服务器 |
| 连接超时 | 网络不稳定 | 增加超时时间，使用重试 |
| 版本冲突 | 依赖版本不匹配 | 统一版本号 |
| 权限错误 | 用户权限不足 | 使用sudo或调整权限 |

## 联系支持

如果问题仍然存在，请提供：
1. 完整的错误日志
2. 网络环境信息
3. Docker版本信息
4. 操作系统信息
