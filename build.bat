@echo off
REM Docker构建优化脚本 - Windows版本
REM 解决网络连接问题和依赖安装失败

setlocal enabledelayedexpansion

echo === Docker构建优化脚本 ===

REM 检查Docker是否运行
echo [INFO] 检查Docker状态...
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker未运行，请启动Docker Desktop
    pause
    exit /b 1
)
echo [INFO] Docker运行正常

REM 清理旧的构建缓存
echo [INFO] 清理Docker构建缓存...
docker builder prune -f >nul 2>&1
docker system prune -f >nul 2>&1
echo [INFO] 缓存清理完成

REM 检查网络连接
echo [INFO] 检查网络连接...
ping -n 1 pypi.tuna.tsinghua.edu.cn >nul 2>&1
if errorlevel 1 (
    echo [WARN] 清华源网络连接失败，但继续构建...
) else (
    echo [INFO] 网络连接正常
)

REM 设置构建环境变量
set DOCKER_BUILDKIT=1
set BUILDKIT_PROGRESS=plain

REM 尝试构建（最多3次）
for /L %%i in (1,1,3) do (
    echo [INFO] 开始构建Docker镜像 ^(尝试 %%i/3^)...
    
    docker build --no-cache --progress=plain --build-arg BUILDKIT_INLINE_CACHE=1 --network=host -t wb_backend:latest .
    
    if !errorlevel! equ 0 (
        echo [INFO] Docker镜像构建成功！
        goto :success
    ) else (
        echo [ERROR] Docker镜像构建失败
        if %%i lss 3 (
            echo [WARN] 构建失败，等待10秒后重试...
            timeout /t 10 /nobreak >nul
            docker builder prune -f >nul 2>&1
        )
    )
)

echo [ERROR] 构建失败，已尝试3次
echo.
echo 故障排除建议：
echo 1. 检查网络连接是否正常
echo 2. 尝试更换DNS服务器
echo 3. 检查防火墙设置
echo 4. 尝试使用VPN或代理
echo 5. 使用离线构建方式
echo.
echo 离线构建命令：
echo   python download_wheels.py
echo   docker build -f Dockerfile.offline -t wb_backend:latest .
pause
exit /b 1

:success
echo [INFO] 构建成功！
echo.
echo 镜像信息：
docker images wb_backend:latest
echo.
echo 构建完成！可以使用以下命令启动服务：
echo   docker-compose up -d
echo.
echo 或者单独运行后端服务：
echo   docker run -p 8000:8000 wb_backend:latest
echo.
pause
