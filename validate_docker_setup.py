#!/usr/bin/env python3
"""
验证Docker部署配置
"""

import os
import yaml
import json

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (不存在)")
        return False

def validate_docker_compose():
    """验证docker-compose.yml配置"""
    print("\n🔍 验证Docker Compose配置...")
    
    if not check_file_exists("docker-compose.yml", "Docker Compose配置文件"):
        return False
    
    try:
        with open("docker-compose.yml", 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查必要的服务
        required_services = ['mysql', 'redis', 'backend', 'nginx']
        services = config.get('services', {})
        
        for service in required_services:
            if service in services:
                print(f"✅ 服务配置: {service}")
            else:
                print(f"❌ 缺少服务: {service}")
        
        # 检查网络配置
        if 'networks' in config:
            print("✅ 网络配置: 已定义")
        else:
            print("❌ 网络配置: 未定义")
        
        # 检查数据卷配置
        if 'volumes' in config:
            print("✅ 数据卷配置: 已定义")
        else:
            print("❌ 数据卷配置: 未定义")
        
        return True
        
    except Exception as e:
        print(f"❌ Docker Compose配置解析失败: {e}")
        return False

def validate_dockerfile():
    """验证Dockerfile"""
    print("\n🔍 验证Dockerfile...")
    
    if not check_file_exists("Dockerfile", "Dockerfile"):
        return False
    
    try:
        with open("Dockerfile", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查必要的指令
        required_instructions = ['FROM', 'WORKDIR', 'COPY', 'RUN', 'EXPOSE', 'CMD']
        
        for instruction in required_instructions:
            if instruction in content:
                print(f"✅ Dockerfile指令: {instruction}")
            else:
                print(f"❌ 缺少指令: {instruction}")
        
        return True
        
    except Exception as e:
        print(f"❌ Dockerfile读取失败: {e}")
        return False

def validate_requirements():
    """验证requirements.txt"""
    print("\n🔍 验证Python依赖...")
    
    if not check_file_exists("requirements.txt", "Python依赖文件"):
        return False
    
    try:
        with open("requirements.txt", 'r', encoding='utf-8') as f:
            requirements = f.read()
        
        # 检查关键依赖
        key_packages = ['fastapi', 'uvicorn', 'sqlalchemy', 'pymysql', 'websockets']
        
        for package in key_packages:
            if package.lower() in requirements.lower():
                print(f"✅ 关键依赖: {package}")
            else:
                print(f"❌ 缺少依赖: {package}")
        
        return True
        
    except Exception as e:
        print(f"❌ requirements.txt读取失败: {e}")
        return False

def validate_env_config():
    """验证环境配置"""
    print("\n🔍 验证环境配置...")
    
    env_example_exists = check_file_exists(".env.example", "环境配置示例")
    
    if env_example_exists:
        try:
            with open(".env.example", 'r', encoding='utf-8') as f:
                env_content = f.read()
            
            # 检查关键配置项
            key_configs = ['MYSQL_HOST', 'MYSQL_PASSWORD', 'SECRET_KEY', 'DATABASE_URL']
            
            for config in key_configs:
                if config in env_content:
                    print(f"✅ 环境变量: {config}")
                else:
                    print(f"❌ 缺少配置: {config}")
            
        except Exception as e:
            print(f"❌ 环境配置读取失败: {e}")
    
    return env_example_exists

def validate_nginx_config():
    """验证Nginx配置"""
    print("\n🔍 验证Nginx配置...")
    
    nginx_conf = check_file_exists("nginx/nginx.conf", "Nginx主配置")
    default_conf = check_file_exists("nginx/conf.d/default.conf", "Nginx站点配置")
    
    return nginx_conf and default_conf

def validate_deploy_scripts():
    """验证部署脚本"""
    print("\n🔍 验证部署脚本...")
    
    linux_script = check_file_exists("deploy.sh", "Linux部署脚本")
    windows_script = check_file_exists("deploy.bat", "Windows部署脚本")
    
    return linux_script and windows_script

def validate_dockerignore():
    """验证.dockerignore"""
    print("\n🔍 验证Docker忽略文件...")
    
    return check_file_exists(".dockerignore", "Docker忽略文件")

def main():
    """主函数"""
    print("🐳 Docker部署配置验证")
    print("="*50)
    
    # 验证各个组件
    validations = [
        validate_dockerfile(),
        validate_docker_compose(),
        validate_requirements(),
        validate_env_config(),
        validate_nginx_config(),
        validate_deploy_scripts(),
        validate_dockerignore()
    ]
    
    # 统计结果
    passed = sum(validations)
    total = len(validations)
    
    print(f"\n📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 Docker部署配置验证通过！")
        print("\n📝 下一步操作:")
        print("1. 安装Docker和Docker Compose")
        print("2. 复制.env.example到.env并修改配置")
        print("3. 运行部署脚本:")
        print("   - Linux/macOS: ./deploy.sh")
        print("   - Windows: deploy.bat")
        print("\n🔗 详细部署说明请参考: DOCKER_DEPLOY.md")
    else:
        print(f"\n❌ 验证失败，请检查缺失的文件和配置")
    
    return passed == total

if __name__ == "__main__":
    main()
