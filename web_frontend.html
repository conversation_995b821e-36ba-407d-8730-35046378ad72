<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微博任务管理系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .status-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .status-item {
            display: inline-block;
            margin-right: 20px;
            padding: 5px 10px;
            border-radius: 3px;
        }
        .status-online { background: #d4edda; color: #155724; }
        .status-offline { background: #f8d7da; color: #721c24; }
        .status-checking { background: #fff3cd; color: #856404; }
        .button-group {
            margin: 20px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .log-entry {
            margin-bottom: 5px;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
        .api-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>微博任务管理系统</h1>
            <p>前端测试界面</p>
        </div>
        
        <div class="status-panel">
            <div class="status-item" id="serverStatus">
                <strong>服务器:</strong> http://***********
            </div>
            <div class="status-item status-checking" id="connectionStatus">
                <strong>状态:</strong> 检测中...
            </div>
            <button class="btn" onclick="testConnection()">测试连接</button>
        </div>
        
        <div class="api-section">
            <h3>API功能测试</h3>
            <div class="button-group">
                <button class="btn" onclick="getSystemInfo()">获取系统信息</button>
                <button class="btn" onclick="getDevices()">获取设备列表</button>
                <button class="btn" onclick="getTasks()">获取任务列表</button>
                <button class="btn" onclick="getGroups()">获取分组列表</button>
                <button class="btn" onclick="clearLog()">清空日志</button>
            </div>
        </div>
        
        <div class="api-section">
            <h3>操作日志</h3>
            <div class="log-area" id="logArea"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://***********';
        
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
            log('日志已清空');
        }
        
        function updateConnectionStatus(status, message) {
            const statusEl = document.getElementById('connectionStatus');
            statusEl.className = `status-item status-${status}`;
            statusEl.innerHTML = `<strong>状态:</strong> ${message}`;
        }
        
        async function apiRequest(endpoint, options = {}) {
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                throw error;
            }
        }
        
        async function testConnection() {
            log('正在测试后端连接...', 'info');
            updateConnectionStatus('checking', '检测中...');
            
            try {
                const health = await apiRequest('/health');
                
                if (health.status === 'healthy') {
                    updateConnectionStatus('online', '连接正常');
                    log('✅ 后端连接成功', 'success');
                    log(`服务器信息: ${health.service || 'unknown'}`, 'info');
                    log(`版本: ${health.version || 'unknown'}`, 'info');
                } else {
                    updateConnectionStatus('offline', '连接异常');
                    log('❌ 后端连接失败', 'error');
                    log(`响应: ${JSON.stringify(health)}`, 'error');
                }
            } catch (error) {
                updateConnectionStatus('offline', '连接失败');
                log(`❌ 连接错误: ${error.message}`, 'error');
            }
        }
        
        async function getSystemInfo() {
            log('正在获取系统信息...', 'info');
            try {
                const info = await apiRequest('/system');
                log('✅ 系统信息获取成功', 'success');
                log(`主机名: ${info.hostname || 'unknown'}`, 'info');
                log(`本地IP: ${info.local_ip || 'unknown'}`, 'info');
                log(`Python版本: ${info.python_version || 'unknown'}`, 'info');
            } catch (error) {
                log(`❌ 获取系统信息失败: ${error.message}`, 'error');
            }
        }
        
        async function getDevices() {
            log('正在获取设备列表...', 'info');
            try {
                const devices = await apiRequest('/api/devices');
                log('✅ 设备列表获取成功', 'success');
                if (Array.isArray(devices)) {
                    log(`设备数量: ${devices.length}`, 'info');
                } else {
                    log(`响应: ${JSON.stringify(devices)}`, 'info');
                }
            } catch (error) {
                log(`❌ 获取设备列表失败: ${error.message}`, 'error');
            }
        }
        
        async function getTasks() {
            log('正在获取任务列表...', 'info');
            try {
                const tasks = await apiRequest('/api/tasks');
                log('✅ 任务列表获取成功', 'success');
                if (Array.isArray(tasks)) {
                    log(`任务数量: ${tasks.length}`, 'info');
                } else {
                    log(`响应: ${JSON.stringify(tasks)}`, 'info');
                }
            } catch (error) {
                log(`❌ 获取任务列表失败: ${error.message}`, 'error');
            }
        }
        
        async function getGroups() {
            log('正在获取分组列表...', 'info');
            try {
                const groups = await apiRequest('/api/groups');
                log('✅ 分组列表获取成功', 'success');
                if (Array.isArray(groups)) {
                    log(`分组数量: ${groups.length}`, 'info');
                } else {
                    log(`响应: ${JSON.stringify(groups)}`, 'info');
                }
            } catch (error) {
                log(`❌ 获取分组列表失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动测试连接
        window.onload = function() {
            log('页面加载完成', 'info');
            testConnection();
        };
    </script>
</body>
</html>
