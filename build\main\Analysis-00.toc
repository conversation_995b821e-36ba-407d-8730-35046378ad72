(['E:\\PythonWBYK\\fastApiProject\\app\\main.py'],
 ['E:\\PythonWBYK\\fastApiProject'],
 ['uvicorn.protocols.http',
  'uvicorn.protocols.websockets',
  'fastapi',
  'pydantic',
  'sqlalchemy',
  'pymysql',
  'fastapi.applications',
  'fastapi.routing',
  'fastapi.param_functions',
  'fastapi.dependencies.utils',
  'fastapi.encoders',
  'uvicorn.lifespan.on',
  'uvicorn.lifespan.off',
  'uvicorn.protocols.http.h11_impl',
  'uvicorn.protocols.websockets.websockets_impl',
  'sqlalchemy.ext.asyncio',
  'pymysql.constants.CLIENT'],
 [('E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('.env', 'E:\\PythonWBYK\\fastApiProject\\.env', 'DATA')],
 '3.9.13 (tags/v3.9.13:6de2ca5, May 17 2022, 16:36:42) [MSC v.1929 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('main', 'E:\\PythonWBYK\\fastApiProject\\app\\main.py', 'PYSOURCE')],
 [('subprocess', 'D:\\myProgram\\Python39\\lib\\subprocess.py', 'PYMODULE'),
  ('selectors', 'D:\\myProgram\\Python39\\lib\\selectors.py', 'PYMODULE'),
  ('contextlib', 'D:\\myProgram\\Python39\\lib\\contextlib.py', 'PYMODULE'),
  ('threading', 'D:\\myProgram\\Python39\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\myProgram\\Python39\\lib\\_threading_local.py',
   'PYMODULE'),
  ('signal', 'D:\\myProgram\\Python39\\lib\\signal.py', 'PYMODULE'),
  ('_strptime', 'D:\\myProgram\\Python39\\lib\\_strptime.py', 'PYMODULE'),
  ('calendar', 'D:\\myProgram\\Python39\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'D:\\myProgram\\Python39\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\myProgram\\Python39\\lib\\textwrap.py', 'PYMODULE'),
  ('shutil', 'D:\\myProgram\\Python39\\lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'D:\\myProgram\\Python39\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'D:\\myProgram\\Python39\\lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\myProgram\\Python39\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'D:\\myProgram\\Python39\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\myProgram\\Python39\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('typing', 'D:\\myProgram\\Python39\\lib\\typing.py', 'PYMODULE'),
  ('pathlib', 'D:\\myProgram\\Python39\\lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse',
   'D:\\myProgram\\Python39\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib', 'D:\\myProgram\\Python39\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('importlib.abc',
   'D:\\myProgram\\Python39\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib._common',
   'D:\\myProgram\\Python39\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('tempfile', 'D:\\myProgram\\Python39\\lib\\tempfile.py', 'PYMODULE'),
  ('random', 'D:\\myProgram\\Python39\\lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\myProgram\\Python39\\lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\myProgram\\Python39\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\myProgram\\Python39\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\myProgram\\Python39\\lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\myProgram\\Python39\\lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\myProgram\\Python39\\lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\myProgram\\Python39\\lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'D:\\myProgram\\Python39\\lib\\bisect.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\myProgram\\Python39\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\myProgram\\Python39\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\myProgram\\Python39\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('configparser', 'D:\\myProgram\\Python39\\lib\\configparser.py', 'PYMODULE'),
  ('email', 'D:\\myProgram\\Python39\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'D:\\myProgram\\Python39\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\myProgram\\Python39\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils', 'D:\\myProgram\\Python39\\lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr',
   'D:\\myProgram\\Python39\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\myProgram\\Python39\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'D:\\myProgram\\Python39\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\myProgram\\Python39\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\myProgram\\Python39\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\myProgram\\Python39\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'D:\\myProgram\\Python39\\lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\myProgram\\Python39\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\myProgram\\Python39\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\myProgram\\Python39\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\myProgram\\Python39\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\myProgram\\Python39\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\myProgram\\Python39\\lib\\getopt.py', 'PYMODULE'),
  ('quopri', 'D:\\myProgram\\Python39\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'D:\\myProgram\\Python39\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\myProgram\\Python39\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'D:\\myProgram\\Python39\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'D:\\myProgram\\Python39\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\myProgram\\Python39\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\myProgram\\Python39\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\myProgram\\Python39\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\myProgram\\Python39\\lib\\email\\errors.py',
   'PYMODULE'),
  ('csv', 'D:\\myProgram\\Python39\\lib\\csv.py', 'PYMODULE'),
  ('tokenize', 'D:\\myProgram\\Python39\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\myProgram\\Python39\\lib\\token.py', 'PYMODULE'),
  ('struct', 'D:\\myProgram\\Python39\\lib\\struct.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\myProgram\\Python39\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('tarfile', 'D:\\myProgram\\Python39\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\myProgram\\Python39\\lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'D:\\myProgram\\Python39\\lib\\_compression.py', 'PYMODULE'),
  ('lzma', 'D:\\myProgram\\Python39\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\myProgram\\Python39\\lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'D:\\myProgram\\Python39\\lib\\fnmatch.py', 'PYMODULE'),
  ('copy', 'D:\\myProgram\\Python39\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\myProgram\\Python39\\lib\\gettext.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\myProgram\\Python39\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\myProgram\\Python39\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\myProgram\\Python39\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\myProgram\\Python39\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\myProgram\\Python39\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\myProgram\\Python39\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'D:\\myProgram\\Python39\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\myProgram\\Python39\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\myProgram\\Python39\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\myProgram\\Python39\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'D:\\myProgram\\Python39\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'D:\\myProgram\\Python39\\lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\myProgram\\Python39\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'D:\\myProgram\\Python39\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.cookies',
   'D:\\myProgram\\Python39\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('ssl', 'D:\\myProgram\\Python39\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\myProgram\\Python39\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\myProgram\\Python39\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'D:\\myProgram\\Python39\\lib\\http\\client.py', 'PYMODULE'),
  ('hmac', 'D:\\myProgram\\Python39\\lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'D:\\myProgram\\Python39\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'D:\\myProgram\\Python39\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\myProgram\\Python39\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\myProgram\\Python39\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('pickle', 'D:\\myProgram\\Python39\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\myProgram\\Python39\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\myProgram\\Python39\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\myProgram\\Python39\\lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\myProgram\\Python39\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\myProgram\\Python39\\lib\\zipimport.py', 'PYMODULE'),
  ('inspect', 'D:\\myProgram\\Python39\\lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\myProgram\\Python39\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\myProgram\\Python39\\lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\myProgram\\Python39\\lib\\ast.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\myProgram\\Python39\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\myProgram\\Python39\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\myProgram\\Python39\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\myProgram\\Python39\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\myProgram\\Python39\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('__future__', 'D:\\myProgram\\Python39\\lib\\__future__.py', 'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\cyextension\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\row.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('dataclasses', 'D:\\myProgram\\Python39\\lib\\dataclasses.py', 'PYMODULE'),
  ('sqlalchemy.pool.events',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\pool\\events.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('uuid', 'D:\\myProgram\\Python39\\lib\\uuid.py', 'PYMODULE'),
  ('platform', 'D:\\myProgram\\Python39\\lib\\platform.py', 'PYMODULE'),
  ('json', 'D:\\myProgram\\Python39\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'D:\\myProgram\\Python39\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\myProgram\\Python39\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\myProgram\\Python39\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py',
   'PYMODULE'),
  ('greenlet',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\context.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\future\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\future\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('importlib_metadata',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.glob',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp._functools',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_typing.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\exc.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.websockets_impl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\websockets_impl.py',
   'PYMODULE'),
  ('uvicorn.server',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\server.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.wsproto_impl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\wsproto_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.httptools_impl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\httptools_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.flow_control',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\flow_control.py',
   'PYMODULE'),
  ('httptools',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\httptools\\__init__.py',
   'PYMODULE'),
  ('httptools._version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\httptools\\_version.py',
   'PYMODULE'),
  ('httptools.parser',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\httptools\\parser\\__init__.py',
   'PYMODULE'),
  ('httptools.parser.errors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\httptools\\parser\\errors.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('uvicorn',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\__init__.py',
   'PYMODULE'),
  ('uvicorn.workers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\workers.py',
   'PYMODULE'),
  ('uvicorn.supervisors.watchfilesreload',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\watchfilesreload.py',
   'PYMODULE'),
  ('watchfiles',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\watchfiles\\__init__.py',
   'PYMODULE'),
  ('watchfiles.version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\watchfiles\\version.py',
   'PYMODULE'),
  ('watchfiles.run',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\watchfiles\\run.py',
   'PYMODULE'),
  ('anyio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('exceptiongroup',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\exceptiongroup\\__init__.py',
   'PYMODULE'),
  ('exceptiongroup._suppress',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\exceptiongroup\\_suppress.py',
   'PYMODULE'),
  ('exceptiongroup._formatting',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\exceptiongroup\\_formatting.py',
   'PYMODULE'),
  ('exceptiongroup._exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\exceptiongroup\\_exceptions.py',
   'PYMODULE'),
  ('exceptiongroup._version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\exceptiongroup\\_version.py',
   'PYMODULE'),
  ('exceptiongroup._catch',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\exceptiongroup\\_catch.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio.streams',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('anyio.abc._eventloop',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\_eventloop.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('ipaddress', 'D:\\myProgram\\Python39\\lib\\ipaddress.py', 'PYMODULE'),
  ('anyio.from_thread',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio._core',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio.abc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\myProgram\\Python39\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\myProgram\\Python39\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\myProgram\\Python39\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\myProgram\\Python39\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\myProgram\\Python39\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio._core._asyncio_selector_thread',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_asyncio_selector_thread.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\myProgram\\Python39\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\myProgram\\Python39\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\myProgram\\Python39\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\myProgram\\Python39\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\myProgram\\Python39\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\myProgram\\Python39\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('sniffio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\myProgram\\Python39\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\myProgram\\Python39\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\myProgram\\Python39\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\myProgram\\Python39\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\myProgram\\Python39\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('anyio._backends',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._tempfile',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_tempfile.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('idna',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('watchfiles.main',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\watchfiles\\main.py',
   'PYMODULE'),
  ('watchfiles.filters',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\watchfiles\\filters.py',
   'PYMODULE'),
  ('uvicorn.supervisors.statreload',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\statreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.multiprocess',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\multiprocess.py',
   'PYMODULE'),
  ('uvicorn.supervisors.basereload',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\basereload.py',
   'PYMODULE'),
  ('uvicorn.supervisors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.auto',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.auto',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\auto.py',
   'PYMODULE'),
  ('uvicorn.protocols',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\__init__.py',
   'PYMODULE'),
  ('uvicorn.middleware.wsgi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\middleware\\wsgi.py',
   'PYMODULE'),
  ('uvicorn.middleware.proxy_headers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\middleware\\proxy_headers.py',
   'PYMODULE'),
  ('uvicorn.middleware.message_logger',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\middleware\\message_logger.py',
   'PYMODULE'),
  ('uvicorn.middleware.asgi2',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\middleware\\asgi2.py',
   'PYMODULE'),
  ('uvicorn.middleware',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\middleware\\__init__.py',
   'PYMODULE'),
  ('uvicorn.loops.uvloop',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\loops\\uvloop.py',
   'PYMODULE'),
  ('uvicorn.loops.auto',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\loops\\auto.py',
   'PYMODULE'),
  ('uvicorn.loops.asyncio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\loops\\asyncio.py',
   'PYMODULE'),
  ('uvicorn.loops',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\loops\\__init__.py',
   'PYMODULE'),
  ('uvicorn.lifespan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\lifespan\\__init__.py',
   'PYMODULE'),
  ('uvicorn.importer',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\importer.py',
   'PYMODULE'),
  ('uvicorn._subprocess',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\_subprocess.py',
   'PYMODULE'),
  ('uvicorn.__main__',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\__main__.py',
   'PYMODULE'),
  ('uvicorn.main',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\main.py',
   'PYMODULE'),
  ('click',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('glob', 'D:\\myProgram\\Python39\\lib\\glob.py', 'PYMODULE'),
  ('click._compat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('colorama',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\myProgram\\Python39\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('click._winconsole',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.termui',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('tty', 'D:\\myProgram\\Python39\\lib\\tty.py', 'PYMODULE'),
  ('webbrowser', 'D:\\myProgram\\Python39\\lib\\webbrowser.py', 'PYMODULE'),
  ('click.parser',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('difflib', 'D:\\myProgram\\Python39\\lib\\difflib.py', 'PYMODULE'),
  ('click.globals',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.formatting',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.decorators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.core',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.shell_completion',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('uvicorn.protocols.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\utils.py',
   'PYMODULE'),
  ('uvicorn.logging',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\logging.py',
   'PYMODULE'),
  ('uvicorn.config',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\config.py',
   'PYMODULE'),
  ('yaml',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.resolver',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.composer',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.parser',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.events',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.tokens',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yaml.error',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('dotenv',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.variables',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('dotenv.parser',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('logging.config',
   'D:\\myProgram\\Python39\\lib\\logging\\config.py',
   'PYMODULE'),
  ('socketserver', 'D:\\myProgram\\Python39\\lib\\socketserver.py', 'PYMODULE'),
  ('logging.handlers',
   'D:\\myProgram\\Python39\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('smtplib', 'D:\\myProgram\\Python39\\lib\\smtplib.py', 'PYMODULE'),
  ('uvicorn._types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\_types.py',
   'PYMODULE'),
  ('websockets.typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\typing.py',
   'PYMODULE'),
  ('websockets.server',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\server.py',
   'PYMODULE'),
  ('websockets.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\utils.py',
   'PYMODULE'),
  ('websockets.protocol',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\protocol.py',
   'PYMODULE'),
  ('websockets.streams',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\streams.py',
   'PYMODULE'),
  ('websockets.frames',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\frames.py',
   'PYMODULE'),
  ('websockets.imports',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\imports.py',
   'PYMODULE'),
  ('websockets.http11',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\http11.py',
   'PYMODULE'),
  ('websockets.version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\version.py',
   'PYMODULE'),
  ('websockets.headers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\headers.py',
   'PYMODULE'),
  ('websockets.extensions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\extensions\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\server.py',
   'PYMODULE'),
  ('websockets.legacy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\framing.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\http.py',
   'PYMODULE'),
  ('websockets.legacy.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\exceptions.py',
   'PYMODULE'),
  ('websockets.asyncio.compatibility',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\compatibility.py',
   'PYMODULE'),
  ('websockets.asyncio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\__init__.py',
   'PYMODULE'),
  ('websockets.asyncio.async_timeout',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\async_timeout.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\extensions\\permessage_deflate.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\extensions\\base.py',
   'PYMODULE'),
  ('websockets.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\exceptions.py',
   'PYMODULE'),
  ('websockets.datastructures',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\datastructures.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\handshake.py',
   'PYMODULE'),
  ('websockets',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\__init__.py',
   'PYMODULE'),
  ('websockets.uri',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\uri.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\sync\\utils.py',
   'PYMODULE'),
  ('websockets.sync.server',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\sync\\server.py',
   'PYMODULE'),
  ('websockets.sync.router',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\sync\\router.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\sync\\messages.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\sync\\connection.py',
   'PYMODULE'),
  ('websockets.sync.client',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\sync\\client.py',
   'PYMODULE'),
  ('websockets.sync',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\sync\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\client.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\auth.py',
   'PYMODULE'),
  ('websockets.http',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\http.py',
   'PYMODULE'),
  ('websockets.connection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\connection.py',
   'PYMODULE'),
  ('websockets.cli',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\cli.py',
   'PYMODULE'),
  ('websockets.auth',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\auth.py',
   'PYMODULE'),
  ('websockets.asyncio.messages',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\messages.py',
   'PYMODULE'),
  ('websockets.asyncio.connection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\connection.py',
   'PYMODULE'),
  ('websockets.__main__',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\__main__.py',
   'PYMODULE'),
  ('websockets.client',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\client.py',
   'PYMODULE'),
  ('websockets.asyncio.server',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\server.py',
   'PYMODULE'),
  ('websockets.asyncio.router',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\router.py',
   'PYMODULE'),
  ('websockets.asyncio.client',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\client.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.h11_impl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\h11_impl.py',
   'PYMODULE'),
  ('h11._connection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._writers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('h11._util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._state',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._readers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._abnf',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._headers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._events',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('uvicorn.lifespan.off',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\lifespan\\off.py',
   'PYMODULE'),
  ('uvicorn.lifespan.on',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\lifespan\\on.py',
   'PYMODULE'),
  ('fastapi.encoders',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\encoders.py',
   'PYMODULE'),
  ('fastapi._compat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\_compat.py',
   'PYMODULE'),
  ('pydantic.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\utils.py',
   'PYMODULE'),
  ('pydantic._migration',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_migration.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_validators.py',
   'PYMODULE'),
  ('pydantic._internal',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._namespace_utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_namespace_utils.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_generate_schema.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\__init__.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\version.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\types.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\typing.py',
   'PYMODULE'),
  ('pydantic.v1.validators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\utils.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\tools.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\parse.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\networks.py',
   'PYMODULE'),
  ('pydantic.v1.main',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\main.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\schema.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\json.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\color.py',
   'PYMODULE'),
  ('colorsys', 'D:\\myProgram\\Python39\\lib\\colorsys.py', 'PYMODULE'),
  ('pydantic.v1.fields',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\fields.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\env_settings.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\decorator.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\config.py',
   'PYMODULE'),
  ('pydantic.v1.class_validators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\errors.py',
   'PYMODULE'),
  ('pydantic._internal._serializers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_serializers.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._signature',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_signature.py',
   'PYMODULE'),
  ('pydantic.main',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\main.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\json.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\__init__.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\parse.py',
   'PYMODULE'),
  ('pydantic._internal._schema_gather',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_schema_gather.py',
   'PYMODULE'),
  ('pydantic._internal._docs_extraction',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_docs_extraction.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_core_utils.py',
   'PYMODULE'),
  ('pydantic.warnings',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\warnings.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\functional_validators.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\annotated_handlers.py',
   'PYMODULE'),
  ('pydantic.aliases',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\aliases.py',
   'PYMODULE'),
  ('typing_inspection.introspection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\typing_inspection\\introspection.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\plugin\\_schema_validator.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\plugin\\_loader.py',
   'PYMODULE'),
  ('pydantic.plugin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\plugin\\__init__.py',
   'PYMODULE'),
  ('pydantic.config',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\config.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py',
   'PYMODULE'),
  ('annotated_types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\annotated_types\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_model_construction.py',
   'PYMODULE'),
  ('pydantic.root_model',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\root_model.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\type_adapter.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_fields.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_generics.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_decorators.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_config.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_repr.py',
   'PYMODULE'),
  ('pydantic._internal._git',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_git.py',
   'PYMODULE'),
  ('pydantic._internal._import_utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_import_utils.py',
   'PYMODULE'),
  ('typing_inspection.typing_objects',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\typing_inspection\\typing_objects.py',
   'PYMODULE'),
  ('typing_inspection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\typing_inspection\\__init__.py',
   'PYMODULE'),
  ('zoneinfo',
   'D:\\myProgram\\Python39\\lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'D:\\myProgram\\Python39\\lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'D:\\myProgram\\Python39\\lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'D:\\myProgram\\Python39\\lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('sysconfig', 'D:\\myProgram\\Python39\\lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support', 'D:\\myProgram\\Python39\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'D:\\myProgram\\Python39\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('pydantic.typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\typing.py',
   'PYMODULE'),
  ('pydantic.schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\schema.py',
   'PYMODULE'),
  ('pydantic.errors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\errors.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\class_validators.py',
   'PYMODULE'),
  ('fastapi.openapi.constants',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\openapi\\constants.py',
   'PYMODULE'),
  ('fastapi.openapi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\openapi\\__init__.py',
   'PYMODULE'),
  ('fastapi.params',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\params.py',
   'PYMODULE'),
  ('fastapi.openapi.models',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\openapi\\models.py',
   'PYMODULE'),
  ('fastapi.logger',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\logger.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic_core\\core_schema.py',
   'PYMODULE'),
  ('pydantic_core',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic_core\\__init__.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\json_schema.py',
   'PYMODULE'),
  ('pydantic.fields',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\fields.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_utils.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_typing_extra.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py',
   'PYMODULE'),
  ('starlette.datastructures',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\datastructures.py',
   'PYMODULE'),
  ('starlette.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\types.py',
   'PYMODULE'),
  ('starlette.websockets',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\websockets.py',
   'PYMODULE'),
  ('starlette.responses',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\responses.py',
   'PYMODULE'),
  ('starlette.background',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\background.py',
   'PYMODULE'),
  ('starlette._utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\_utils.py',
   'PYMODULE'),
  ('starlette.requests',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\requests.py',
   'PYMODULE'),
  ('multipart.multipart',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\multipart\\multipart.py',
   'PYMODULE'),
  ('multipart',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\multipart\\__init__.py',
   'PYMODULE'),
  ('python_multipart',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\python_multipart\\__init__.py',
   'PYMODULE'),
  ('starlette.routing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\routing.py',
   'PYMODULE'),
  ('starlette.middleware',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\middleware\\__init__.py',
   'PYMODULE'),
  ('starlette.convertors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\convertors.py',
   'PYMODULE'),
  ('starlette._exception_handler',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\_exception_handler.py',
   'PYMODULE'),
  ('starlette.applications',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\applications.py',
   'PYMODULE'),
  ('starlette.middleware.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\middleware\\exceptions.py',
   'PYMODULE'),
  ('starlette.middleware.errors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\middleware\\errors.py',
   'PYMODULE'),
  ('html', 'D:\\myProgram\\Python39\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\myProgram\\Python39\\lib\\html\\entities.py',
   'PYMODULE'),
  ('starlette.middleware.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\middleware\\base.py',
   'PYMODULE'),
  ('python_multipart.multipart',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\python_multipart\\multipart.py',
   'PYMODULE'),
  ('python_multipart.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\python_multipart\\exceptions.py',
   'PYMODULE'),
  ('python_multipart.decoders',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\python_multipart\\decoders.py',
   'PYMODULE'),
  ('starlette.formparsers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\formparsers.py',
   'PYMODULE'),
  ('starlette.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\exceptions.py',
   'PYMODULE'),
  ('starlette',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\__init__.py',
   'PYMODULE'),
  ('starlette.status',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\status.py',
   'PYMODULE'),
  ('starlette.concurrency',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\concurrency.py',
   'PYMODULE'),
  ('pydantic.version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\version.py',
   'PYMODULE'),
  ('fastapi.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\exceptions.py',
   'PYMODULE'),
  ('pydantic.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\types.py',
   'PYMODULE'),
  ('pydantic.networks',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\networks.py',
   'PYMODULE'),
  ('pydantic.color',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\color.py',
   'PYMODULE'),
  ('fastapi.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\types.py',
   'PYMODULE'),
  ('fastapi.dependencies.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\dependencies\\utils.py',
   'PYMODULE'),
  ('fastapi.dependencies',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\dependencies\\__init__.py',
   'PYMODULE'),
  ('fastapi.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\utils.py',
   'PYMODULE'),
  ('fastapi.datastructures',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\datastructures.py',
   'PYMODULE'),
  ('fastapi.security.open_id_connect_url',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\security\\open_id_connect_url.py',
   'PYMODULE'),
  ('fastapi.security',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\security\\__init__.py',
   'PYMODULE'),
  ('fastapi.security.http',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\security\\http.py',
   'PYMODULE'),
  ('fastapi.security.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\security\\utils.py',
   'PYMODULE'),
  ('fastapi.security.api_key',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\security\\api_key.py',
   'PYMODULE'),
  ('fastapi.security.oauth2',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\security\\oauth2.py',
   'PYMODULE'),
  ('fastapi.security.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\security\\base.py',
   'PYMODULE'),
  ('fastapi.dependencies.models',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\dependencies\\models.py',
   'PYMODULE'),
  ('fastapi.concurrency',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\concurrency.py',
   'PYMODULE'),
  ('fastapi.background',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\background.py',
   'PYMODULE'),
  ('fastapi.param_functions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\param_functions.py',
   'PYMODULE'),
  ('fastapi.routing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\routing.py',
   'PYMODULE'),
  ('fastapi.applications',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\applications.py',
   'PYMODULE'),
  ('fastapi.openapi.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\openapi\\utils.py',
   'PYMODULE'),
  ('fastapi.responses',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\responses.py',
   'PYMODULE'),
  ('fastapi.openapi.docs',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\openapi\\docs.py',
   'PYMODULE'),
  ('fastapi.exception_handlers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\exception_handlers.py',
   'PYMODULE'),
  ('fastapi.websockets',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\websockets.py',
   'PYMODULE'),
  ('pymysql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql.connections',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.charset',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.converters',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql._auth',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('pymysql.times',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('pymysql.err',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('sqlalchemy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects._typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\myProgram\\Python39\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3', 'D:\\myProgram\\Python39\\lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dump',
   'D:\\myProgram\\Python39\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.operators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('psycopg2.extras',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\extras.py',
   'PYMODULE'),
  ('psycopg2.sql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\sql.py',
   'PYMODULE'),
  ('psycopg2._ipaddress',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\_ipaddress.py',
   'PYMODULE'),
  ('psycopg2._range',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\_range.py',
   'PYMODULE'),
  ('psycopg2._json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\_json.py',
   'PYMODULE'),
  ('psycopg2.extensions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\extensions.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.vector',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\vector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.asyncio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\connectors\\asyncio.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadb',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.aioodbc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.aioodbc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\connectors\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE'),
  ('psycopg2',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\__init__.py',
   'PYMODULE'),
  ('MySQLdb',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\__init__.py',
   'PYMODULE'),
  ('MySQLdb.connections',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\connections.py',
   'PYMODULE'),
  ('MySQLdb.converters',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\converters.py',
   'PYMODULE'),
  ('MySQLdb.constants.FLAG',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\constants\\FLAG.py',
   'PYMODULE'),
  ('MySQLdb.constants.CLIENT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\constants\\CLIENT.py',
   'PYMODULE'),
  ('MySQLdb._exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\_exceptions.py',
   'PYMODULE'),
  ('MySQLdb.cursors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\cursors.py',
   'PYMODULE'),
  ('MySQLdb.times',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\times.py',
   'PYMODULE'),
  ('MySQLdb.constants.FIELD_TYPE',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('MySQLdb.constants',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\constants\\__init__.py',
   'PYMODULE'),
  ('MySQLdb.release',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\release.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('pydantic',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\__init__.py',
   'PYMODULE'),
  ('pydantic.validators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.mypy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\mypy.py',
   'PYMODULE'),
  ('tomli',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\tomli\\__init__.py',
   'PYMODULE'),
  ('tomli._parser',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\tomli\\_parser.py',
   'PYMODULE'),
  ('tomli._types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\tomli\\_types.py',
   'PYMODULE'),
  ('tomli._re',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\tomli\\_re.py',
   'PYMODULE'),
  ('pydantic.v1.generics',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\generics.py',
   'PYMODULE'),
  ('pydantic.v1._hypothesis_plugin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\_hypothesis_plugin.py',
   'PYMODULE'),
  ('pydantic.tools',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\tools.py',
   'PYMODULE'),
  ('pydantic.parse',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\parse.py',
   'PYMODULE'),
  ('pydantic.mypy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\mypy.py',
   'PYMODULE'),
  ('pydantic.json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\json.py',
   'PYMODULE'),
  ('pydantic.generics',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\generics.py',
   'PYMODULE'),
  ('pydantic.experimental.pipeline',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\experimental\\pipeline.py',
   'PYMODULE'),
  ('pydantic.experimental.arguments_schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\experimental\\arguments_schema.py',
   'PYMODULE'),
  ('pydantic.experimental',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\experimental\\__init__.py',
   'PYMODULE'),
  ('pydantic.env_settings',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\env_settings.py',
   'PYMODULE'),
  ('pydantic.deprecated.decorator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\decorator.py',
   'PYMODULE'),
  ('pydantic.decorator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\decorator.py',
   'PYMODULE'),
  ('pydantic.datetime_parse',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.alias_generators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\alias_generators.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\tools.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\config.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\class_validators.py',
   'PYMODULE'),
  ('pydantic.validate_call_decorator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\validate_call_decorator.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\functional_serializers.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.http',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\__init__.py',
   'PYMODULE'),
  ('stringprep', 'D:\\myProgram\\Python39\\lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\myProgram\\Python39\\lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'D:\\myProgram\\Python39\\lib\\_py_abc.py', 'PYMODULE'),
  ('datetime', 'D:\\myProgram\\Python39\\lib\\datetime.py', 'PYMODULE'),
  ('socket', 'D:\\myProgram\\Python39\\lib\\socket.py', 'PYMODULE'),
  ('app.routes.timeout_monitor',
   'E:\\PythonWBYK\\fastApiProject\\app\\routes\\timeout_monitor.py',
   'PYMODULE'),
  ('app.models.task',
   'E:\\PythonWBYK\\fastApiProject\\app\\models\\task.py',
   'PYMODULE'),
  ('app.models',
   'E:\\PythonWBYK\\fastApiProject\\app\\models\\__init__.py',
   'PYMODULE'),
  ('app.models.group_task_status',
   'E:\\PythonWBYK\\fastApiProject\\app\\models\\group_task_status.py',
   'PYMODULE'),
  ('app.models.group',
   'E:\\PythonWBYK\\fastApiProject\\app\\models\\group.py',
   'PYMODULE'),
  ('app.models.device_status',
   'E:\\PythonWBYK\\fastApiProject\\app\\models\\device_status.py',
   'PYMODULE'),
  ('app.models.device',
   'E:\\PythonWBYK\\fastApiProject\\app\\models\\device.py',
   'PYMODULE'),
  ('app', 'E:\\PythonWBYK\\fastApiProject\\app\\__init__.py', 'PYMODULE'),
  ('app.crud', 'E:\\PythonWBYK\\fastApiProject\\app\\crud.py', 'PYMODULE'),
  ('app.schemas',
   'E:\\PythonWBYK\\fastApiProject\\app\\schemas\\__init__.py',
   'PYMODULE'),
  ('app.schemas.group',
   'E:\\PythonWBYK\\fastApiProject\\app\\schemas\\group.py',
   'PYMODULE'),
  ('app.schemas.task',
   'E:\\PythonWBYK\\fastApiProject\\app\\schemas\\task.py',
   'PYMODULE'),
  ('app.schemas.device',
   'E:\\PythonWBYK\\fastApiProject\\app\\schemas\\device.py',
   'PYMODULE'),
  ('logging', 'D:\\myProgram\\Python39\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('asyncio', 'D:\\myProgram\\Python39\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\myProgram\\Python39\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\myProgram\\Python39\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\myProgram\\Python39\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\myProgram\\Python39\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\myProgram\\Python39\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\myProgram\\Python39\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('app.services.task_timeout_monitor',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\task_timeout_monitor.py',
   'PYMODULE'),
  ('app.services',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\__init__.py',
   'PYMODULE'),
  ('app.utils.time_utils',
   'E:\\PythonWBYK\\fastApiProject\\app\\utils\\time_utils.py',
   'PYMODULE'),
  ('app.utils',
   'E:\\PythonWBYK\\fastApiProject\\app\\utils\\__init__.py',
   'PYMODULE'),
  ('app.utils.exception_handler',
   'E:\\PythonWBYK\\fastApiProject\\app\\utils\\exception_handler.py',
   'PYMODULE'),
  ('pytz',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('doctest', 'D:\\myProgram\\Python39\\lib\\doctest.py', 'PYMODULE'),
  ('unittest',
   'D:\\myProgram\\Python39\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\myProgram\\Python39\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\myProgram\\Python39\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\myProgram\\Python39\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\myProgram\\Python39\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\myProgram\\Python39\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\myProgram\\Python39\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\myProgram\\Python39\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\myProgram\\Python39\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\myProgram\\Python39\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\myProgram\\Python39\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('pdb', 'D:\\myProgram\\Python39\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'D:\\myProgram\\Python39\\lib\\pydoc.py', 'PYMODULE'),
  ('http.server', 'D:\\myProgram\\Python39\\lib\\http\\server.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\myProgram\\Python39\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\myProgram\\Python39\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('code', 'D:\\myProgram\\Python39\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\myProgram\\Python39\\lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'D:\\myProgram\\Python39\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\myProgram\\Python39\\lib\\cmd.py', 'PYMODULE'),
  ('pytz.tzfile',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('app.services.optimized_scheduler',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\optimized_scheduler.py',
   'PYMODULE'),
  ('app.services.task_sync',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\task_sync.py',
   'PYMODULE'),
  ('app.services.scheduler',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\scheduler.py',
   'PYMODULE'),
  ('app.config', 'E:\\PythonWBYK\\fastApiProject\\app\\config.py', 'PYMODULE'),
  ('app.websocket.ws_manager',
   'E:\\PythonWBYK\\fastApiProject\\app\\websocket\\ws_manager.py',
   'PYMODULE'),
  ('app.websocket', '-', 'PYMODULE'),
  ('app.routes.task_sync',
   'E:\\PythonWBYK\\fastApiProject\\app\\routes\\task_sync.py',
   'PYMODULE'),
  ('app.routes.group',
   'E:\\PythonWBYK\\fastApiProject\\app\\routes\\group.py',
   'PYMODULE'),
  ('app.services.group',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\group.py',
   'PYMODULE'),
  ('app.routes.task',
   'E:\\PythonWBYK\\fastApiProject\\app\\routes\\task.py',
   'PYMODULE'),
  ('app.services.task_dispatcher',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\task_dispatcher.py',
   'PYMODULE'),
  ('app.services.dispatcher',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\dispatcher.py',
   'PYMODULE'),
  ('app.routes.device',
   'E:\\PythonWBYK\\fastApiProject\\app\\routes\\device.py',
   'PYMODULE'),
  ('app.services.device',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\device.py',
   'PYMODULE'),
  ('app.routes', '-', 'PYMODULE'),
  ('app.db', 'E:\\PythonWBYK\\fastApiProject\\app\\db.py', 'PYMODULE'),
  ('fastapi.middleware.cors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\middleware\\cors.py',
   'PYMODULE'),
  ('fastapi.middleware',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\middleware\\__init__.py',
   'PYMODULE'),
  ('starlette.middleware.cors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\middleware\\cors.py',
   'PYMODULE'),
  ('fastapi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\__init__.py',
   'PYMODULE'),
  ('fastapi.requests',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\requests.py',
   'PYMODULE')],
 [('python39.dll', 'D:\\myProgram\\Python39\\python39.dll', 'BINARY'),
  ('select.pyd', 'D:\\myProgram\\Python39\\DLLs\\select.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\myProgram\\Python39\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd', 'D:\\myProgram\\Python39\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\myProgram\\Python39\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\myProgram\\Python39\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\myProgram\\Python39\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\myProgram\\Python39\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\myProgram\\Python39\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\myProgram\\Python39\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\myProgram\\Python39\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\myProgram\\Python39\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\myProgram\\Python39\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\cyextension\\util.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\cyextension\\processors.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\cyextension\\collections.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\myProgram\\Python39\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('greenlet\\_greenlet.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\greenlet\\_greenlet.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('httptools\\parser\\url_parser.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\httptools\\parser\\url_parser.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('httptools\\parser\\parser.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\httptools\\parser\\parser.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_asyncio.pyd', 'D:\\myProgram\\Python39\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('watchfiles\\_rust_notify.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\watchfiles\\_rust_notify.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\_yaml.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('websockets\\speedups.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\speedups.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('pydantic_core\\_pydantic_core.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic_core\\_pydantic_core.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_zoneinfo.pyd',
   'D:\\myProgram\\Python39\\DLLs\\_zoneinfo.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\_cffi_backend.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'D:\\myProgram\\Python39\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('psycopg2\\_psycopg.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\_psycopg.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('MySQLdb\\_mysql.cp39-win_amd64.pyd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\_mysql.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\myProgram\\Python39\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\myProgram\\Python39\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\myProgram\\Python39\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll', 'D:\\myProgram\\Python39\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libffi-7.dll', 'D:\\myProgram\\Python39\\DLLs\\libffi-7.dll', 'BINARY'),
  ('python3.dll', 'D:\\myProgram\\Python39\\python3.dll', 'BINARY'),
  ('sqlite3.dll', 'D:\\myProgram\\Python39\\DLLs\\sqlite3.dll', 'BINARY')],
 [],
 [],
 [('.env', 'E:\\PythonWBYK\\fastApiProject\\.env', 'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\licenses\\LICENSE',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\WHEEL',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\INSTALLER',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\RECORD',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\METADATA',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-8.7.0.dist-info\\top_level.txt',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata-8.7.0.dist-info\\top_level.txt',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\WHEEL',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\METADATA',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\RECORD',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\REQUESTED',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\INSTALLER',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography-45.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('websockets-15.0.1.dist-info\\LICENSE',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\LICENSE',
   'DATA'),
  ('websockets-15.0.1.dist-info\\RECORD',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\RECORD',
   'DATA'),
  ('websockets-15.0.1.dist-info\\top_level.txt',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('websockets-15.0.1.dist-info\\INSTALLER',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\entry_points.txt',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\entry_points.txt',
   'DATA'),
  ('websockets-15.0.1.dist-info\\METADATA',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\METADATA',
   'DATA'),
  ('websockets-15.0.1.dist-info\\WHEEL',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\WHEEL',
   'DATA'),
  ('base_library.zip',
   'E:\\PythonWBYK\\fastApiProject\\build\\main\\base_library.zip',
   'DATA')],
 [('weakref', 'D:\\myProgram\\Python39\\lib\\weakref.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\myProgram\\Python39\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\myProgram\\Python39\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\myProgram\\Python39\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\myProgram\\Python39\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\myProgram\\Python39\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\myProgram\\Python39\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\myProgram\\Python39\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\myProgram\\Python39\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\myProgram\\Python39\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\myProgram\\Python39\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\myProgram\\Python39\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\myProgram\\Python39\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\myProgram\\Python39\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\myProgram\\Python39\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\myProgram\\Python39\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\myProgram\\Python39\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\myProgram\\Python39\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\myProgram\\Python39\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\myProgram\\Python39\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\myProgram\\Python39\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\myProgram\\Python39\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\myProgram\\Python39\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\myProgram\\Python39\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\myProgram\\Python39\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\myProgram\\Python39\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\myProgram\\Python39\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\myProgram\\Python39\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\myProgram\\Python39\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\myProgram\\Python39\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\myProgram\\Python39\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\myProgram\\Python39\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\myProgram\\Python39\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\myProgram\\Python39\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\myProgram\\Python39\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\myProgram\\Python39\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\myProgram\\Python39\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\myProgram\\Python39\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\myProgram\\Python39\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\myProgram\\Python39\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\myProgram\\Python39\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\myProgram\\Python39\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\myProgram\\Python39\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\myProgram\\Python39\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\myProgram\\Python39\\lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\myProgram\\Python39\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\myProgram\\Python39\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\myProgram\\Python39\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\myProgram\\Python39\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\myProgram\\Python39\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\myProgram\\Python39\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\myProgram\\Python39\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\myProgram\\Python39\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\myProgram\\Python39\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\myProgram\\Python39\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\myProgram\\Python39\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\myProgram\\Python39\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\myProgram\\Python39\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\myProgram\\Python39\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\myProgram\\Python39\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\myProgram\\Python39\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\myProgram\\Python39\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\myProgram\\Python39\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('_collections_abc',
   'D:\\myProgram\\Python39\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('_weakrefset', 'D:\\myProgram\\Python39\\lib\\_weakrefset.py', 'PYMODULE'),
  ('sre_compile', 'D:\\myProgram\\Python39\\lib\\sre_compile.py', 'PYMODULE'),
  ('copyreg', 'D:\\myProgram\\Python39\\lib\\copyreg.py', 'PYMODULE'),
  ('posixpath', 'D:\\myProgram\\Python39\\lib\\posixpath.py', 'PYMODULE'),
  ('stat', 'D:\\myProgram\\Python39\\lib\\stat.py', 'PYMODULE'),
  ('enum', 'D:\\myProgram\\Python39\\lib\\enum.py', 'PYMODULE'),
  ('heapq', 'D:\\myProgram\\Python39\\lib\\heapq.py', 'PYMODULE'),
  ('re', 'D:\\myProgram\\Python39\\lib\\re.py', 'PYMODULE'),
  ('reprlib', 'D:\\myProgram\\Python39\\lib\\reprlib.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\myProgram\\Python39\\lib\\sre_constants.py',
   'PYMODULE'),
  ('types', 'D:\\myProgram\\Python39\\lib\\types.py', 'PYMODULE'),
  ('locale', 'D:\\myProgram\\Python39\\lib\\locale.py', 'PYMODULE'),
  ('linecache', 'D:\\myProgram\\Python39\\lib\\linecache.py', 'PYMODULE'),
  ('warnings', 'D:\\myProgram\\Python39\\lib\\warnings.py', 'PYMODULE'),
  ('os', 'D:\\myProgram\\Python39\\lib\\os.py', 'PYMODULE'),
  ('traceback', 'D:\\myProgram\\Python39\\lib\\traceback.py', 'PYMODULE'),
  ('codecs', 'D:\\myProgram\\Python39\\lib\\codecs.py', 'PYMODULE'),
  ('functools', 'D:\\myProgram\\Python39\\lib\\functools.py', 'PYMODULE'),
  ('io', 'D:\\myProgram\\Python39\\lib\\io.py', 'PYMODULE'),
  ('keyword', 'D:\\myProgram\\Python39\\lib\\keyword.py', 'PYMODULE'),
  ('collections.abc',
   'D:\\myProgram\\Python39\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\myProgram\\Python39\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('genericpath', 'D:\\myProgram\\Python39\\lib\\genericpath.py', 'PYMODULE'),
  ('_bootlocale', 'D:\\myProgram\\Python39\\lib\\_bootlocale.py', 'PYMODULE'),
  ('operator', 'D:\\myProgram\\Python39\\lib\\operator.py', 'PYMODULE'),
  ('sre_parse', 'D:\\myProgram\\Python39\\lib\\sre_parse.py', 'PYMODULE'),
  ('abc', 'D:\\myProgram\\Python39\\lib\\abc.py', 'PYMODULE'),
  ('ntpath', 'D:\\myProgram\\Python39\\lib\\ntpath.py', 'PYMODULE')])
