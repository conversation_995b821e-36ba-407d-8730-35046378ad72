#!/bin/bash

# 安装完整的运行环境

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

echo "========================================"
echo "  安装微博任务管理系统运行环境"
echo "========================================"
echo ""

# 1. 更新系统
log_step "1. 更新系统包"
sudo dnf update -y

# 2. 安装基础工具
log_step "2. 安装基础工具"
sudo dnf install -y wget curl git vim

# 3. 安装Python3
log_step "3. 安装Python3"

if command -v python3 >/dev/null 2>&1; then
    log_info "Python3 已安装: $(python3 --version)"
else
    log_info "正在安装Python3..."
    
    # 尝试不同的安装方法
    if sudo dnf install -y python3 python3-pip python3-venv python3-devel; then
        log_info "✅ Python3 安装成功"
    elif sudo dnf install -y python39 python39-pip python39-devel; then
        log_info "✅ Python3.9 安装成功"
        sudo ln -sf /usr/bin/python3.9 /usr/bin/python3
        sudo ln -sf /usr/bin/pip3.9 /usr/bin/pip3
    else
        log_error "❌ Python3 安装失败，尝试从源码编译..."
        
        # 安装编译依赖
        sudo dnf groupinstall -y "Development Tools"
        sudo dnf install -y openssl-devel bzip2-devel libffi-devel zlib-devel
        
        # 下载并编译Python
        cd /tmp
        wget https://www.python.org/ftp/python/3.11.0/Python-3.11.0.tgz
        tar -xzf Python-3.11.0.tgz
        cd Python-3.11.0
        ./configure --enable-optimizations
        make -j$(nproc)
        sudo make altinstall
        
        # 创建软链接
        sudo ln -sf /usr/local/bin/python3.11 /usr/bin/python3
        sudo ln -sf /usr/local/bin/pip3.11 /usr/bin/pip3
        
        log_info "✅ Python3 从源码编译安装成功"
    fi
fi

# 验证Python安装
python3 --version
pip3 --version

# 4. 安装MySQL开发库
log_step "4. 安装MySQL开发库"
sudo dnf install -y mysql-devel gcc pkg-config || \
sudo dnf install -y mariadb-devel gcc pkg-config || \
log_warn "MySQL开发库安装失败，可能影响pymysql安装"

# 5. 安装Docker
log_step "5. 安装Docker"

if command -v docker >/dev/null 2>&1; then
    log_info "Docker 已安装"
else
    log_info "正在安装Docker..."
    
    # 安装Docker
    sudo dnf install -y dnf-plugins-core
    sudo dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
    sudo dnf install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # 启动Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # 添加当前用户到docker组
    sudo usermod -aG docker $USER
    
    log_info "✅ Docker 安装成功"
fi

# 6. 安装Docker Compose
log_step "6. 安装Docker Compose"

if command -v docker-compose >/dev/null 2>&1; then
    log_info "Docker Compose 已安装"
else
    log_info "正在安装Docker Compose..."
    
    # 下载Docker Compose
    sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    
    # 创建软链接
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    log_info "✅ Docker Compose 安装成功"
fi

# 7. 配置防火墙
log_step "7. 配置防火墙"

if systemctl is-active --quiet firewalld; then
    log_info "配置防火墙规则..."
    sudo firewall-cmd --permanent --add-port=11234/tcp
    sudo firewall-cmd --permanent --add-port=3306/tcp
    sudo firewall-cmd --reload
    log_info "✅ 防火墙配置完成"
else
    log_warn "防火墙未运行，跳过配置"
fi

# 8. 创建项目环境
log_step "8. 创建项目环境"

cd /www/wwwroot/fastApiProject

# 创建虚拟环境
if [ ! -d "venv" ]; then
    log_info "创建Python虚拟环境..."
    python3 -m venv venv
    log_info "✅ 虚拟环境创建成功"
fi

# 激活虚拟环境并安装依赖
log_info "安装Python依赖..."
source venv/bin/activate
pip install --upgrade pip

# 安装依赖
if pip install -r requirements.txt; then
    log_info "✅ Python依赖安装成功"
else
    log_error "❌ Python依赖安装失败"
    log_info "尝试单独安装关键依赖..."
    pip install fastapi uvicorn sqlalchemy pymysql python-dotenv pydantic
fi

# 9. 测试环境
log_step "9. 测试环境"

log_info "测试Python环境..."
python3 -c "
import sys
print(f'Python版本: {sys.version}')

try:
    import fastapi
    print(f'FastAPI版本: {fastapi.__version__}')
except ImportError:
    print('FastAPI未安装')

try:
    import sqlalchemy
    print(f'SQLAlchemy版本: {sqlalchemy.__version__}')
except ImportError:
    print('SQLAlchemy未安装')

try:
    import pymysql
    print(f'PyMySQL版本: {pymysql.__version__}')
except ImportError:
    print('PyMySQL未安装')
"

log_info "测试Docker..."
docker --version
docker-compose --version

echo ""
echo "========================================"
echo "  环境安装完成！"
echo "========================================"
echo ""
log_info "已安装组件:"
echo "  ✅ Python3: $(python3 --version)"
echo "  ✅ Docker: $(docker --version)"
echo "  ✅ Docker Compose: $(docker-compose --version)"
echo ""
log_info "下一步:"
echo "  1. 重新登录以应用Docker组权限"
echo "  2. 运行: ./deploy_db_only.sh"
echo "  3. 启动: ./start_backend.sh"
echo ""
echo "🎉 环境安装完成！"
