#!/usr/bin/env python3
"""
测试设置功能
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'frontend'))

import tkinter as tk
from tkinter import ttk

def test_config_manager():
    """测试配置管理器"""
    print("🔧 测试配置管理器...")
    
    try:
        from utils.config_manager import config_manager
        
        # 测试默认配置
        print(f"默认API URL: {config_manager.get_api_base_url()}")
        print(f"默认WebSocket URL: {config_manager.get_websocket_url()}")
        
        # 测试配置更新
        config_manager.update_server_config("192.168.1.100", 8080, True)
        print(f"更新后API URL: {config_manager.get_api_base_url()}")
        print(f"更新后WebSocket URL: {config_manager.get_websocket_url()}")
        
        # 测试保存配置
        if config_manager.save_config():
            print("✅ 配置保存成功")
        else:
            print("❌ 配置保存失败")
            
        print("✅ 配置管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def test_settings_dialog():
    """测试设置对话框"""
    print("\n🎨 测试设置对话框...")
    
    try:
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        from widgets.settings_dialog import SettingsDialog
        
        dialog = SettingsDialog(root)
        print("✅ 设置对话框创建成功")
        
        # 不实际显示对话框，只测试创建
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 设置对话框测试失败: {e}")
        return False

def test_config_integration():
    """测试配置集成"""
    print("\n🔗 测试配置集成...")
    
    try:
        from config import Config
        
        # 测试静态方法
        api_url = Config.get_api_base_url()
        ws_url = Config.get_websocket_url()
        
        print(f"Config API URL: {api_url}")
        print(f"Config WebSocket URL: {ws_url}")
        
        print("✅ 配置集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 设置功能测试")
    print("="*50)
    
    tests = [
        test_config_manager,
        test_settings_dialog,
        test_config_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！设置功能可以正常使用。")
        print("\n📝 使用说明:")
        print("1. 启动前端应用")
        print("2. 点击工具栏的 '⚙️ 设置' 按钮")
        print("3. 在设置界面中配置服务器地址")
        print("4. 点击 '🧪 测试连接' 验证连接")
        print("5. 点击 '✅ 确定' 保存配置")
        print("6. 重启应用以应用新配置")
    else:
        print("❌ 部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
