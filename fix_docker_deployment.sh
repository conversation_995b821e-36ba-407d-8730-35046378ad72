#!/bin/bash

# 修复Docker部署

echo "=== 修复Docker部署 ==="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. 停止原生服务
log_info "1. 停止原生服务..."
sudo systemctl stop wb-proxy 2>/dev/null || true
sudo pkill -f "uvicorn.*8000" 2>/dev/null || true
sudo pkill -f "proxy_server" 2>/dev/null || true

# 2. 清理Docker环境
log_info "2. 清理Docker环境..."
docker-compose down 2>/dev/null || true
docker system prune -f >/dev/null 2>&1

# 3. 修复Dockerfile
log_info "3. 修复Dockerfile..."
cat > Dockerfile << 'EOF'
FROM python:3.9-slim

WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF

# 4. 修复docker-compose.yml
log_info "4. 修复docker-compose.yml..."
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: wb_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: wb
      MYSQL_USER: wb_user
      MYSQL_PASSWORD: wb_password
      MYSQL_ROOT_HOST: '%'
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - wb_network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123456"]
      timeout: 20s
      retries: 10

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: wb_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - wb_network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 20s
      retries: 10

  # 后端API服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wb_backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      # 数据库配置
      DATABASE_URL: mysql+pymysql://wb_user:wb_password@mysql:3306/wb
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_USER: wb_user
      MYSQL_PASSWORD: wb_password
      MYSQL_DATABASE: wb
      
      # Redis配置
      REDIS_URL: redis://redis:6379/0
      
      # 应用配置
      DEBUG: "false"
      LOG_LEVEL: "info"
      
    volumes:
      - ./logs:/app/logs
    networks:
      - wb_network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      timeout: 20s
      retries: 10
      start_period: 30s

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: wb_nginx
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
    networks:
      - wb_network
    depends_on:
      - backend

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  wb_network:
    driver: bridge
EOF

# 5. 创建Nginx配置
log_info "5. 创建Nginx配置..."
mkdir -p nginx/conf.d
cat > nginx/conf.d/default.conf << 'EOF'
server {
    listen 80;
    server_name _;

    # 后端API代理
    location / {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket代理
    location /ws {
        proxy_pass http://backend:8000/ws;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# 6. 启动服务
log_info "6. 启动Docker服务..."

# 先启动数据库
log_info "启动数据库服务..."
docker-compose up -d mysql redis

# 等待数据库启动
log_info "等待数据库启动..."
sleep 20

# 检查数据库状态
for i in {1..30}; do
    if docker-compose exec -T mysql mysqladmin ping -h localhost -u root -p123456 >/dev/null 2>&1; then
        log_info "✅ MySQL已启动"
        break
    fi
    echo "等待MySQL启动... ($i/30)"
    sleep 2
done

# 构建并启动后端
log_info "构建并启动后端..."
export DOCKER_BUILDKIT=0
docker-compose up -d backend

# 等待后端启动
log_info "等待后端启动..."
sleep 30

# 启动Nginx
log_info "启动Nginx..."
docker-compose up -d nginx

# 7. 检查服务状态
log_info "7. 检查服务状态..."
docker-compose ps

echo ""
log_info "健康检查..."
for i in {1..20}; do
    if curl -s http://localhost/health >/dev/null 2>&1; then
        log_info "✅ 服务启动成功！"
        echo ""
        echo "🎉 Docker部署完成！"
        echo ""
        echo "访问地址："
        echo "  - 主页: http://***********/"
        echo "  - 健康检查: http://***********/health"
        echo "  - API文档: http://***********/docs"
        echo ""
        echo "管理命令："
        echo "  - 查看状态: docker-compose ps"
        echo "  - 查看日志: docker-compose logs -f"
        echo "  - 停止服务: docker-compose down"
        echo "  - 重启服务: docker-compose restart"
        break
    fi
    echo "等待服务启动... ($i/20)"
    sleep 3
done

echo ""
echo "=== Docker部署修复完成 ==="
