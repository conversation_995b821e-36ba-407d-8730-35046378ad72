#!/usr/bin/env python3
"""
预下载Python依赖包脚本
用于解决Docker构建时网络连接问题
"""

import os
import subprocess
import sys
from pathlib import Path

def create_wheels_dir():
    """创建wheels目录"""
    wheels_dir = Path("wheels")
    wheels_dir.mkdir(exist_ok=True)
    return wheels_dir

def download_wheels():
    """下载所有依赖包的wheel文件"""
    wheels_dir = create_wheels_dir()
    
    print("开始下载Python依赖包...")
    
    # 读取requirements.txt
    if not Path("requirements.txt").exists():
        print("错误：找不到requirements.txt文件")
        return False
    
    # 尝试多个镜像源
    mirrors = [
        "https://pypi.tuna.tsinghua.edu.cn/simple",
        "https://mirrors.aliyun.com/pypi/simple/",
        "https://pypi.douban.com/simple/",
        "https://pypi.python.org/simple/"
    ]
    
    for mirror in mirrors:
        print(f"尝试使用镜像源: {mirror}")
        
        cmd = [
            sys.executable, "-m", "pip", "download",
            "--no-deps",  # 不下载依赖的依赖
            "--dest", str(wheels_dir),
            "--index-url", mirror,
            "--trusted-host", mirror.split("//")[1].split("/")[0],
            "-r", "requirements.txt"
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("下载成功！")
            print(f"输出: {result.stdout}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"下载失败: {e}")
            print(f"错误输出: {e.stderr}")
            continue
    
    print("所有镜像源都失败了")
    return False

def download_with_dependencies():
    """下载包含所有依赖的wheel文件"""
    wheels_dir = create_wheels_dir()
    
    print("下载包含依赖的wheel文件...")
    
    mirrors = [
        "https://pypi.tuna.tsinghua.edu.cn/simple",
        "https://mirrors.aliyun.com/pypi/simple/",
        "https://pypi.douban.com/simple/"
    ]
    
    for mirror in mirrors:
        print(f"尝试使用镜像源: {mirror}")
        
        cmd = [
            sys.executable, "-m", "pip", "download",
            "--dest", str(wheels_dir),
            "--index-url", mirror,
            "--trusted-host", mirror.split("//")[1].split("/")[0],
            "-r", "requirements.txt"
        ]
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("下载成功！")
            return True
        except subprocess.CalledProcessError as e:
            print(f"下载失败: {e}")
            continue
    
    return False

def main():
    """主函数"""
    print("Python依赖包下载工具")
    print("=" * 40)
    
    # 检查pip版本
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True, check=True)
        print(f"Pip版本: {result.stdout.strip()}")
    except subprocess.CalledProcessError:
        print("错误：无法找到pip")
        return False
    
    # 升级pip
    print("升级pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        print("pip升级成功")
    except subprocess.CalledProcessError as e:
        print(f"pip升级失败: {e}")
    
    # 先尝试下载包含依赖的版本
    if download_with_dependencies():
        print("\n✅ 依赖包下载完成！")
        print("现在可以使用 Dockerfile.offline 进行离线构建")
        
        # 显示下载的文件
        wheels_dir = Path("wheels")
        files = list(wheels_dir.glob("*.whl"))
        print(f"\n下载的文件数量: {len(files)}")
        for file in files[:10]:  # 只显示前10个
            print(f"  - {file.name}")
        if len(files) > 10:
            print(f"  ... 还有 {len(files) - 10} 个文件")
        
        return True
    else:
        print("\n❌ 依赖包下载失败")
        print("请检查网络连接或尝试手动下载")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
