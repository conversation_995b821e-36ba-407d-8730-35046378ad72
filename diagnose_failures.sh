#!/bin/bash

# 详细诊断服务启动失败的原因

echo "=== 服务启动失败诊断 ==="
echo "系统信息: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
echo "内核版本: $(uname -r)"
echo "时间: $(date)"
echo ""

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 1. 检查系统资源
echo "1. 系统资源检查"
echo "=================="
log_info "内存使用情况:"
free -h

log_info "磁盘空间:"
df -h /

log_info "CPU信息:"
lscpu | grep -E "Model name|CPU\(s\):"

echo ""

# 2. 检查软件包安装状态
echo "2. 软件包安装状态"
echo "=================="

log_info "检查MySQL相关包:"
dnf list installed | grep -i mysql || echo "  未找到MySQL包"

log_info "检查MariaDB相关包:"
dnf list installed | grep -i mariadb || echo "  未找到MariaDB包"

log_info "检查Redis相关包:"
dnf list installed | grep -i redis || echo "  未找到Redis包"

echo ""

# 3. 检查systemd服务状态
echo "3. Systemd服务状态"
echo "=================="

services=("mysqld" "mariadb" "redis")

for service in "${services[@]}"; do
    log_info "检查 $service 服务:"
    
    if systemctl list-unit-files | grep -q "^$service.service"; then
        echo "  ✅ 服务文件存在"
        
        # 检查服务状态
        status=$(systemctl is-active $service 2>/dev/null || echo "inactive")
        enabled=$(systemctl is-enabled $service 2>/dev/null || echo "disabled")
        
        echo "  状态: $status"
        echo "  启用: $enabled"
        
        # 如果服务失败，显示详细信息
        if [ "$status" = "failed" ]; then
            log_error "$service 服务失败，详细信息:"
            systemctl status $service --no-pager -l
            echo ""
            log_error "$service 最近的日志:"
            journalctl -u $service --no-pager -n 10
            echo ""
        fi
    else
        echo "  ❌ 服务文件不存在"
    fi
    echo ""
done

# 4. 检查端口占用
echo "4. 端口占用检查"
echo "==============="

ports=("3306:MySQL" "6379:Redis" "8000:应用")

for port_info in "${ports[@]}"; do
    port=$(echo $port_info | cut -d: -f1)
    name=$(echo $port_info | cut -d: -f2)
    
    log_info "检查端口 $port ($name):"
    if netstat -tlnp | grep ":$port " >/dev/null 2>&1; then
        echo "  ✅ 端口被占用:"
        netstat -tlnp | grep ":$port "
    else
        echo "  ❌ 端口未被占用"
    fi
done

echo ""

# 5. 检查配置文件
echo "5. 配置文件检查"
echo "==============="

log_info "MySQL/MariaDB配置文件:"
config_files=("/etc/my.cnf" "/etc/mysql/my.cnf" "/etc/my.cnf.d/server.cnf")
for config in "${config_files[@]}"; do
    if [ -f "$config" ]; then
        echo "  ✅ $config 存在"
        # 检查关键配置
        if grep -q "bind-address" "$config" 2>/dev/null; then
            echo "    bind-address: $(grep bind-address $config | head -1)"
        fi
    else
        echo "  ❌ $config 不存在"
    fi
done

log_info "Redis配置文件:"
redis_configs=("/etc/redis.conf" "/etc/redis/redis.conf")
for config in "${redis_configs[@]}"; do
    if [ -f "$config" ]; then
        echo "  ✅ $config 存在"
        # 检查关键配置
        if grep -q "^bind" "$config" 2>/dev/null; then
            echo "    bind: $(grep "^bind" $config | head -1)"
        fi
        if grep -q "^port" "$config" 2>/dev/null; then
            echo "    port: $(grep "^port" $config | head -1)"
        fi
    else
        echo "  ❌ $config 不存在"
    fi
done

echo ""

# 6. 检查数据目录和权限
echo "6. 数据目录和权限检查"
echo "===================="

log_info "MySQL数据目录:"
mysql_dirs=("/var/lib/mysql" "/var/lib/mariadb")
for dir in "${mysql_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "  ✅ $dir 存在"
        echo "    权限: $(ls -ld $dir)"
        echo "    内容: $(ls -la $dir | wc -l) 个文件/目录"
        
        # 检查关键文件
        if [ -f "$dir/mysql/user.frm" ] || [ -f "$dir/mysql/user.MYD" ]; then
            echo "    ✅ MySQL系统表存在"
        else
            echo "    ❌ MySQL系统表缺失 - 需要初始化"
        fi
    else
        echo "  ❌ $dir 不存在"
    fi
done

log_info "Redis数据目录:"
redis_dirs=("/var/lib/redis" "/var/lib/redis-server")
for dir in "${redis_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "  ✅ $dir 存在"
        echo "    权限: $(ls -ld $dir)"
    else
        echo "  ❌ $dir 不存在"
    fi
done

echo ""

# 7. 检查SELinux状态
echo "7. SELinux状态检查"
echo "=================="
if command -v getenforce >/dev/null 2>&1; then
    selinux_status=$(getenforce)
    log_info "SELinux状态: $selinux_status"
    
    if [ "$selinux_status" = "Enforcing" ]; then
        log_warn "SELinux处于强制模式，可能阻止服务启动"
        log_info "相关的SELinux布尔值:"
        getsebool -a | grep -E "(mysql|redis)" | head -5
    fi
else
    log_info "SELinux未安装"
fi

echo ""

# 8. 检查防火墙状态
echo "8. 防火墙状态检查"
echo "=================="
if systemctl is-active firewalld >/dev/null 2>&1; then
    log_info "Firewalld状态: 运行中"
    log_info "开放的端口:"
    firewall-cmd --list-ports
else
    log_info "Firewalld状态: 未运行"
fi

echo ""

# 9. 尝试手动启动并捕获错误
echo "9. 手动启动测试"
echo "==============="

log_info "尝试手动启动MySQL/MariaDB..."
if command -v mysqld >/dev/null 2>&1; then
    log_debug "测试mysqld命令:"
    timeout 5 mysqld --help >/dev/null 2>&1 && echo "  ✅ mysqld命令正常" || echo "  ❌ mysqld命令异常"
fi

if command -v mariadbd >/dev/null 2>&1; then
    log_debug "测试mariadbd命令:"
    timeout 5 mariadbd --help >/dev/null 2>&1 && echo "  ✅ mariadbd命令正常" || echo "  ❌ mariadbd命令异常"
fi

log_info "尝试手动启动Redis..."
if command -v redis-server >/dev/null 2>&1; then
    log_debug "测试redis-server命令:"
    timeout 5 redis-server --help >/dev/null 2>&1 && echo "  ✅ redis-server命令正常" || echo "  ❌ redis-server命令异常"
fi

echo ""

# 10. 生成解决方案建议
echo "10. 解决方案建议"
echo "================"

log_info "基于诊断结果的建议:"

# 检查是否安装了数据库
if ! dnf list installed | grep -q -E "(mysql|mariadb)"; then
    echo "  🔧 建议1: 安装数据库服务"
    echo "     dnf install -y mariadb-server mariadb"
fi

# 检查数据目录
if [ ! -d "/var/lib/mysql" ] && [ ! -d "/var/lib/mariadb" ]; then
    echo "  🔧 建议2: 初始化数据库"
    echo "     mysql_install_db --user=mysql --datadir=/var/lib/mysql"
fi

# 检查Redis
if ! dnf list installed | grep -q redis; then
    echo "  🔧 建议3: 安装Redis服务"
    echo "     dnf install -y redis"
fi

# SELinux建议
if command -v getenforce >/dev/null 2>&1 && [ "$(getenforce)" = "Enforcing" ]; then
    echo "  🔧 建议4: 临时禁用SELinux测试"
    echo "     setenforce 0"
fi

echo ""
echo "=== 诊断完成 ==="
echo ""
log_info "请查看上述诊断结果，找出具体的失败原因"
log_info "如需详细日志，运行: journalctl -u mysqld -f 或 journalctl -u mariadb -f"
