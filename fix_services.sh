#!/bin/bash

# 修复服务启动问题

echo "=== 修复MySQL和Redis服务 ==="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    log_error "请使用root权限运行此脚本"
    exit 1
fi

# 1. 检查并修复MySQL
log_info "1. 检查MySQL安装状态..."

if ! command -v mysql >/dev/null 2>&1; then
    log_warn "MySQL未安装，正在安装..."
    
    # 安装MySQL
    dnf install -y mysql-server mysql
    
    if [ $? -ne 0 ]; then
        log_error "MySQL安装失败，尝试其他方法..."
        
        # 尝试安装MariaDB作为替代
        log_info "尝试安装MariaDB..."
        dnf install -y mariadb-server mariadb
        
        if [ $? -eq 0 ]; then
            log_info "MariaDB安装成功"
            # 创建MySQL兼容的命令链接
            ln -sf /usr/bin/mariadb /usr/bin/mysql 2>/dev/null || true
            ln -sf /usr/bin/mariadb-admin /usr/bin/mysqladmin 2>/dev/null || true
            
            # 启用和启动MariaDB
            systemctl enable mariadb
            systemctl start mariadb
            
            # 检查状态
            if systemctl is-active mariadb >/dev/null 2>&1; then
                log_info "MariaDB启动成功"
            else
                log_error "MariaDB启动失败"
                systemctl status mariadb
            fi
        else
            log_error "数据库安装失败"
        fi
    else
        log_info "MySQL安装成功"
        
        # 初始化MySQL数据目录
        if [ ! -d "/var/lib/mysql/mysql" ]; then
            log_info "初始化MySQL数据目录..."
            mysqld --initialize-insecure --user=mysql --datadir=/var/lib/mysql
        fi
        
        # 启用和启动MySQL
        systemctl enable mysqld
        systemctl start mysqld
        
        # 检查状态
        if systemctl is-active mysqld >/dev/null 2>&1; then
            log_info "MySQL启动成功"
        else
            log_error "MySQL启动失败，查看详细错误..."
            systemctl status mysqld
            
            # 尝试修复权限问题
            log_info "尝试修复MySQL权限..."
            chown -R mysql:mysql /var/lib/mysql
            chmod 755 /var/lib/mysql
            
            # 重新启动
            systemctl start mysqld
        fi
    fi
else
    log_info "MySQL已安装"
    
    # 尝试启动MySQL
    if systemctl list-unit-files | grep -q mysqld; then
        systemctl enable mysqld
        systemctl start mysqld
        
        if systemctl is-active mysqld >/dev/null 2>&1; then
            log_info "MySQL启动成功"
        else
            log_warn "MySQL启动失败，尝试MariaDB..."
            systemctl enable mariadb 2>/dev/null || true
            systemctl start mariadb 2>/dev/null || true
        fi
    elif systemctl list-unit-files | grep -q mariadb; then
        systemctl enable mariadb
        systemctl start mariadb
        
        if systemctl is-active mariadb >/dev/null 2>&1; then
            log_info "MariaDB启动成功"
        fi
    fi
fi

# 2. 检查并修复Redis
log_info "2. 检查Redis安装状态..."

if ! command -v redis-server >/dev/null 2>&1; then
    log_warn "Redis未安装，正在安装..."
    dnf install -y redis
    
    if [ $? -eq 0 ]; then
        log_info "Redis安装成功"
    else
        log_error "Redis安装失败"
    fi
fi

# 启动Redis
if command -v redis-server >/dev/null 2>&1; then
    systemctl enable redis
    systemctl start redis
    
    if systemctl is-active redis >/dev/null 2>&1; then
        log_info "Redis启动成功"
    else
        log_warn "Redis启动失败，查看错误..."
        systemctl status redis
        
        # 尝试修复Redis配置
        log_info "尝试修复Redis配置..."
        
        # 检查Redis配置文件
        if [ -f "/etc/redis.conf" ]; then
            # 确保Redis可以启动
            sed -i 's/^bind 127.0.0.1/bind 127.0.0.1/' /etc/redis.conf
            sed -i 's/^# requirepass/# requirepass/' /etc/redis.conf
        elif [ -f "/etc/redis/redis.conf" ]; then
            sed -i 's/^bind 127.0.0.1/bind 127.0.0.1/' /etc/redis/redis.conf
            sed -i 's/^# requirepass/# requirepass/' /etc/redis/redis.conf
        fi
        
        # 重新启动
        systemctl restart redis
    fi
fi

# 3. 显示服务状态
echo ""
log_info "3. 服务状态检查..."

echo "MySQL/MariaDB状态:"
if systemctl is-active mysqld >/dev/null 2>&1; then
    echo "  ✅ mysqld: $(systemctl is-active mysqld)"
elif systemctl is-active mariadb >/dev/null 2>&1; then
    echo "  ✅ mariadb: $(systemctl is-active mariadb)"
else
    echo "  ❌ 数据库服务未运行"
fi

echo "Redis状态:"
if systemctl is-active redis >/dev/null 2>&1; then
    echo "  ✅ redis: $(systemctl is-active redis)"
else
    echo "  ❌ Redis服务未运行"
fi

# 4. 测试数据库连接
echo ""
log_info "4. 测试数据库连接..."

# 检测使用的是MySQL还是MariaDB
if systemctl is-active mysqld >/dev/null 2>&1; then
    DB_SERVICE="mysqld"
    DB_CMD="mysql"
elif systemctl is-active mariadb >/dev/null 2>&1; then
    DB_SERVICE="mariadb"
    DB_CMD="mysql"
else
    log_error "没有可用的数据库服务"
    exit 1
fi

log_info "使用数据库服务: $DB_SERVICE"

# 尝试无密码连接（初始安装）
if $DB_CMD -u root -e "SELECT 1;" >/dev/null 2>&1; then
    log_info "数据库无密码连接成功（初始状态）"
    
    # 创建数据库和用户
    log_info "创建数据库和用户..."
    $DB_CMD -u root << 'EOF'
CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'wb_user'@'localhost' IDENTIFIED BY 'wb_password';
GRANT ALL PRIVILEGES ON wb.* TO 'wb_user'@'localhost';
FLUSH PRIVILEGES;
EOF
    
    if [ $? -eq 0 ]; then
        log_info "数据库和用户创建成功"
        
        # 测试新用户
        if $DB_CMD -u wb_user -pwb_password -e "USE wb; SELECT 'OK' as status;" >/dev/null 2>&1; then
            log_info "新用户连接测试成功"
        fi
    fi
else
    log_warn "数据库需要密码或需要初始化"
    log_info "请稍后运行: ./setup_database.sh"
fi

# 5. 测试Redis连接
echo ""
log_info "5. 测试Redis连接..."
if redis-cli ping >/dev/null 2>&1; then
    log_info "Redis连接测试成功"
else
    log_warn "Redis连接测试失败"
fi

echo ""
log_info "=== 服务修复完成 ==="
echo ""
log_info "下一步操作:"
echo "1. 如果数据库需要密码，运行: ./setup_database.sh"
echo "2. 启动应用: ./quick_start.sh"
echo "3. 或使用系统服务: systemctl start wb-system"
