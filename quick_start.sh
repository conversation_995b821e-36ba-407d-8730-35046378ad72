#!/bin/bash

# 快速启动脚本

echo "=== 微博任务管理系统 - 快速启动 ==="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查应用目录
APP_DIR="/opt/wb_system"
if [ ! -d "$APP_DIR" ]; then
    log_error "应用目录不存在，请先运行部署脚本"
    echo "sudo ./native_deploy.sh"
    exit 1
fi

cd $APP_DIR

# 检查虚拟环境
if [ ! -d "venv" ]; then
    log_error "Python虚拟环境不存在"
    exit 1
fi

# 激活虚拟环境
source venv/bin/activate

# 检查服务状态
log_info "检查服务状态..."
echo "  - MySQL: $(systemctl is-active mysqld 2>/dev/null || echo 'not installed')"
echo "  - Redis: $(systemctl is-active redis 2>/dev/null || echo 'not installed')"

# 启动MySQL和Redis（如果需要）
if systemctl is-enabled mysqld >/dev/null 2>&1; then
    systemctl start mysqld
fi

if systemctl is-enabled redis >/dev/null 2>&1; then
    systemctl start redis
fi

# 检查端口占用
if netstat -tlnp | grep :8000 >/dev/null 2>&1; then
    log_warn "端口8000已被占用"
    echo "当前占用进程:"
    netstat -tlnp | grep :8000
    echo ""
    read -p "是否要停止现有进程并继续？(y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        pkill -f "uvicorn.*8000" || true
        sleep 2
    else
        log_info "退出启动"
        exit 0
    fi
fi

# 加载环境变量
if [ -f ".env" ]; then
    export $(cat .env | grep -v '^#' | xargs)
    log_info "环境变量已加载"
else
    log_warn ".env文件不存在，使用默认配置"
fi

# 启动应用
log_info "启动应用..."
echo ""
echo "🚀 正在启动微博任务管理系统..."
echo "📍 应用目录: $APP_DIR"
echo "🌐 访问地址: http://$(curl -s ifconfig.me 2>/dev/null || echo 'localhost'):8000"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 启动uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
