#!/usr/bin/env python3
"""
CentOS部署配置验证脚本
验证所有CentOS部署相关文件是否正确配置
"""

import os
import subprocess
import sys

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (不存在)")
        return False

def check_script_executable(script_path):
    """检查脚本是否可执行"""
    if os.path.exists(script_path):
        if os.access(script_path, os.X_OK):
            print(f"✅ 脚本可执行: {script_path}")
            return True
        else:
            print(f"⚠️  脚本需要执行权限: {script_path}")
            return False
    return False

def validate_centos_scripts():
    """验证CentOS部署脚本"""
    print("\n🔍 验证CentOS部署脚本...")
    
    scripts = [
        ("check_centos_system.sh", "系统检测脚本"),
        ("deploy_centos.sh", "CentOS部署脚本"),
        ("install_centos.sh", "一键安装脚本")
    ]
    
    results = []
    for script, desc in scripts:
        exists = check_file_exists(script, desc)
        if exists:
            executable = check_script_executable(script)
            results.append(exists and executable)
        else:
            results.append(False)
    
    return all(results)

def validate_docker_config():
    """验证Docker配置"""
    print("\n🔍 验证Docker配置...")
    
    configs = [
        ("docker-compose.yml", "Docker Compose配置"),
        ("Dockerfile", "Docker镜像配置"),
        (".dockerignore", "Docker忽略文件"),
        (".env.example", "环境配置模板")
    ]
    
    results = []
    for config, desc in configs:
        results.append(check_file_exists(config, desc))
    
    return all(results)

def validate_nginx_config():
    """验证Nginx配置"""
    print("\n🔍 验证Nginx配置...")
    
    configs = [
        ("nginx/nginx.conf", "Nginx主配置"),
        ("nginx/conf.d/default.conf", "Nginx站点配置")
    ]
    
    results = []
    for config, desc in configs:
        results.append(check_file_exists(config, desc))
    
    return all(results)

def validate_app_structure():
    """验证应用结构"""
    print("\n🔍 验证应用结构...")
    
    paths = [
        ("app/", "应用代码目录"),
        ("app/main.py", "主应用文件"),
        ("requirements.txt", "Python依赖文件"),
        ("alembic/", "数据库迁移目录"),
        ("alembic.ini", "Alembic配置文件")
    ]
    
    results = []
    for path, desc in paths:
        if os.path.isdir(path):
            print(f"✅ {desc}: {path} (目录)")
            results.append(True)
        elif os.path.isfile(path):
            print(f"✅ {desc}: {path} (文件)")
            results.append(True)
        else:
            print(f"❌ {desc}: {path} (不存在)")
            results.append(False)
    
    return all(results)

def validate_documentation():
    """验证文档"""
    print("\n🔍 验证文档...")
    
    docs = [
        ("CENTOS_DEPLOY.md", "CentOS部署文档"),
        ("DOCKER_DEPLOY.md", "Docker部署文档"),
        ("DOCKER_FILES_STRUCTURE.md", "文件结构文档")
    ]
    
    results = []
    for doc, desc in docs:
        results.append(check_file_exists(doc, desc))
    
    return all(results)

def check_script_syntax():
    """检查脚本语法"""
    print("\n🔍 检查脚本语法...")
    
    bash_scripts = [
        "check_centos_system.sh",
        "deploy_centos.sh", 
        "install_centos.sh"
    ]
    
    results = []
    for script in bash_scripts:
        if os.path.exists(script):
            try:
                # 使用bash -n检查语法
                result = subprocess.run(['bash', '-n', script], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ 语法检查通过: {script}")
                    results.append(True)
                else:
                    print(f"❌ 语法错误: {script}")
                    print(f"   错误信息: {result.stderr}")
                    results.append(False)
            except Exception as e:
                print(f"⚠️  无法检查语法: {script} ({e})")
                results.append(True)  # 假设正确
        else:
            results.append(False)
    
    return all(results)

def generate_deployment_summary():
    """生成部署总结"""
    print("\n📋 CentOS部署总结")
    print("="*50)
    
    # 获取当前目录
    current_dir = os.getcwd()
    print(f"项目目录: {current_dir}")
    
    # 检查文件大小
    total_size = 0
    file_count = 0
    
    for root, dirs, files in os.walk('.'):
        # 跳过隐藏目录和__pycache__
        dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
        
        for file in files:
            if not file.startswith('.') and not file.endswith('.pyc'):
                file_path = os.path.join(root, file)
                try:
                    size = os.path.getsize(file_path)
                    total_size += size
                    file_count += 1
                except:
                    pass
    
    print(f"项目文件数: {file_count}")
    print(f"项目大小: {total_size / 1024 / 1024:.2f} MB")
    
    print("\n🚀 部署步骤:")
    print("1. 将项目文件上传到CentOS服务器")
    print("2. 执行: chmod +x *.sh")
    print("3. 执行: sudo ./install_centos.sh")
    print("4. 等待安装完成")
    print("5. 在前端配置服务器地址")
    
    print("\n📞 如果遇到问题:")
    print("• 查看 CENTOS_DEPLOY.md 详细文档")
    print("• 运行 ./check_centos_system.sh 检查系统")
    print("• 手动执行 ./deploy_centos.sh 部署")

def main():
    """主函数"""
    print("🐧 CentOS部署配置验证")
    print("="*50)
    
    # 执行各项验证
    validations = [
        ("CentOS部署脚本", validate_centos_scripts()),
        ("Docker配置", validate_docker_config()),
        ("Nginx配置", validate_nginx_config()),
        ("应用结构", validate_app_structure()),
        ("文档", validate_documentation()),
        ("脚本语法", check_script_syntax())
    ]
    
    # 统计结果
    passed = sum(1 for _, result in validations if result)
    total = len(validations)
    
    print(f"\n📊 验证结果: {passed}/{total} 通过")
    
    # 显示详细结果
    print("\n📋 详细结果:")
    for name, result in validations:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
    
    if passed == total:
        print("\n🎉 CentOS部署配置验证通过！")
        print("✅ 所有文件和配置都已准备就绪")
        print("✅ 可以开始在CentOS服务器上部署")
        
        generate_deployment_summary()
        
        return True
    else:
        print(f"\n❌ 验证失败，请检查缺失的文件和配置")
        print("请确保所有必要的文件都存在并且配置正确")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
