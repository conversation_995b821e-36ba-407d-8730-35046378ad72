#!/bin/bash

# 修复数据库连接问题

echo "=== 修复数据库连接问题 ==="

# 1. 检查MySQL容器状态
echo "1. 检查MySQL容器状态..."
docker-compose ps mysql

echo "检查MySQL是否可以连接..."
for i in {1..10}; do
    if docker-compose exec -T mysql mysqladmin ping -h localhost -u root -p123456 >/dev/null 2>&1; then
        echo "✅ MySQL容器正常运行"
        break
    fi
    echo "等待MySQL启动... ($i/10)"
    sleep 3
done

# 2. 测试数据库连接
echo ""
echo "2. 测试数据库连接..."
echo "测试root用户连接:"
docker-compose exec -T mysql mysql -u root -p123456 -e "SELECT 1;" 2>/dev/null && echo "✅ root用户连接正常" || echo "❌ root用户连接失败"

echo "测试wb_user用户连接:"
docker-compose exec -T mysql mysql -u wb_user -pwb_password -e "SELECT 1;" 2>/dev/null && echo "✅ wb_user用户连接正常" || echo "❌ wb_user用户连接失败"

# 3. 创建修复版的main.py
echo ""
echo "3. 创建修复版的main.py..."
cp app/main.py app/main.py.backup

cat > app/main.py << 'EOF'
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
import asyncio
import socket
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="微博任务管理系统")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_local_ip():
    """获取本地IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "微博任务管理系统",
        "status": "running",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 尝试连接数据库
        from app.db import get_db
        
        db = next(get_db())
        db.execute("SELECT 1")
        db.close()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "database": "connected",
            "services": {
                "api": "running",
                "database": "connected"
            }
        }
    except Exception as e:
        logger.warning(f"Database connection failed: {e}")
        return {
            "status": "partial",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "database": "disconnected",
            "error": str(e),
            "services": {
                "api": "running",
                "database": "disconnected"
            }
        }

@app.get("/ip")
async def show_ip_info():
    """显示服务器IP信息"""
    return {
        "hostname": socket.gethostname(),
        "local_ip": get_local_ip(),
        "message": "访问此服务的IP地址: " + get_local_ip()
    }

@app.get("/db-test")
async def test_database():
    """测试数据库连接"""
    try:
        from app.db import engine, get_db
        
        # 测试连接
        db = next(get_db())
        result = db.execute("SELECT VERSION()").fetchone()
        db.close()
        
        return {
            "status": "success",
            "mysql_version": result[0] if result else "unknown",
            "connection": "ok"
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "connection": "failed"
        }

async def init_database():
    """初始化数据库"""
    try:
        from app.db import engine, Base
        
        # 等待数据库准备就绪
        max_retries = 30
        for i in range(max_retries):
            try:
                # 测试连接
                connection = engine.connect()
                connection.close()
                logger.info("Database connection successful")
                break
            except Exception as e:
                if i < max_retries - 1:
                    logger.info(f"Waiting for database... ({i+1}/{max_retries})")
                    await asyncio.sleep(2)
                else:
                    logger.error(f"Failed to connect to database after {max_retries} attempts: {e}")
                    return
        
        # 创建表
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("Starting up application...")
    
    # 异步初始化数据库
    asyncio.create_task(init_database())
    
    logger.info("Application startup completed")

# 延迟导入路由（避免启动时的循环导入问题）
@app.on_event("startup")
async def setup_routes():
    """设置路由"""
    try:
        # 延迟导入以避免启动时的数据库连接问题
        await asyncio.sleep(5)  # 等待数据库初始化
        
        from app.routes import device, task, group, task_sync
        from app.websocket import ws_manager
        
        app.include_router(device.router, tags=["Devices"])
        app.include_router(task.router, prefix="/tasks", tags=["Tasks"])
        app.include_router(group.router, prefix="/groups", tags=["Groups"])
        app.include_router(task_sync.router, tags=["Task Sync"])
        app.include_router(ws_manager.router, prefix="/ws", tags=["WebSocket"])
        
        logger.info("Routes setup completed")
        
    except Exception as e:
        logger.warning(f"Route setup failed: {e}")
EOF

# 4. 重新构建镜像
echo ""
echo "4. 重新构建镜像..."
export DOCKER_BUILDKIT=0
docker build -t wb_backend:latest .

# 5. 重新启动服务
echo ""
echo "5. 重新启动服务..."
docker-compose stop backend
docker-compose up -d backend

# 6. 等待并检查状态
echo ""
echo "6. 等待服务启动..."
sleep 20

echo "检查容器状态..."
docker-compose ps

echo ""
echo "检查容器日志..."
docker logs wb_backend --tail 15

# 7. 测试服务
echo ""
echo "7. 测试服务..."
for i in {1..15}; do
    if curl -s http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ 后端服务启动成功！"
        echo ""
        echo "健康检查响应:"
        curl -s http://localhost:8000/health | python3 -m json.tool 2>/dev/null || curl -s http://localhost:8000/health
        break
    fi
    echo "等待后端服务启动... ($i/15)"
    sleep 3
done

echo ""
echo "=== 修复完成 ==="

# 检查最终状态
if curl -s http://localhost:8000/health >/dev/null 2>&1; then
    echo ""
    echo "🎉 服务修复成功！"
    echo ""
    echo "可用的API端点:"
    echo "  - 根路径: http://***********:8000/"
    echo "  - 健康检查: http://***********:8000/health"
    echo "  - IP信息: http://***********:8000/ip"
    echo "  - 数据库测试: http://***********:8000/db-test"
    echo "  - API文档: http://***********:8000/docs"
else
    echo ""
    echo "❌ 服务仍有问题，请检查日志:"
    echo "docker logs wb_backend"
fi
