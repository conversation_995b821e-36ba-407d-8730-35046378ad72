#!/bin/bash

# 快速修复依赖问题

echo "=== 快速修复依赖问题 ==="

# 1. 停止容器
docker-compose stop backend

# 2. 在运行的容器中安装缺失的依赖
echo "在容器中安装缺失的依赖..."
docker run --rm -v $(pwd):/app -w /app python:3.9-slim pip install pytz alembic

# 3. 重新构建镜像
echo "重新构建镜像..."
export DOCKER_BUILDKIT=0
docker build -t wb_backend:latest .

# 4. 重新启动
echo "重新启动服务..."
docker-compose up -d backend

# 5. 检查状态
sleep 10
docker-compose ps
docker logs wb_backend --tail 5
