#!/bin/bash

# 修复缺失依赖问题

echo "=== 修复缺失依赖问题 ==="

# 1. 停止当前容器
echo "1. 停止当前容器..."
docker-compose stop backend
docker rm wb_backend 2>/dev/null || true

# 2. 检查更新后的requirements.txt
echo ""
echo "2. 检查更新后的requirements.txt..."
echo "当前requirements.txt内容:"
cat requirements.txt

# 3. 重新构建镜像
echo ""
echo "3. 重新构建镜像..."
export DOCKER_BUILDKIT=0

# 创建修复的Dockerfile
cat > Dockerfile.deps_fixed << 'EOF'
FROM python:3.9-slim

WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# 复制requirements文件
COPY requirements.txt .

# 安装依赖
RUN pip install --no-cache-dir \
    --index-url https://pypi.tuna.tsinghua.edu.cn/simple \
    --trusted-host pypi.tuna.tsinghua.edu.cn \
    -r requirements.txt

# 复制应用代码
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF

echo "重新构建镜像..."
if docker build -f Dockerfile.deps_fixed -t wb_backend:latest .; then
    echo "✅ 镜像重新构建成功"
    rm -f Dockerfile.deps_fixed
else
    echo "❌ 镜像构建失败"
    rm -f Dockerfile.deps_fixed
    exit 1
fi

# 4. 重新启动后端服务
echo ""
echo "4. 重新启动后端服务..."
docker-compose up -d backend

# 5. 等待并检查状态
echo ""
echo "5. 等待服务启动..."
sleep 15

echo "检查容器状态..."
docker-compose ps

echo ""
echo "检查容器日志..."
docker logs wb_backend --tail 10

# 6. 测试服务
echo ""
echo "6. 测试服务..."
for i in {1..15}; do
    if curl -s http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ 后端服务启动成功！"
        echo ""
        echo "健康检查响应:"
        curl -s http://localhost:8000/health | python3 -m json.tool 2>/dev/null || curl -s http://localhost:8000/health
        break
    fi
    echo "等待后端服务启动... ($i/15)"
    sleep 3
done

# 7. 显示最终状态
echo ""
echo "7. 最终状态..."
docker-compose ps

echo ""
echo "=== 修复完成 ==="

# 检查最终状态
if curl -s http://localhost:8000/health >/dev/null 2>&1; then
    echo ""
    echo "🎉 服务修复成功！"
    echo ""
    echo "现在可以访问:"
    echo "  - 健康检查: http://***********:8000/health"
    echo "  - API文档: http://***********:8000/docs"
    echo "  - 后端API: http://***********:8000"
    echo ""
    echo "如果外部无法访问，请检查防火墙和安全组设置"
else
    echo ""
    echo "❌ 服务仍有问题，请检查日志:"
    echo "docker logs wb_backend"
fi
