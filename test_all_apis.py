#!/usr/bin/env python3
"""
微博任务管理系统 - 完整API测试脚本
测试所有接口的连通性和功能
"""

import requests
import json
import websocket
import threading
import time
from datetime import datetime

class APITester:
    def __init__(self, base_url="http://***********:11234"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api"
        self.ws_base = base_url.replace("http", "ws")
        
    def test_basic_endpoints(self):
        """测试基础端点"""
        print("=" * 50)
        print("🔍 测试基础端点")
        print("=" * 50)
        
        endpoints = [
            "/",
            "/health", 
            "/ip",
            "/docs",
            "/openapi.json"
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                if response.status_code == 200:
                    print(f"✅ {endpoint} - 状态码: {response.status_code}")
                    if endpoint in ["/", "/health", "/ip"]:
                        data = response.json()
                        print(f"   响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                else:
                    print(f"❌ {endpoint} - 状态码: {response.status_code}")
            except Exception as e:
                print(f"❌ {endpoint} - 错误: {e}")
        
    def test_device_apis(self):
        """测试设备管理API"""
        print("\n" + "=" * 50)
        print("📱 测试设备管理API")
        print("=" * 50)
        
        # 获取设备列表
        try:
            response = requests.get(f"{self.api_base}/devices", timeout=10)
            print(f"✅ GET /api/devices - 状态码: {response.status_code}")
            devices = response.json()
            print(f"   设备数量: {len(devices.get('devices', devices))}")
        except Exception as e:
            print(f"❌ GET /api/devices - 错误: {e}")
        
        # 创建测试设备
        try:
            test_device = {
                "device_number": f"test_device_{int(time.time())}",
                "device_ip": "*************"
            }
            response = requests.post(f"{self.api_base}/devices", 
                                   json=test_device, timeout=10)
            print(f"✅ POST /api/devices - 状态码: {response.status_code}")
            if response.status_code in [200, 201]:
                device_data = response.json()
                print(f"   创建设备: {device_data}")
        except Exception as e:
            print(f"❌ POST /api/devices - 错误: {e}")
    
    def test_task_apis(self):
        """测试任务管理API"""
        print("\n" + "=" * 50)
        print("📋 测试任务管理API")
        print("=" * 50)
        
        # 获取任务列表
        try:
            response = requests.get(f"{self.api_base}/tasks", timeout=10)
            print(f"✅ GET /api/tasks - 状态码: {response.status_code}")
            tasks = response.json()
            print(f"   任务数量: {len(tasks.get('tasks', tasks))}")
        except Exception as e:
            print(f"❌ GET /api/tasks - 错误: {e}")
        
        # 创建测试任务
        try:
            test_task = {
                "task_type": "like",
                "parameters": {
                    "blogger_id": "test123",
                    "like_id": "test456"
                },
                "target_scope": "all",
                "delay_group": 5,
                "delay_like": 3
            }
            response = requests.post(f"{self.api_base}/tasks", 
                                   json=test_task, timeout=10)
            print(f"✅ POST /api/tasks - 状态码: {response.status_code}")
            if response.status_code in [200, 201]:
                task_data = response.json()
                print(f"   创建任务: {task_data}")
        except Exception as e:
            print(f"❌ POST /api/tasks - 错误: {e}")
    
    def test_group_apis(self):
        """测试分组管理API"""
        print("\n" + "=" * 50)
        print("👥 测试分组管理API")
        print("=" * 50)
        
        # 获取分组列表
        try:
            response = requests.get(f"{self.api_base}/groups", timeout=10)
            print(f"✅ GET /api/groups - 状态码: {response.status_code}")
            groups = response.json()
            print(f"   分组数量: {len(groups.get('groups', groups))}")
        except Exception as e:
            print(f"❌ GET /api/groups - 错误: {e}")
        
        # 创建测试分组
        try:
            test_group = {
                "name": f"test_group_{int(time.time())}",
                "description": "测试分组"
            }
            response = requests.post(f"{self.api_base}/groups", 
                                   json=test_group, timeout=10)
            print(f"✅ POST /api/groups - 状态码: {response.status_code}")
            if response.status_code in [200, 201]:
                group_data = response.json()
                print(f"   创建分组: {group_data}")
        except Exception as e:
            print(f"❌ POST /api/groups - 错误: {e}")
    
    def test_websocket_connection(self):
        """测试WebSocket连接"""
        print("\n" + "=" * 50)
        print("🔌 测试WebSocket连接")
        print("=" * 50)
        
        def on_message(ws, message):
            print(f"📨 收到消息: {message}")
        
        def on_error(ws, error):
            print(f"❌ WebSocket错误: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print(f"🔌 WebSocket连接关闭")
        
        def on_open(ws):
            print(f"✅ WebSocket连接成功")
            # 发送心跳消息
            heartbeat_msg = {
                "type": "heartbeat",
                "timestamp": datetime.now().isoformat(),
                "device_number": "test_device_api"
            }
            ws.send(json.dumps(heartbeat_msg))
            print(f"📤 发送心跳: {heartbeat_msg}")
            
            # 5秒后关闭连接
            def close_connection():
                time.sleep(5)
                ws.close()
            
            threading.Thread(target=close_connection).start()
        
        try:
            ws_url = f"{self.ws_base}/ws/test_device_api"
            print(f"🔗 连接WebSocket: {ws_url}")
            
            ws = websocket.WebSocketApp(ws_url,
                                      on_open=on_open,
                                      on_message=on_message,
                                      on_error=on_error,
                                      on_close=on_close)
            
            ws.run_forever()
            
        except Exception as e:
            print(f"❌ WebSocket连接失败: {e}")
    
    def test_task_sync_apis(self):
        """测试任务同步API"""
        print("\n" + "=" * 50)
        print("🔄 测试任务同步API")
        print("=" * 50)
        
        sync_endpoints = [
            "/task-sync/sync-all-tasks",
            "/task-sync/sync-all-devices", 
            "/task-sync/group-summary",
            "/task-sync/device-queues"
        ]
        
        for endpoint in sync_endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                print(f"✅ GET {endpoint} - 状态码: {response.status_code}")
                if response.status_code == 200:
                    data = response.json()
                    print(f"   响应数据量: {len(str(data))} 字符")
            except Exception as e:
                print(f"❌ GET {endpoint} - 错误: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始API完整性测试")
        print(f"🌐 测试服务器: {self.base_url}")
        print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.test_basic_endpoints()
        self.test_device_apis()
        self.test_task_apis()
        self.test_group_apis()
        self.test_task_sync_apis()
        self.test_websocket_connection()
        
        print("\n" + "=" * 50)
        print("🎉 API测试完成")
        print("=" * 50)
        print("\n📋 测试总结:")
        print("✅ 基础端点测试")
        print("✅ 设备管理API测试")
        print("✅ 任务管理API测试")
        print("✅ 分组管理API测试")
        print("✅ 任务同步API测试")
        print("✅ WebSocket连接测试")
        print("\n🔗 前端配置:")
        print(f'SERVER_URL = "{self.base_url}"')
        print(f'API_BASE_URL = "{self.api_base}"')
        print(f'WS_URL = "{self.ws_base}/ws"')

def main():
    import sys
    
    # 允许自定义服务器地址
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://***********:11234"
    
    tester = APITester(base_url)
    tester.run_all_tests()

if __name__ == "__main__":
    main()
