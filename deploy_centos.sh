#!/bin/bash

# CentOS Docker部署脚本
# 专门为CentOS系统优化的部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测CentOS版本
detect_centos_version() {
    if [ -f /etc/centos-release ]; then
        if grep -q "CentOS Linux release 7" /etc/centos-release; then
            CENTOS_MAJOR=7
            PACKAGE_MANAGER="yum"
        elif grep -q "CentOS Linux release 8" /etc/centos-release || grep -q "CentOS Stream" /etc/centos-release; then
            CENTOS_MAJOR=8
            PACKAGE_MANAGER="dnf"
        else
            CENTOS_MAJOR=7
            PACKAGE_MANAGER="yum"
        fi
        log_info "检测到CentOS $CENTOS_MAJOR，使用 $PACKAGE_MANAGER 包管理器"
    else
        log_error "这不是CentOS系统"
        exit 1
    fi
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户或sudo权限运行此脚本"
        echo "使用方法: sudo $0"
        exit 1
    fi
}

# 更新系统
update_system() {
    log_info "更新系统包..."
    
    if [ "$CENTOS_MAJOR" = "7" ]; then
        yum update -y
        yum install -y epel-release
        yum install -y yum-utils device-mapper-persistent-data lvm2 curl wget git
    else
        dnf update -y
        dnf install -y dnf-plugins-core curl wget git
    fi
    
    log_success "系统更新完成"
}

# 安装Docker
install_docker() {
    log_info "安装Docker..."
    
    # 检查Docker是否已安装
    if command -v docker >/dev/null 2>&1; then
        log_warning "Docker已安装，跳过安装步骤"
        return
    fi
    
    # 添加Docker仓库
    if [ "$CENTOS_MAJOR" = "7" ]; then
        yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
        yum install -y docker-ce docker-ce-cli containerd.io
    else
        dnf config-manager --add-repo=https://download.docker.com/linux/centos/docker-ce.repo
        dnf install -y docker-ce docker-ce-cli containerd.io
    fi
    
    # 启动Docker服务
    systemctl start docker
    systemctl enable docker
    
    # 验证Docker安装
    if docker --version >/dev/null 2>&1; then
        log_success "Docker安装成功: $(docker --version)"
    else
        log_error "Docker安装失败"
        exit 1
    fi
}

# 安装Docker Compose
install_docker_compose() {
    log_info "安装Docker Compose..."
    
    # 检查Docker Compose是否已安装
    if command -v docker-compose >/dev/null 2>&1; then
        log_warning "Docker Compose已安装，跳过安装步骤"
        return
    fi
    
    # 下载Docker Compose
    COMPOSE_VERSION="v2.12.2"
    curl -L "https://github.com/docker/compose/releases/download/${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    
    # 设置执行权限
    chmod +x /usr/local/bin/docker-compose
    
    # 创建软链接
    ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    # 验证安装
    if docker-compose --version >/dev/null 2>&1; then
        log_success "Docker Compose安装成功: $(docker-compose --version)"
    else
        log_error "Docker Compose安装失败"
        exit 1
    fi
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    if systemctl is-active --quiet firewalld; then
        log_info "配置firewalld规则..."
        
        # 开放必要端口
        firewall-cmd --permanent --add-port=80/tcp
        firewall-cmd --permanent --add-port=443/tcp
        firewall-cmd --permanent --add-port=8000/tcp
        firewall-cmd --permanent --add-port=3306/tcp
        firewall-cmd --permanent --add-port=6379/tcp
        
        # 允许Docker网络
        firewall-cmd --permanent --zone=trusted --add-interface=docker0
        firewall-cmd --permanent --zone=trusted --add-masquerade
        
        # 重载防火墙规则
        firewall-cmd --reload
        
        log_success "防火墙配置完成"
    else
        log_warning "firewalld未运行，跳过防火墙配置"
    fi
}

# 配置SELinux
configure_selinux() {
    log_info "配置SELinux..."
    
    if command -v getenforce >/dev/null 2>&1; then
        SELINUX_STATUS=$(getenforce)
        
        if [ "$SELINUX_STATUS" = "Enforcing" ]; then
            log_info "SELinux处于强制模式，配置相关策略..."
            
            # 安装SELinux策略工具
            if [ "$CENTOS_MAJOR" = "7" ]; then
                yum install -y policycoreutils-python
            else
                dnf install -y policycoreutils-python-utils
            fi
            
            # 设置SELinux布尔值
            setsebool -P httpd_can_network_connect 1
            setsebool -P container_manage_cgroup 1
            
            log_success "SELinux配置完成"
        else
            log_info "SELinux状态: $SELINUX_STATUS，无需额外配置"
        fi
    fi
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p /opt/wb_system
    mkdir -p /opt/wb_system/logs/nginx
    mkdir -p /opt/wb_system/mysql/init
    mkdir -p /opt/wb_system/nginx/ssl
    mkdir -p /opt/wb_system/data/mysql
    mkdir -p /opt/wb_system/data/redis
    
    # 设置目录权限
    chown -R 1000:1000 /opt/wb_system/logs
    chmod -R 755 /opt/wb_system
    
    log_success "目录创建完成"
}

# 复制项目文件
copy_project_files() {
    log_info "复制项目文件到 /opt/wb_system..."
    
    # 复制应用代码
    cp -r app/ /opt/wb_system/
    cp -r alembic/ /opt/wb_system/
    cp alembic.ini /opt/wb_system/
    cp requirements.txt /opt/wb_system/
    cp Dockerfile /opt/wb_system/
    
    # 复制配置文件
    cp docker-compose.yml /opt/wb_system/
    cp .env.example /opt/wb_system/
    cp -r nginx/ /opt/wb_system/
    
    # 复制部署脚本
    cp .dockerignore /opt/wb_system/
    
    log_success "项目文件复制完成"
}

# 配置环境变量
setup_environment() {
    log_info "配置环境变量..."
    
    cd /opt/wb_system
    
    if [ ! -f .env ]; then
        cp .env.example .env
        
        # 生成随机密钥
        SECRET_KEY=$(openssl rand -hex 32)
        MYSQL_ROOT_PASSWORD=$(openssl rand -base64 32)
        MYSQL_PASSWORD=$(openssl rand -base64 32)
        
        # 更新配置文件
        sed -i "s/your-secret-key-change-in-production/$SECRET_KEY/" .env
        sed -i "s/123456/$MYSQL_ROOT_PASSWORD/" .env
        sed -i "s/wb_password/$MYSQL_PASSWORD/" .env
        
        log_success "环境配置完成"
        log_info "数据库root密码: $MYSQL_ROOT_PASSWORD"
        log_info "数据库用户密码: $MYSQL_PASSWORD"
        
        # 保存密码到文件
        echo "MySQL Root Password: $MYSQL_ROOT_PASSWORD" > /opt/wb_system/passwords.txt
        echo "MySQL User Password: $MYSQL_PASSWORD" >> /opt/wb_system/passwords.txt
        chmod 600 /opt/wb_system/passwords.txt
        
    else
        log_info ".env文件已存在，跳过配置"
    fi
}

# 构建和启动服务
deploy_services() {
    log_info "构建和启动服务..."
    
    cd /opt/wb_system
    
    # 构建镜像
    log_info "构建Docker镜像..."
    docker-compose build --no-cache
    
    # 启动数据库服务
    log_info "启动数据库服务..."
    docker-compose up -d mysql redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 30
    
    # 启动后端服务
    log_info "启动后端服务..."
    docker-compose up -d backend
    
    # 等待后端启动
    log_info "等待后端服务启动..."
    sleep 20
    
    # 启动Nginx
    log_info "启动Nginx服务..."
    docker-compose up -d nginx
    
    log_success "所有服务启动完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    cd /opt/wb_system
    
    # 检查服务状态
    echo "服务状态:"
    docker-compose ps
    
    # 等待服务完全启动
    sleep 10
    
    # 检查健康状态
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        log_success "后端服务健康检查通过"
    else
        log_warning "后端服务健康检查失败，请检查日志"
    fi
    
    if curl -f http://localhost/health >/dev/null 2>&1; then
        log_success "Nginx代理健康检查通过"
    else
        log_warning "Nginx代理健康检查失败，请检查配置"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "CentOS部署完成！"
    
    # 获取服务器IP
    SERVER_IP=$(ip route get ******* | awk -F"src " 'NR==1{split($2,a," ");print a[1]}')
    
    echo ""
    echo "=========================================="
    echo "🎉 微博设备管理系统部署成功！"
    echo "=========================================="
    echo ""
    echo "📱 访问地址："
    echo "  - 主服务: http://$SERVER_IP"
    echo "  - API文档: http://$SERVER_IP/docs"
    echo "  - 后端直连: http://$SERVER_IP:8000"
    echo "  - 健康检查: http://$SERVER_IP/health"
    echo ""
    echo "🗄️ 数据库信息："
    echo "  - MySQL: $SERVER_IP:3306"
    echo "  - 用户名: wb_user"
    echo "  - 密码: 查看 /opt/wb_system/passwords.txt"
    echo "  - 数据库: wb"
    echo ""
    echo "📊 管理命令："
    echo "  - 查看日志: cd /opt/wb_system && docker-compose logs -f"
    echo "  - 停止服务: cd /opt/wb_system && docker-compose down"
    echo "  - 重启服务: cd /opt/wb_system && docker-compose restart"
    echo "  - 查看状态: cd /opt/wb_system && docker-compose ps"
    echo ""
    echo "📁 项目目录: /opt/wb_system"
    echo "🔑 密码文件: /opt/wb_system/passwords.txt"
    echo ""
    echo "⚙️ 前端配置："
    echo "  请在前端设置中配置服务器地址为："
    echo "  - 服务器IP: $SERVER_IP:8000"
    echo ""
}

# 主函数
main() {
    echo "🐧 CentOS Docker部署脚本"
    echo "=========================="
    echo ""
    
    detect_centos_version
    check_root
    update_system
    install_docker
    install_docker_compose
    configure_firewall
    configure_selinux
    create_directories
    copy_project_files
    setup_environment
    deploy_services
    verify_deployment
    show_deployment_info
}

# 处理命令行参数
case "${1:-}" in
    "install-deps")
        log_info "仅安装依赖..."
        detect_centos_version
        check_root
        update_system
        install_docker
        install_docker_compose
        configure_firewall
        configure_selinux
        ;;
    "deploy-only")
        log_info "仅部署服务..."
        check_root
        create_directories
        copy_project_files
        setup_environment
        deploy_services
        verify_deployment
        show_deployment_info
        ;;
    *)
        main
        ;;
esac
