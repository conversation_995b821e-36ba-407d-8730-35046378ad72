# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['app\\main.py'],
    pathex=[],
    binaries=[],
    datas=[('.env', '.')],
    hiddenimports=['uvicorn.protocols.http', 'uvicorn.protocols.websockets', 'fastapi', 'pydantic', 'sqlalchemy', 'pymysql', 'fastapi.applications', 'fastapi.routing', 'fastapi.param_functions', 'fastapi.dependencies.utils', 'fastapi.encoders', 'uvicorn.lifespan.on', 'uvicorn.lifespan.off', 'uvicorn.protocols.http.h11_impl', 'uvicorn.protocols.websockets.websockets_impl', 'sqlalchemy.ext.asyncio', 'pymysql.constants.CLIENT'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='main',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
