#!/usr/bin/env python3
"""
测试设备在线时的任务执行
"""

import requests
import json
import time

def test_online_task():
    """测试设备在线时的任务执行"""
    print('🟢 测试设备在线时的任务执行')
    print('='*50)

    try:
        task_data = {
            'task_type': 'like',
            'target_scope': 'single',
            'target_id': 3,  # device_test_01对应设备ID 3
            'parameters': {
                'blogger_id': 'online_test_blogger',
                'like_id': 'online_test_like',
                'delay_click': 1000
            },
            'delay_group': 2000,
            'delay_like': 1000
        }
        
        print('📤 创建任务...')
        response = requests.post('http://localhost:8000/tasks/', json=task_data, timeout=10)
        print(f'📊 创建状态码: {response.status_code}')
        
        if response.status_code == 200:
            task = response.json()
            task_id = task.get('id')
            initial_status = task.get('status')
            print(f'✅ 任务创建成功: ID={task_id}')
            print(f'初始状态: {initial_status}')
            
            # 监控任务状态变化
            print(f'\n⏳ 监控任务执行过程...')
            for i in range(15):
                time.sleep(1)
                
                # 检查任务状态
                task_response = requests.get(f'http://localhost:8000/tasks/{task_id}')
                if task_response.status_code == 200:
                    updated_task = task_response.json()
                    status = updated_task.get('status')
                    print(f'  第{i+1}秒: 状态={status}')
                    
                    if status != initial_status:
                        if status in ['completed', 'done']:
                            print(f'\n✅ 任务执行成功: {status}')
                            print(f'这证明设备在线时任务能正确执行！')
                            return True
                        elif status == 'failed':
                            print(f'\n❌ 任务执行失败: {status}')
                            return False
                        elif status == 'running':
                            print(f'\n🔄 任务正在执行: {status}')
                            initial_status = status  # 更新初始状态
                else:
                    print(f'  第{i+1}秒: 无法获取任务状态')
            else:
                print(f'\n⏰ 15秒内任务未完成，最终状态: {status}')
                return False
                
        else:
            print(f'❌ 任务创建失败: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ 测试异常: {e}')
        return False

if __name__ == "__main__":
    test_online_task()
