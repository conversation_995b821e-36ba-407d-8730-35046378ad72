"""
设置对话框
用于配置服务器地址和其他设置
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import re
from utils.config_manager import config_manager

class SettingsDialog:
    def __init__(self, parent):
        self.parent = parent
        self.dialog = None
        self.result = False
        
        # 配置变量
        self.host_var = tk.StringVar()
        self.port_var = tk.StringVar()
        self.use_https_var = tk.BooleanVar()
        self.auto_refresh_var = tk.BooleanVar()
        self.refresh_interval_var = tk.StringVar()
        self.request_timeout_var = tk.StringVar()
        
    def show(self):
        """显示设置对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("⚙️ 系统设置")
        self.dialog.geometry("500x600")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"500x600+{x}+{y}")
        
        self.create_widgets()
        self.load_current_config()
        
        # 等待对话框关闭
        self.dialog.wait_window()
        return self.result
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 创建Notebook用于分页
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 服务器设置页面
        self.create_server_tab(notebook)
        
        # 界面设置页面
        self.create_ui_tab(notebook)
        
        # 高级设置页面
        self.create_advanced_tab(notebook)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        # 按钮
        ttk.Button(button_frame, text="📁 导入配置", command=self.import_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="💾 导出配置", command=self.export_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 重置默认", command=self.reset_to_default).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="❌ 取消", command=self.cancel).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="✅ 确定", command=self.save_and_close).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="🧪 测试连接", command=self.test_connection).pack(side=tk.RIGHT, padx=5)
    
    def create_server_tab(self, notebook):
        """创建服务器设置页面"""
        server_frame = ttk.Frame(notebook)
        notebook.add(server_frame, text="🌐 服务器设置")
        
        # 服务器地址设置
        server_group = ttk.LabelFrame(server_frame, text="服务器地址配置")
        server_group.pack(fill=tk.X, padx=10, pady=10)
        
        # 主机地址
        ttk.Label(server_group, text="主机地址:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        host_entry = ttk.Entry(server_group, textvariable=self.host_var, width=30)
        host_entry.grid(row=0, column=1, padx=10, pady=5, sticky=tk.EW)
        
        # 端口
        ttk.Label(server_group, text="端口:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        port_entry = ttk.Entry(server_group, textvariable=self.port_var, width=30)
        port_entry.grid(row=1, column=1, padx=10, pady=5, sticky=tk.EW)
        
        # HTTPS选项
        https_check = ttk.Checkbutton(server_group, text="使用HTTPS/WSS", variable=self.use_https_var)
        https_check.grid(row=2, column=0, columnspan=2, sticky=tk.W, padx=10, pady=5)
        
        server_group.columnconfigure(1, weight=1)
        
        # 连接信息显示
        info_group = ttk.LabelFrame(server_frame, text="连接信息")
        info_group.pack(fill=tk.X, padx=10, pady=10)
        
        self.api_url_label = ttk.Label(info_group, text="API地址: ", foreground="blue")
        self.api_url_label.pack(anchor=tk.W, padx=10, pady=5)
        
        self.ws_url_label = ttk.Label(info_group, text="WebSocket地址: ", foreground="blue")
        self.ws_url_label.pack(anchor=tk.W, padx=10, pady=5)
        
        # 绑定变量变化事件
        self.host_var.trace('w', self.update_url_display)
        self.port_var.trace('w', self.update_url_display)
        self.use_https_var.trace('w', self.update_url_display)
        
        # 预设服务器
        preset_group = ttk.LabelFrame(server_frame, text="快速设置")
        preset_group.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(preset_group, text="本地服务器", command=lambda: self.set_preset("localhost", 8000, False)).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(preset_group, text="局域网服务器", command=self.set_lan_server).pack(side=tk.LEFT, padx=5, pady=5)
    
    def create_ui_tab(self, notebook):
        """创建界面设置页面"""
        ui_frame = ttk.Frame(notebook)
        notebook.add(ui_frame, text="🎨 界面设置")
        
        # 刷新设置
        refresh_group = ttk.LabelFrame(ui_frame, text="自动刷新设置")
        refresh_group.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Checkbutton(refresh_group, text="启用自动刷新", variable=self.auto_refresh_var).pack(anchor=tk.W, padx=10, pady=5)
        
        interval_frame = ttk.Frame(refresh_group)
        interval_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(interval_frame, text="刷新间隔(秒):").pack(side=tk.LEFT)
        ttk.Entry(interval_frame, textvariable=self.refresh_interval_var, width=10).pack(side=tk.LEFT, padx=(10, 0))
    
    def create_advanced_tab(self, notebook):
        """创建高级设置页面"""
        advanced_frame = ttk.Frame(notebook)
        notebook.add(advanced_frame, text="🔧 高级设置")
        
        # 网络设置
        network_group = ttk.LabelFrame(advanced_frame, text="网络设置")
        network_group.pack(fill=tk.X, padx=10, pady=10)
        
        timeout_frame = ttk.Frame(network_group)
        timeout_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(timeout_frame, text="请求超时(秒):").pack(side=tk.LEFT)
        ttk.Entry(timeout_frame, textvariable=self.request_timeout_var, width=10).pack(side=tk.LEFT, padx=(10, 0))
    
    def load_current_config(self):
        """加载当前配置"""
        self.host_var.set(config_manager.get('server.host', 'localhost'))
        self.port_var.set(str(config_manager.get('server.port', 8000)))
        self.use_https_var.set(config_manager.get('server.use_https', False))
        self.auto_refresh_var.set(config_manager.get('ui.auto_refresh', True))
        self.refresh_interval_var.set(str(config_manager.get('ui.refresh_interval', 5)))
        self.request_timeout_var.set(str(config_manager.get('advanced.request_timeout', 10)))
        
        self.update_url_display()
    
    def update_url_display(self, *args):
        """更新URL显示"""
        try:
            host = self.host_var.get() or 'localhost'
            port = self.port_var.get() or '8000'
            use_https = self.use_https_var.get()
            
            protocol = 'https' if use_https else 'http'
            ws_protocol = 'wss' if use_https else 'ws'
            
            api_url = f"{protocol}://{host}:{port}"
            ws_url = f"{ws_protocol}://{host}:{port}"
            
            self.api_url_label.config(text=f"API地址: {api_url}")
            self.ws_url_label.config(text=f"WebSocket地址: {ws_url}")
        except:
            pass
    
    def set_preset(self, host, port, use_https):
        """设置预设服务器"""
        self.host_var.set(host)
        self.port_var.set(str(port))
        self.use_https_var.set(use_https)
    
    def set_lan_server(self):
        """设置局域网服务器"""
        # 简单的IP输入对话框
        ip = simpledialog.askstring("局域网服务器", "请输入服务器IP地址:", initialvalue="*************")
        if ip and self.validate_ip(ip):
            self.set_preset(ip, 8000, False)
        elif ip:
            messagebox.showerror("错误", "无效的IP地址格式")
    
    def validate_ip(self, ip):
        """验证IP地址格式"""
        pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if re.match(pattern, ip):
            parts = ip.split('.')
            return all(0 <= int(part) <= 255 for part in parts)
        return False
    
    def test_connection(self):
        """测试连接"""
        try:
            import requests
            host = self.host_var.get() or 'localhost'
            port = self.port_var.get() or '8000'
            use_https = self.use_https_var.get()
            
            protocol = 'https' if use_https else 'http'
            url = f"{protocol}://{host}:{port}/docs"
            
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                messagebox.showinfo("连接测试", "✅ 连接成功！服务器响应正常。")
            else:
                messagebox.showwarning("连接测试", f"⚠️ 服务器响应异常，状态码: {response.status_code}")
        except Exception as e:
            messagebox.showerror("连接测试", f"❌ 连接失败: {str(e)}")
    
    def save_and_close(self):
        """保存配置并关闭"""
        try:
            # 验证输入
            host = self.host_var.get().strip()
            port_str = self.port_var.get().strip()
            
            if not host:
                messagebox.showerror("错误", "主机地址不能为空")
                return
            
            try:
                port = int(port_str)
                if not (1 <= port <= 65535):
                    raise ValueError()
            except ValueError:
                messagebox.showerror("错误", "端口必须是1-65535之间的数字")
                return
            
            try:
                refresh_interval = int(self.refresh_interval_var.get())
                if refresh_interval < 1:
                    raise ValueError()
            except ValueError:
                messagebox.showerror("错误", "刷新间隔必须是大于0的整数")
                return
            
            try:
                request_timeout = int(self.request_timeout_var.get())
                if request_timeout < 1:
                    raise ValueError()
            except ValueError:
                messagebox.showerror("错误", "请求超时必须是大于0的整数")
                return
            
            # 保存配置
            config_manager.update_server_config(host, port, self.use_https_var.get())
            config_manager.set('ui.auto_refresh', self.auto_refresh_var.get())
            config_manager.set('ui.refresh_interval', refresh_interval)
            config_manager.set('advanced.request_timeout', request_timeout)
            
            if config_manager.save_config():
                messagebox.showinfo("成功", "配置已保存！重启应用后生效。")
                self.result = True
                self.dialog.destroy()
            else:
                messagebox.showerror("错误", "保存配置失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"保存配置时发生错误: {str(e)}")
    
    def cancel(self):
        """取消"""
        self.result = False
        self.dialog.destroy()
    
    def reset_to_default(self):
        """重置为默认配置"""
        if messagebox.askyesno("确认", "确定要重置为默认配置吗？"):
            config_manager.reset_to_default()
            self.load_current_config()
            messagebox.showinfo("成功", "已重置为默认配置")
    
    def export_config(self):
        """导出配置"""
        file_path = filedialog.asksaveasfilename(
            title="导出配置",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            if config_manager.export_config(file_path):
                messagebox.showinfo("成功", "配置已导出")
            else:
                messagebox.showerror("错误", "导出配置失败")
    
    def import_config(self):
        """导入配置"""
        file_path = filedialog.askopenfilename(
            title="导入配置",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if file_path:
            if config_manager.import_config(file_path):
                self.load_current_config()
                messagebox.showinfo("成功", "配置已导入")
            else:
                messagebox.showerror("错误", "导入配置失败")
