#!/bin/bash

# 快速成功构建脚本

echo "=== 快速成功构建 ==="

# 1. 检查Docker
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker异常"
    exit 1
fi

# 2. 清理环境
docker system prune -f >/dev/null 2>&1

# 3. 创建最简单的在线Dockerfile
echo "创建简单的在线Dockerfile..."
cat > Dockerfile.success << 'EOF'
FROM python:3.9-slim

WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# 复制requirements文件
COPY requirements.txt .

# 安装依赖 - 使用清华源
RUN pip install --no-cache-dir \
    --index-url https://pypi.tuna.tsinghua.edu.cn/simple \
    --trusted-host pypi.tuna.tsinghua.edu.cn \
    --timeout 600 \
    -r requirements.txt

# 复制应用代码
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF

# 4. 构建
echo "开始构建..."
export DOCKER_BUILDKIT=0

if docker build -f Dockerfile.success -t wb_backend:latest .; then
    echo "✅ 构建成功！"
    
    # 显示镜像信息
    echo ""
    echo "镜像信息："
    docker images wb_backend:latest
    
    echo ""
    echo "🎉 构建完成！"
    echo ""
    echo "测试运行："
    echo "  docker run --rm -p 8000:8000 wb_backend:latest"
    echo ""
    echo "后台运行："
    echo "  docker run -d -p 8000:8000 --name wb_backend wb_backend:latest"
    echo ""
    echo "使用docker-compose："
    echo "  docker-compose up -d"
    
    # 清理临时文件
    rm -f Dockerfile.success
    
else
    echo "❌ 构建失败"
    rm -f Dockerfile.success
    exit 1
fi
