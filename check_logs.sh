#!/bin/bash

# 检查服务启动失败的具体日志

echo "=== 服务启动失败日志分析 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 1. 检查MySQL/MariaDB日志
echo "1. MySQL/MariaDB 启动日志"
echo "========================="

if systemctl list-unit-files | grep -q mysqld; then
    log_info "MySQL服务日志 (最近20行):"
    journalctl -u mysqld --no-pager -n 20 2>/dev/null || echo "无法获取mysqld日志"
    echo ""
    
    log_info "MySQL错误日志文件:"
    error_logs=("/var/log/mysqld.log" "/var/log/mysql/error.log" "/var/lib/mysql/$(hostname).err")
    for log_file in "${error_logs[@]}"; do
        if [ -f "$log_file" ]; then
            echo "  ✅ $log_file 存在"
            echo "  最后10行:"
            tail -10 "$log_file" 2>/dev/null | sed 's/^/    /'
            echo ""
        fi
    done
fi

if systemctl list-unit-files | grep -q mariadb; then
    log_info "MariaDB服务日志 (最近20行):"
    journalctl -u mariadb --no-pager -n 20 2>/dev/null || echo "无法获取mariadb日志"
    echo ""
fi

# 2. 检查Redis日志
echo "2. Redis 启动日志"
echo "=================="

if systemctl list-unit-files | grep -q redis; then
    log_info "Redis服务日志 (最近20行):"
    journalctl -u redis --no-pager -n 20 2>/dev/null || echo "无法获取redis日志"
    echo ""
    
    log_info "Redis日志文件:"
    redis_logs=("/var/log/redis/redis-server.log" "/var/log/redis.log")
    for log_file in "${redis_logs[@]}"; do
        if [ -f "$log_file" ]; then
            echo "  ✅ $log_file 存在"
            echo "  最后10行:"
            tail -10 "$log_file" 2>/dev/null | sed 's/^/    /'
            echo ""
        fi
    done
fi

# 3. 检查系统启动日志中的相关错误
echo "3. 系统启动日志中的数据库相关错误"
echo "================================="

log_info "搜索MySQL相关错误:"
journalctl --no-pager -n 50 | grep -i mysql | tail -10 || echo "未找到MySQL相关日志"

log_info "搜索MariaDB相关错误:"
journalctl --no-pager -n 50 | grep -i mariadb | tail -10 || echo "未找到MariaDB相关日志"

log_info "搜索Redis相关错误:"
journalctl --no-pager -n 50 | grep -i redis | tail -10 || echo "未找到Redis相关日志"

echo ""

# 4. 检查常见的启动失败原因
echo "4. 常见启动失败原因检查"
echo "======================="

log_info "检查磁盘空间 (数据库需要足够空间):"
df -h / | tail -1

log_info "检查内存使用 (数据库需要足够内存):"
free -h | grep Mem

log_info "检查/tmp目录权限 (MySQL需要):"
ls -ld /tmp

log_info "检查MySQL数据目录:"
if [ -d "/var/lib/mysql" ]; then
    echo "  ✅ /var/lib/mysql 存在"
    echo "  权限: $(ls -ld /var/lib/mysql)"
    echo "  所有者: $(stat -c '%U:%G' /var/lib/mysql)"
    
    # 检查是否已初始化
    if [ -f "/var/lib/mysql/mysql/user.frm" ] || [ -f "/var/lib/mysql/mysql/user.MYD" ]; then
        echo "  ✅ 数据库已初始化"
    else
        echo "  ❌ 数据库未初始化"
        log_warn "需要运行: mysql_install_db 或 mysqld --initialize"
    fi
else
    echo "  ❌ /var/lib/mysql 不存在"
fi

log_info "检查MySQL用户:"
if id mysql >/dev/null 2>&1; then
    echo "  ✅ mysql用户存在: $(id mysql)"
else
    echo "  ❌ mysql用户不存在"
    log_warn "需要创建mysql用户"
fi

log_info "检查Redis数据目录:"
redis_data_dirs=("/var/lib/redis" "/var/lib/redis-server")
for dir in "${redis_data_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "  ✅ $dir 存在"
        echo "  权限: $(ls -ld $dir)"
        echo "  所有者: $(stat -c '%U:%G' $dir)"
    fi
done

log_info "检查Redis用户:"
if id redis >/dev/null 2>&1; then
    echo "  ✅ redis用户存在: $(id redis)"
else
    echo "  ❌ redis用户不存在"
fi

echo ""

# 5. 尝试手动启动并显示实时错误
echo "5. 手动启动测试"
echo "==============="

log_info "尝试手动启动MySQL (如果安装了):"
if command -v mysqld >/dev/null 2>&1; then
    echo "  测试mysqld --help:"
    mysqld --help 2>&1 | head -3 || echo "  mysqld命令执行失败"
    
    echo "  测试mysqld --version:"
    mysqld --version 2>&1 || echo "  mysqld版本检查失败"
fi

if command -v mariadbd >/dev/null 2>&1; then
    echo "  测试mariadbd --version:"
    mariadbd --version 2>&1 || echo "  mariadbd版本检查失败"
fi

log_info "尝试手动启动Redis (如果安装了):"
if command -v redis-server >/dev/null 2>&1; then
    echo "  测试redis-server --version:"
    redis-server --version 2>&1 || echo "  redis-server版本检查失败"
    
    echo "  测试redis-server配置:"
    redis-server --test-config 2>&1 || echo "  Redis配置测试失败"
fi

echo ""
echo "=== 日志分析完成 ==="
echo ""
log_info "如果需要实时查看日志，使用以下命令:"
echo "  journalctl -u mysqld -f    # MySQL实时日志"
echo "  journalctl -u mariadb -f   # MariaDB实时日志"  
echo "  journalctl -u redis -f     # Redis实时日志"
