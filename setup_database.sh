#!/bin/bash

# 数据库配置脚本

echo "=== 配置MySQL数据库 ==="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查MySQL是否运行
if ! systemctl is-active mysqld >/dev/null 2>&1; then
    log_error "MySQL服务未运行，请先启动MySQL"
    echo "systemctl start mysqld"
    exit 1
fi

log_info "MySQL服务正在运行"

# 提示用户输入root密码
echo ""
log_warn "请输入MySQL root密码（如果是首次安装，可能需要先运行 mysql_secure_installation）"
read -s -p "MySQL root密码: " ROOT_PASSWORD
echo ""

# 测试连接
if ! mysql -u root -p"$ROOT_PASSWORD" -e "SELECT 1;" >/dev/null 2>&1; then
    log_error "无法连接到MySQL，请检查密码"
    exit 1
fi

log_info "MySQL连接成功"

# 创建数据库和用户
log_info "创建数据库和用户..."

mysql -u root -p"$ROOT_PASSWORD" << 'EOF'
-- 创建数据库
CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER IF NOT EXISTS 'wb_user'@'localhost' IDENTIFIED BY 'wb_password';

-- 授权
GRANT ALL PRIVILEGES ON wb.* TO 'wb_user'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示结果
SHOW DATABASES LIKE 'wb';
SELECT User, Host FROM mysql.user WHERE User = 'wb_user';
EOF

if [ $? -eq 0 ]; then
    log_info "数据库配置成功"
    echo ""
    log_info "数据库信息:"
    echo "  - 数据库名: wb"
    echo "  - 用户名: wb_user"
    echo "  - 密码: wb_password"
    echo "  - 主机: localhost"
    echo "  - 端口: 3306"
    echo ""
    log_warn "建议修改默认密码！"
    echo ""
    log_info "连接字符串:"
    echo "  mysql+pymysql://wb_user:wb_password@localhost:3306/wb"
else
    log_error "数据库配置失败"
    exit 1
fi

# 测试新用户连接
log_info "测试新用户连接..."
if mysql -u wb_user -pwb_password -e "USE wb; SELECT 'Database connection test successful' as result;" 2>/dev/null; then
    log_info "新用户连接测试成功"
else
    log_error "新用户连接测试失败"
fi

echo ""
log_info "数据库配置完成！"
