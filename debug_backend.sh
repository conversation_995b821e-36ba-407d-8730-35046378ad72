#!/bin/bash

# 调试后端容器问题

echo "=== 调试后端容器问题 ==="

# 1. 查看容器详细状态
echo "1. 容器详细状态..."
docker inspect wb_backend --format='{{.State.Status}}: {{.State.Error}}'

# 2. 查看容器日志
echo ""
echo "2. 容器启动日志..."
docker logs wb_backend --tail 50

# 3. 检查应用文件结构
echo ""
echo "3. 检查应用文件结构..."
echo "app目录结构:"
if [ -d "app" ]; then
    find app -name "*.py" | head -10
else
    echo "❌ app目录不存在"
fi

echo ""
echo "检查main.py文件:"
if [ -f "app/main.py" ]; then
    echo "✅ app/main.py 存在"
    echo "文件大小: $(wc -l app/main.py)"
else
    echo "❌ app/main.py 不存在"
fi

# 4. 检查requirements.txt
echo ""
echo "4. 检查requirements.txt..."
if [ -f "requirements.txt" ]; then
    echo "✅ requirements.txt 存在"
    echo "内容:"
    cat requirements.txt
else
    echo "❌ requirements.txt 不存在"
fi

# 5. 尝试手动运行容器
echo ""
echo "5. 尝试手动运行容器进行调试..."
echo "停止当前容器..."
docker stop wb_backend 2>/dev/null || true

echo "手动运行容器（交互模式）..."
echo "运行命令: docker run --rm -it --name wb_backend_debug wb_backend:latest /bin/bash"
echo "您可以在容器内手动测试:"
echo "  cd /app"
echo "  ls -la"
echo "  python -c 'import app.main'"
echo "  uvicorn app.main:app --host 0.0.0.0 --port 8000"

# 6. 检查数据库连接
echo ""
echo "6. 检查数据库连接..."
echo "MySQL容器状态:"
docker exec wb_mysql mysqladmin ping -h localhost -u root -p123456 2>/dev/null && echo "✅ MySQL正常" || echo "❌ MySQL异常"

echo ""
echo "测试数据库连接:"
docker exec wb_mysql mysql -u wb_user -pwb_password -e "SELECT 1;" 2>/dev/null && echo "✅ 数据库用户正常" || echo "❌ 数据库用户异常"

# 7. 创建简化的测试容器
echo ""
echo "7. 创建简化测试..."
cat > test_app.py << 'EOF'
from fastapi import FastAPI

app = FastAPI()

@app.get("/")
def read_root():
    return {"Hello": "World"}

@app.get("/health")
def health_check():
    return {"status": "ok"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

echo "创建了简化测试应用: test_app.py"
echo ""
echo "可以测试运行:"
echo "  docker run --rm -p 8000:8000 -v \$(pwd):/app -w /app python:3.9-slim python test_app.py"

echo ""
echo "=== 调试信息收集完成 ==="
echo ""
echo "常见问题及解决方案:"
echo "1. 如果是导入错误 - 检查app/main.py文件"
echo "2. 如果是数据库连接错误 - 检查环境变量"
echo "3. 如果是端口绑定错误 - 检查端口配置"
echo "4. 如果是依赖缺失 - 重新构建镜像"
