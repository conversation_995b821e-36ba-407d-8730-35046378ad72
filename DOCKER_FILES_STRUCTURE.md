# 📁 Docker部署文件结构

```
fastApiProject/
├── 🐳 Docker配置文件
│   ├── Dockerfile                 # 后端应用容器化配置
│   ├── docker-compose.yml         # 多服务编排配置
│   ├── .dockerignore              # Docker构建忽略文件
│   └── .env.example               # 环境变量配置模板
│
├── 🚀 部署脚本
│   ├── deploy.sh                  # Linux/macOS部署脚本
│   ├── deploy.bat                 # Windows部署脚本
│   └── validate_docker_setup.py   # 配置验证脚本
│
├── 🌐 Nginx配置
│   ├── nginx/
│   │   ├── nginx.conf             # Nginx主配置
│   │   └── conf.d/
│   │       └── default.conf       # 站点配置
│   └── nginx/ssl/                 # SSL证书目录
│
├── 🗄️ 数据库配置
│   └── mysql/
│       └── init/                  # 数据库初始化脚本
│
├── 📝 应用代码
│   ├── app/                       # FastAPI应用
│   ├── requirements.txt           # Python依赖
│   └── alembic/                   # 数据库迁移
│
├── 📊 日志目录
│   └── logs/
│       ├── nginx/                 # Nginx日志
│       └── app/                   # 应用日志
│
└── 📖 文档
    ├── DOCKER_DEPLOY.md           # 详细部署文档
    └── DOCKER_FILES_STRUCTURE.md  # 文件结构说明
```

## 🔧 核心配置文件说明

### 1. Dockerfile
- **基础镜像**: python:3.9-slim
- **工作目录**: /app
- **端口暴露**: 8000
- **健康检查**: curl -f http://localhost:8000/health
- **安全**: 非root用户运行

### 2. docker-compose.yml
- **服务编排**: MySQL + Redis + Backend + Nginx
- **网络**: 自定义bridge网络
- **数据卷**: 持久化存储
- **健康检查**: 所有服务都有健康检查
- **依赖管理**: 服务启动顺序控制

### 3. 环境配置 (.env.example)
```env
# 数据库配置
MYSQL_HOST=mysql
MYSQL_PASSWORD=wb_password
DATABASE_URL=mysql+pymysql://wb_user:wb_password@mysql:3306/wb

# 安全配置
SECRET_KEY=your-secret-key-change-in-production

# 应用配置
DEBUG=false
LOG_LEVEL=info
```

### 4. Nginx配置
- **反向代理**: 后端API代理
- **WebSocket支持**: 升级连接处理
- **静态文件**: 高效服务
- **安全头**: XSS、CSRF防护
- **Gzip压缩**: 性能优化

## 🚀 部署脚本功能

### deploy.sh (Linux/macOS)
- ✅ Docker环境检查
- ✅ 目录结构创建
- ✅ 环境配置设置
- ✅ 镜像构建
- ✅ 服务启动
- ✅ 健康检查
- ✅ 访问信息显示

### deploy.bat (Windows)
- ✅ 相同功能的Windows版本
- ✅ PowerShell兼容
- ✅ 中文支持
- ✅ 错误处理

## 📊 数据持久化

### MySQL数据卷
```yaml
volumes:
  mysql_data:
    driver: local
```

### Redis数据卷
```yaml
volumes:
  redis_data:
    driver: local
```

### 日志挂载
```yaml
volumes:
  - ./logs:/app/logs
  - ./logs/nginx:/var/log/nginx
```

## 🔐 安全配置

### 1. 网络隔离
- 自定义Docker网络
- 服务间通信隔离
- 端口映射控制

### 2. 用户权限
- 非root用户运行
- 最小权限原则
- 文件权限控制

### 3. 环境变量
- 敏感信息环境变量化
- 生产环境密钥管理
- 配置文件分离

## 🎯 使用场景

### 1. 开发环境
```bash
# 快速启动开发环境
./deploy.sh
```

### 2. 测试环境
```bash
# 构建测试镜像
./deploy.sh build
```

### 3. 生产环境
```bash
# 生产部署（需要修改.env配置）
cp .env.example .env
# 编辑.env文件设置生产配置
./deploy.sh
```

## 📈 性能优化

### 1. 镜像优化
- 多阶段构建
- 最小化镜像大小
- 缓存层优化

### 2. 资源限制
```yaml
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 2G
```

### 3. 健康检查
- 应用健康检查
- 数据库连接检查
- 服务依赖检查

## 🛠️ 维护命令

### 日常维护
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 更新服务
docker-compose up -d --build
```

### 数据备份
```bash
# 备份数据库
docker-compose exec mysql mysqldump -u root -p wb > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u root -p wb < backup.sql
```

## 🔍 故障排除

### 常见问题
1. **端口冲突**: 修改docker-compose.yml端口映射
2. **内存不足**: 增加Docker内存限制
3. **权限问题**: 检查文件和目录权限
4. **网络问题**: 检查防火墙和网络配置

### 调试命令
```bash
# 进入容器调试
docker-compose exec backend bash

# 查看容器资源使用
docker stats

# 查看网络连接
docker network ls
```
