#!/usr/bin/env python3
"""
测试离线设备任务bug
"""

import requests
import json
import time

def test_offline_task_bug():
    """测试没有设备连接时创建任务的bug"""
    print('🐛 重现bug：没有设备连接时创建任务')
    print('='*50)

    try:
        # 创建任务
        task_data = {
            'task_type': 'like',
            'target_scope': 'single',
            'target_id': 3,  # 使用离线设备
            'parameters': {
                'blogger_id': 'bug_test_blogger',
                'like_id': 'bug_test_like',
                'delay_click': 1000
            },
            'delay_group': 2000,
            'delay_like': 1000
        }
        
        print('📤 创建任务...')
        print(f'任务数据: {json.dumps(task_data, indent=2)}')
        
        response = requests.post('http://localhost:8000/tasks/', json=task_data, timeout=10)
        print(f'📊 创建状态码: {response.status_code}')
        
        if response.status_code == 200:
            task = response.json()
            task_id = task.get('id')
            initial_status = task.get('status')
            print(f'✅ 任务创建成功: ID={task_id}')
            print(f'初始状态: {initial_status}')
            
            # 等待并检查任务状态变化
            print(f'\n⏳ 监控任务状态变化...')
            for i in range(10):
                time.sleep(1)
                
                # 检查任务状态
                task_response = requests.get(f'http://localhost:8000/tasks/{task_id}')
                if task_response.status_code == 200:
                    updated_task = task_response.json()
                    status = updated_task.get('status')
                    print(f'  第{i+1}秒: 状态={status}')
                    
                    if status != initial_status:
                        if status in ['completed', 'failed']:
                            print(f'\n🐛 BUG确认: 任务在没有设备连接的情况下变为 {status}!')
                            print(f'这是不正确的行为！')
                            print(f'任务详情:')
                            for key, value in updated_task.items():
                                print(f'  {key}: {value}')
                            return True
                        else:
                            print(f'\n📊 任务状态变化: {initial_status} -> {status}')
                else:
                    print(f'  第{i+1}秒: 无法获取任务状态')
            else:
                print(f'\n✅ 正常：10秒内任务状态保持为 {initial_status}')
                return False
                
        else:
            print(f'❌ 任务创建失败: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ 测试异常: {e}')
        return False

if __name__ == "__main__":
    test_offline_task_bug()
