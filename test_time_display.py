#!/usr/bin/env python3
"""
测试时间显示修复
"""

import requests
import json
import sys
import os
from datetime import datetime

# 添加frontend路径以导入时间工具
sys.path.append(os.path.join(os.path.dirname(__file__), 'frontend'))

def test_time_display():
    """测试时间显示修复"""
    print('🕐 测试任务管理时间显示修复')
    print('='*50)
    
    try:
        task_data = {
            'task_type': 'like',
            'target_scope': 'single',
            'target_id': 3,
            'parameters': {
                'blogger_id': 'time_test_blogger',
                'like_id': 'time_test_like',
                'delay_click': 1000
            },
            'delay_group': 2000,
            'delay_like': 1000
        }
        
        print(f'📤 创建时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")} (本地时间)')
        response = requests.post('http://localhost:8000/tasks/', json=task_data, timeout=10)
        print(f'📊 创建状态码: {response.status_code}')
        
        if response.status_code == 200:
            task = response.json()
            task_id = task.get('id')
            create_time = task.get('create_time')
            print(f'✅ 任务创建成功: ID={task_id}')
            print(f'🕐 服务器返回的创建时间: {create_time}')
            
            # 测试直接使用服务器时间
            print(f'📅 服务器时间格式: {create_time}')

            # 测试前端格式化逻辑
            try:
                if 'T' in create_time:
                    dt = datetime.fromisoformat(create_time.replace('Z', ''))
                else:
                    dt = datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S')

                # 列表显示格式
                list_format = dt.strftime('%Y-%m-%d %H:%M')
                # 详情显示格式
                detail_format = dt.strftime('%Y-%m-%d %H:%M:%S')

                print(f'📋 任务列表显示: {list_format}')
                print(f'📄 任务详情显示: {detail_format}')

                print(f'\n✅ 时间显示修复验证:')
                print(f'   - 修复前: 只显示时分 (如: 11:27)')
                print(f'   - 修复后: 显示年月日时分 (如: {list_format})')
                print(f'   - 时间来源: 直接使用服务器返回时间')
                print(f'   - 无需时区转换: 服务器已返回正确时间')

            except Exception as e:
                print(f'⚠️ 时间格式化失败: {e}')
                print(f'📅 原始时间: {create_time}')
                
        else:
            print(f'❌ 任务创建失败: {response.text}')
            
    except Exception as e:
        print(f'❌ 测试异常: {e}')

if __name__ == "__main__":
    test_time_display()
