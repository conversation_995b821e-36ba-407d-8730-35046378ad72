#!/bin/bash

# CentOS系统检测脚本
# 检查系统环境和依赖

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统版本
check_system() {
    log_info "检查系统信息..."
    
    if [ -f /etc/centos-release ]; then
        CENTOS_VERSION=$(cat /etc/centos-release)
        log_success "系统版本: $CENTOS_VERSION"
        
        # 检查是否是CentOS 7或8
        if grep -q "CentOS Linux release 7" /etc/centos-release; then
            CENTOS_MAJOR=7
            log_info "检测到CentOS 7"
        elif grep -q "CentOS Linux release 8" /etc/centos-release; then
            CENTOS_MAJOR=8
            log_info "检测到CentOS 8"
        elif grep -q "CentOS Stream" /etc/centos-release; then
            CENTOS_MAJOR=8
            log_info "检测到CentOS Stream"
        else
            log_warning "未识别的CentOS版本，将按CentOS 7处理"
            CENTOS_MAJOR=7
        fi
    else
        log_error "这不是CentOS系统"
        exit 1
    fi
    
    # 显示系统信息
    echo "系统架构: $(uname -m)"
    echo "内核版本: $(uname -r)"
    echo "可用内存: $(free -h | grep Mem | awk '{print $2}')"
    echo "可用磁盘: $(df -h / | tail -1 | awk '{print $4}')"
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    
    if ping -c 1 8.8.8.8 >/dev/null 2>&1; then
        log_success "网络连接正常"
    else
        log_error "网络连接失败，请检查网络设置"
        exit 1
    fi
    
    # 检查DNS解析
    if nslookup docker.com >/dev/null 2>&1; then
        log_success "DNS解析正常"
    else
        log_warning "DNS解析可能有问题"
    fi
}

# 检查防火墙状态
check_firewall() {
    log_info "检查防火墙状态..."
    
    if systemctl is-active --quiet firewalld; then
        log_warning "firewalld正在运行，可能需要开放端口"
        echo "当前防火墙规则:"
        firewall-cmd --list-all 2>/dev/null || echo "无法获取防火墙规则"
    else
        log_info "firewalld未运行"
    fi
    
    # 检查SELinux
    if command -v getenforce >/dev/null 2>&1; then
        SELINUX_STATUS=$(getenforce)
        if [ "$SELINUX_STATUS" = "Enforcing" ]; then
            log_warning "SELinux处于强制模式，可能影响Docker运行"
        else
            log_info "SELinux状态: $SELINUX_STATUS"
        fi
    fi
}

# 检查已安装的软件
check_installed_software() {
    log_info "检查已安装的软件..."
    
    # 检查Docker
    if command -v docker >/dev/null 2>&1; then
        DOCKER_VERSION=$(docker --version)
        log_success "Docker已安装: $DOCKER_VERSION"
        
        # 检查Docker服务状态
        if systemctl is-active --quiet docker; then
            log_success "Docker服务正在运行"
        else
            log_warning "Docker服务未运行"
        fi
    else
        log_warning "Docker未安装"
    fi
    
    # 检查Docker Compose
    if command -v docker-compose >/dev/null 2>&1; then
        COMPOSE_VERSION=$(docker-compose --version)
        log_success "Docker Compose已安装: $COMPOSE_VERSION"
    else
        log_warning "Docker Compose未安装"
    fi
    
    # 检查Git
    if command -v git >/dev/null 2>&1; then
        GIT_VERSION=$(git --version)
        log_success "Git已安装: $GIT_VERSION"
    else
        log_warning "Git未安装"
    fi
    
    # 检查Python
    if command -v python3 >/dev/null 2>&1; then
        PYTHON_VERSION=$(python3 --version)
        log_success "Python3已安装: $PYTHON_VERSION"
    else
        log_warning "Python3未安装"
    fi
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用情况..."
    
    PORTS=(80 443 3306 6379 8000)
    
    for port in "${PORTS[@]}"; do
        if netstat -tuln | grep ":$port " >/dev/null 2>&1; then
            log_warning "端口 $port 已被占用"
            echo "占用进程: $(netstat -tulnp | grep ":$port " | awk '{print $7}')"
        else
            log_success "端口 $port 可用"
        fi
    done
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."
    
    # 检查根目录空间
    ROOT_AVAILABLE=$(df / | tail -1 | awk '{print $4}')
    ROOT_AVAILABLE_GB=$((ROOT_AVAILABLE / 1024 / 1024))
    
    if [ $ROOT_AVAILABLE_GB -lt 10 ]; then
        log_error "根目录可用空间不足10GB，当前: ${ROOT_AVAILABLE_GB}GB"
    elif [ $ROOT_AVAILABLE_GB -lt 20 ]; then
        log_warning "根目录可用空间较少，当前: ${ROOT_AVAILABLE_GB}GB，建议至少20GB"
    else
        log_success "根目录可用空间充足: ${ROOT_AVAILABLE_GB}GB"
    fi
    
    # 检查/var目录空间（Docker镜像存储位置）
    if mountpoint -q /var; then
        VAR_AVAILABLE=$(df /var | tail -1 | awk '{print $4}')
        VAR_AVAILABLE_GB=$((VAR_AVAILABLE / 1024 / 1024))
        log_info "/var目录可用空间: ${VAR_AVAILABLE_GB}GB"
    fi
}

# 生成部署建议
generate_recommendations() {
    log_info "生成部署建议..."
    
    echo ""
    echo "=================================="
    echo "🎯 CentOS部署建议"
    echo "=================================="
    echo ""
    
    if [ "$CENTOS_MAJOR" = "7" ]; then
        echo "📋 CentOS 7 部署步骤:"
        echo "1. 安装Docker CE:"
        echo "   sudo yum install -y yum-utils"
        echo "   sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo"
        echo "   sudo yum install -y docker-ce docker-ce-cli containerd.io"
        echo ""
        echo "2. 安装Docker Compose:"
        echo "   sudo curl -L \"https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose"
        echo "   sudo chmod +x /usr/local/bin/docker-compose"
        echo ""
    else
        echo "📋 CentOS 8/Stream 部署步骤:"
        echo "1. 安装Docker CE:"
        echo "   sudo dnf config-manager --add-repo=https://download.docker.com/linux/centos/docker-ce.repo"
        echo "   sudo dnf install -y docker-ce docker-ce-cli containerd.io"
        echo ""
        echo "2. 安装Docker Compose:"
        echo "   sudo curl -L \"https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose"
        echo "   sudo chmod +x /usr/local/bin/docker-compose"
        echo ""
    fi
    
    echo "3. 启动Docker服务:"
    echo "   sudo systemctl start docker"
    echo "   sudo systemctl enable docker"
    echo ""
    
    echo "4. 配置防火墙:"
    echo "   sudo firewall-cmd --permanent --add-port=80/tcp"
    echo "   sudo firewall-cmd --permanent --add-port=443/tcp"
    echo "   sudo firewall-cmd --permanent --add-port=8000/tcp"
    echo "   sudo firewall-cmd --reload"
    echo ""
    
    echo "5. 配置SELinux (如果需要):"
    echo "   sudo setsebool -P httpd_can_network_connect 1"
    echo "   sudo setsebool -P container_manage_cgroup 1"
    echo ""
    
    echo "6. 部署应用:"
    echo "   chmod +x deploy_centos.sh"
    echo "   sudo ./deploy_centos.sh"
    echo ""
}

# 主函数
main() {
    echo "🐧 CentOS系统检测和部署准备"
    echo "=================================="
    echo ""
    
    check_system
    echo ""
    check_network
    echo ""
    check_firewall
    echo ""
    check_installed_software
    echo ""
    check_ports
    echo ""
    check_disk_space
    echo ""
    generate_recommendations
}

# 运行主函数
main
