#!/bin/bash

# 彻底修复数据库连接问题

echo "=== 彻底修复数据库连接 ==="

# 1. 检查当前状态
echo "1. 检查当前状态..."
docker-compose ps

# 2. 修复数据库用户和权限
echo ""
echo "2. 修复数据库用户和权限..."
docker-compose exec mysql mysql -u root -p123456 << 'EOF'
-- 删除可能存在的用户
DROP USER IF EXISTS 'wb_user'@'%';
DROP USER IF EXISTS 'wb_user'@'localhost';

-- 重新创建用户
CREATE USER 'wb_user'@'%' IDENTIFIED BY 'wb_password';

-- 创建数据库
CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 授权
GRANT ALL PRIVILEGES ON wb.* TO 'wb_user'@'%';
GRANT ALL PRIVILEGES ON *.* TO 'wb_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 验证用户
SELECT User, Host FROM mysql.user WHERE User='wb_user';
SELECT SCHEMA_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME='wb';
EOF

echo "✅ 数据库用户和权限已修复"

# 3. 创建正确的数据库配置
echo ""
echo "3. 创建数据库配置文件..."

cat > app/db.py << 'EOF'
from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
import time
import logging

logger = logging.getLogger(__name__)

# 数据库配置 - 使用Docker容器名
MYSQL_HOST = os.getenv("MYSQL_HOST", "mysql")
MYSQL_PORT = int(os.getenv("MYSQL_PORT", "3306"))
MYSQL_USER = os.getenv("MYSQL_USER", "wb_user")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD", "wb_password")
MYSQL_DATABASE = os.getenv("MYSQL_DATABASE", "wb")

# 构建数据库URL
DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}?charset=utf8mb4"

print(f"[INFO] 数据库连接配置:")
print(f"  主机: {MYSQL_HOST}")
print(f"  端口: {MYSQL_PORT}")
print(f"  用户: {MYSQL_USER}")
print(f"  数据库: {MYSQL_DATABASE}")
print(f"  连接URL: {DATABASE_URL}")

# 创建数据库引擎
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_timeout=20,
    max_overflow=0,
    echo=False,  # 设为True可以看到SQL日志
    connect_args={
        "connect_timeout": 60,
        "read_timeout": 60,
        "write_timeout": 60,
    }
)

# 测试数据库连接
def test_database_connection():
    """测试数据库连接"""
    max_retries = 30
    for i in range(max_retries):
        try:
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1 as test"))
                row = result.fetchone()
                if row and row[0] == 1:
                    print(f"[SUCCESS] 数据库连接成功 (尝试 {i+1}/{max_retries})")
                    return True
        except Exception as e:
            print(f"[RETRY] 数据库连接失败 (尝试 {i+1}/{max_retries}): {e}")
            if i < max_retries - 1:
                time.sleep(2)
            else:
                print(f"[ERROR] 数据库连接最终失败: {e}")
                return False
    return False

# 创建会话
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基类
Base = declarative_base()

# 获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 初始化时测试连接
if __name__ != "__main__":
    print("[INFO] 正在测试数据库连接...")
    test_database_connection()
EOF

# 4. 创建带数据库重试的main.py
echo ""
echo "4. 创建应用主文件..."

cat > app/main.py << 'EOF'
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
import asyncio
from datetime import datetime
import socket
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="微博任务管理系统",
    description="微博任务管理系统API",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量跟踪数据库状态
DATABASE_CONNECTED = False

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

@app.get("/")
async def root():
    return {
        "message": "微博任务管理系统",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "deployment": "docker",
        "database_connected": DATABASE_CONNECTED
    }

@app.get("/health")
async def health_check():
    global DATABASE_CONNECTED
    
    try:
        from app.db import engine, text
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        DATABASE_CONNECTED = True
        db_status = "connected"
    except Exception as e:
        DATABASE_CONNECTED = False
        db_status = f"disconnected: {str(e)}"
    
    return {
        "status": "healthy" if DATABASE_CONNECTED else "partial",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "service": "wb_backend",
        "deployment": "docker",
        "server_ip": get_local_ip(),
        "database": db_status,
        "database_connected": DATABASE_CONNECTED
    }

@app.get("/system")
async def system_info():
    return {
        "hostname": socket.gethostname(),
        "local_ip": get_local_ip(),
        "environment": {
            "MYSQL_HOST": os.getenv("MYSQL_HOST", "not_set"),
            "MYSQL_USER": os.getenv("MYSQL_USER", "not_set"),
            "MYSQL_DATABASE": os.getenv("MYSQL_DATABASE", "not_set"),
            "REDIS_URL": os.getenv("REDIS_URL", "not_set")
        },
        "deployment": "docker",
        "database_connected": DATABASE_CONNECTED
    }

async def init_database():
    """异步初始化数据库"""
    global DATABASE_CONNECTED
    
    try:
        logger.info("开始初始化数据库...")
        
        # 导入数据库模块
        from app.db import test_database_connection, engine, Base
        
        # 测试连接
        if test_database_connection():
            logger.info("数据库连接成功，开始创建表...")
            
            # 创建表
            Base.metadata.create_all(bind=engine)
            logger.info("数据库表创建成功")
            
            DATABASE_CONNECTED = True
            
            # 尝试导入路由
            try:
                from app.routes import device, task, group, task_sync
                from app.websocket import ws_manager
                
                app.include_router(device.router, prefix="/api", tags=["Devices"])
                app.include_router(task.router, prefix="/api", tags=["Tasks"])
                app.include_router(group.router, prefix="/api", tags=["Groups"])
                app.include_router(task_sync.router, prefix="/api", tags=["Task Sync"])
                app.include_router(ws_manager.router, prefix="/ws", tags=["WebSocket"])
                
                logger.info("API路由加载成功")
            except Exception as e:
                logger.warning(f"API路由加载失败: {e}")
                # 添加基础API端点
                await add_basic_api_routes()
        else:
            logger.error("数据库连接失败")
            DATABASE_CONNECTED = False
            # 添加基础API端点
            await add_basic_api_routes()
            
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        DATABASE_CONNECTED = False
        # 添加基础API端点
        await add_basic_api_routes()

async def add_basic_api_routes():
    """添加基础API路由"""
    
    @app.get("/api/devices")
    async def get_devices():
        return {"devices": [], "message": "数据库未连接" if not DATABASE_CONNECTED else "正常"}
    
    @app.get("/api/tasks")
    async def get_tasks():
        return {"tasks": [], "message": "数据库未连接" if not DATABASE_CONNECTED else "正常"}
    
    @app.get("/api/groups")
    async def get_groups():
        return {"groups": [], "message": "数据库未连接" if not DATABASE_CONNECTED else "正常"}
    
    logger.info("基础API路由已添加")

@app.on_event("startup")
async def startup_event():
    logger.info("微博任务管理系统启动中...")
    
    # 异步初始化数据库
    asyncio.create_task(init_database())
    
    logger.info("应用启动完成")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

# 5. 停止后端容器
echo ""
echo "5. 停止后端容器..."
docker-compose stop backend

# 6. 重新构建镜像
echo ""
echo "6. 重新构建镜像..."
export DOCKER_BUILDKIT=0
docker-compose build backend --no-cache

# 7. 启动后端容器
echo ""
echo "7. 启动后端容器..."
docker-compose up -d backend

# 8. 等待启动并检查
echo ""
echo "8. 等待服务启动..."
sleep 30

echo "检查容器状态..."
docker-compose ps

echo ""
echo "检查后端日志..."
docker-compose logs backend --tail 15

echo ""
echo "测试数据库连接..."
docker-compose exec backend python -c "
from app.db import test_database_connection
if test_database_connection():
    print('✅ 数据库连接成功')
else:
    print('❌ 数据库连接失败')
"

echo ""
echo "测试API访问..."
sleep 5
curl -s http://localhost/health | python3 -m json.tool 2>/dev/null || curl -s http://localhost/health

echo ""
echo "=== 数据库修复完成 ==="
