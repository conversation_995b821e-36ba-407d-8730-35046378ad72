# 前端配置文件

import os

class Config:
    """前端配置类"""
    
    # 服务器配置
    SERVER_IP = "***********"  # 您的服务器IP
    SERVER_PORT = 80           # 使用80端口（代理端口）
    
    # API基础URL
    BASE_URL = f"http://{SERVER_IP}:{SERVER_PORT}"
    API_BASE_URL = f"{BASE_URL}/api"
    
    # WebSocket配置
    WS_URL = f"ws://{SERVER_IP}:{SERVER_PORT}/ws"
    
    # API端点
    ENDPOINTS = {
        # 基础端点
        "health": f"{BASE_URL}/health",
        "system": f"{BASE_URL}/system",
        "docs": f"{BASE_URL}/docs",
        
        # 设备管理
        "devices": f"{API_BASE_URL}/devices",
        "device_detail": f"{API_BASE_URL}/devices/{{device_id}}",
        
        # 任务管理
        "tasks": f"{API_BASE_URL}/tasks",
        "task_detail": f"{API_BASE_URL}/tasks/{{task_id}}",
        "create_task": f"{API_BASE_URL}/tasks",
        
        # 分组管理
        "groups": f"{API_BASE_URL}/groups",
        "group_detail": f"{API_BASE_URL}/groups/{{group_id}}",
        "create_group": f"{API_BASE_URL}/groups",
        
        # 任务同步
        "task_sync": f"{API_BASE_URL}/task-sync",
    }
    
    # 请求配置
    REQUEST_TIMEOUT = 30  # 请求超时时间（秒）
    RETRY_COUNT = 3       # 重试次数
    
    # UI配置
    WINDOW_TITLE = "微博任务管理系统"
    WINDOW_SIZE = "1200x800"
    REFRESH_INTERVAL = 5000  # 自动刷新间隔（毫秒）
    
    @classmethod
    def get_api_url(cls, endpoint_name, **kwargs):
        """获取API URL"""
        url = cls.ENDPOINTS.get(endpoint_name, "")
        if kwargs:
            url = url.format(**kwargs)
        return url
    
    @classmethod
    def test_connection(cls):
        """测试连接"""
        import requests
        try:
            response = requests.get(cls.ENDPOINTS["health"], timeout=5)
            return response.status_code == 200
        except:
            return False

# 开发环境配置
class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    
# 生产环境配置  
class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    REQUEST_TIMEOUT = 60

# 根据环境变量选择配置
config = DevelopmentConfig if os.getenv("DEBUG", "true").lower() == "true" else ProductionConfig
