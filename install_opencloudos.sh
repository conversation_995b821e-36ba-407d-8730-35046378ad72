#!/bin/bash

# OpenCloudOS 9一键安装脚本
# 自动检测系统环境并安装微博设备管理系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    clear
    echo "======================================================="
    echo "🚀 微博设备管理系统 - OpenCloudOS 9一键安装脚本"
    echo "======================================================="
    echo ""
    echo "本脚本将自动完成以下操作："
    echo "✅ 检测OpenCloudOS 9系统版本"
    echo "✅ 配置腾讯云镜像源和软件仓库"
    echo "✅ 安装Docker CE和Docker Compose"
    echo "✅ 配置防火墙、SELinux和Docker镜像加速"
    echo "✅ 部署微博设备管理系统"
    echo "✅ 配置系统服务和管理脚本"
    echo ""
    echo "🐧 OpenCloudOS 9特性："
    echo "• 基于CentOS 8/RHEL 8，使用DNF包管理器"
    echo "• 腾讯云优化，支持腾讯云镜像加速"
    echo "• 企业级稳定性和安全性"
    echo "• 完全兼容CentOS 8生态"
    echo ""
    echo "⚠️  注意事项："
    echo "• 请确保以root用户或sudo权限运行"
    echo "• 请确保服务器有稳定的网络连接"
    echo "• 建议至少4GB内存和20GB磁盘空间"
    echo "• 支持x86_64和aarch64架构"
    echo ""
    
    read -p "是否继续安装？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "安装已取消"
        exit 0
    fi
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户或sudo权限运行此脚本"
        echo "使用方法: sudo bash $0"
        exit 1
    fi
    
    # 检查系统类型
    if [ -f /etc/opencloudos-release ]; then
        SYSTEM_INFO=$(cat /etc/opencloudos-release)
        log_success "检测到OpenCloudOS系统: $SYSTEM_INFO"
    elif [ -f /etc/centos-release ]; then
        SYSTEM_INFO=$(cat /etc/centos-release)
        log_success "检测到兼容系统: $SYSTEM_INFO"
    elif [ -f /etc/redhat-release ]; then
        SYSTEM_INFO=$(cat /etc/redhat-release)
        log_success "检测到兼容系统: $SYSTEM_INFO"
    else
        log_error "不支持的系统，此脚本仅支持OpenCloudOS/CentOS/RHEL系列"
        exit 1
    fi
    
    # 检查架构
    ARCH=$(uname -m)
    if [ "$ARCH" != "x86_64" ] && [ "$ARCH" != "aarch64" ]; then
        log_warning "检测到架构: $ARCH，可能不完全支持"
    else
        log_success "系统架构: $ARCH"
    fi
    
    # 检查内存
    MEMORY_GB=$(free -g | awk 'NR==2{print $2}')
    if [ $MEMORY_GB -lt 2 ]; then
        log_warning "内存不足4GB，当前: ${MEMORY_GB}GB，可能影响性能"
    else
        log_success "内存检查通过: ${MEMORY_GB}GB"
    fi
    
    # 检查磁盘空间
    DISK_GB=$(df / | tail -1 | awk '{print int($4/1024/1024)}')
    if [ $DISK_GB -lt 10 ]; then
        log_error "磁盘空间不足，至少需要10GB，当前可用: ${DISK_GB}GB"
        exit 1
    else
        log_success "磁盘空间检查通过: ${DISK_GB}GB可用"
    fi
    
    # 检查网络连接
    if ! ping -c 1 ******* >/dev/null 2>&1; then
        log_error "网络连接失败，请检查网络设置"
        exit 1
    fi
    
    # 检查腾讯云网络
    if curl -s --connect-timeout 5 http://mirrors.tencent.com >/dev/null; then
        log_success "腾讯云镜像源连接正常，将使用加速源"
        USE_TENCENT_MIRROR=true
    else
        log_warning "腾讯云镜像源连接失败，将使用默认源"
        USE_TENCENT_MIRROR=false
    fi
    
    log_success "系统要求检查通过"
}

# 下载项目文件
download_project() {
    log_info "准备项目文件..."
    
    # 如果当前目录没有项目文件，提示用户
    if [ ! -f "docker-compose.yml" ] || [ ! -f "Dockerfile" ]; then
        log_warning "未找到项目文件"
        echo ""
        echo "请确保以下文件存在于当前目录："
        echo "• docker-compose.yml"
        echo "• Dockerfile"
        echo "• requirements.txt"
        echo "• app/ 目录"
        echo "• nginx/ 目录"
        echo ""
        echo "您可以："
        echo "1. 将项目文件上传到服务器当前目录"
        echo "2. 使用git克隆项目: git clone <项目地址>"
        echo "3. 使用scp上传项目文件"
        echo "4. 使用FTP/SFTP工具上传"
        echo ""
        echo "OpenCloudOS 9推荐方法："
        echo "# 如果有Git仓库"
        echo "git clone <your-repo-url>"
        echo "cd <project-directory>"
        echo ""
        echo "# 或使用SCP从本地上传"
        echo "scp -r fastApiProject/ root@your-server-ip:/root/"
        echo ""
        read -p "项目文件已准备好？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "请准备好项目文件后重新运行脚本"
            exit 1
        fi
    fi
    
    log_success "项目文件检查通过"
}

# 执行系统检测
run_system_check() {
    log_info "执行系统环境检测..."
    
    if [ -f "check_opencloudos_system.sh" ]; then
        chmod +x check_opencloudos_system.sh
        ./check_opencloudos_system.sh
    else
        log_warning "系统检测脚本不存在，跳过详细检测"
    fi
    
    echo ""
    read -p "是否继续安装？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "安装已取消"
        exit 0
    fi
}

# 执行部署
run_deployment() {
    log_info "开始部署系统..."
    
    if [ -f "deploy_opencloudos.sh" ]; then
        chmod +x deploy_opencloudos.sh
        ./deploy_opencloudos.sh
    else
        log_error "部署脚本不存在"
        exit 1
    fi
}

# 配置系统服务
configure_system_service() {
    log_info "配置系统服务..."
    
    # 创建systemd服务文件
    cat > /etc/systemd/system/wb-system.service << EOF
[Unit]
Description=微博设备管理系统
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/wb_system
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

    # 重载systemd配置
    systemctl daemon-reload
    systemctl enable wb-system.service
    
    log_success "系统服务配置完成"
}

# 创建管理脚本
create_management_scripts() {
    log_info "创建OpenCloudOS 9管理脚本..."
    
    # 创建管理脚本目录
    mkdir -p /usr/local/bin/wb-system
    
    # 创建状态检查脚本
    cat > /usr/local/bin/wb-system/status.sh << 'EOF'
#!/bin/bash
echo "=== OpenCloudOS 9 微博设备管理系统状态 ==="
echo "系统信息: $(cat /etc/opencloudos-release 2>/dev/null || cat /etc/redhat-release)"
echo "服务器IP: $(ip route get ******* | awk -F'src ' 'NR==1{split($2,a," ");print a[1]}')"
echo ""
cd /opt/wb_system
echo "=== 服务状态 ==="
docker-compose ps
echo ""
echo "=== 系统资源 ==="
docker stats --no-stream
echo ""
echo "=== 磁盘使用 ==="
df -h /opt/wb_system
echo ""
echo "=== 内存使用 ==="
free -h
EOF

    # 创建日志查看脚本
    cat > /usr/local/bin/wb-system/logs.sh << 'EOF'
#!/bin/bash
cd /opt/wb_system
if [ "$1" ]; then
    echo "查看 $1 服务日志..."
    docker-compose logs -f "$1"
else
    echo "查看所有服务日志..."
    docker-compose logs -f
fi
EOF

    # 创建备份脚本
    cat > /usr/local/bin/wb-system/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/wb_system/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p "$BACKUP_DIR"
cd /opt/wb_system

echo "开始备份数据库..."
MYSQL_PASSWORD=$(grep MYSQL_ROOT_PASSWORD .env | cut -d'=' -f2)
docker-compose exec -T mysql mysqladump -u root -p"$MYSQL_PASSWORD" wb > "$BACKUP_DIR/wb_backup_$DATE.sql"

if [ $? -eq 0 ]; then
    echo "备份完成: $BACKUP_DIR/wb_backup_$DATE.sql"
    
    # 压缩备份文件
    gzip "$BACKUP_DIR/wb_backup_$DATE.sql"
    echo "备份已压缩: $BACKUP_DIR/wb_backup_$DATE.sql.gz"
    
    # 清理7天前的备份
    find "$BACKUP_DIR" -name "wb_backup_*.sql.gz" -mtime +7 -delete
    echo "已清理7天前的备份文件"
else
    echo "备份失败，请检查数据库状态"
fi
EOF

    # 创建更新脚本
    cat > /usr/local/bin/wb-system/update.sh << 'EOF'
#!/bin/bash
echo "更新微博设备管理系统..."
cd /opt/wb_system

echo "停止服务..."
docker-compose down

echo "拉取最新镜像..."
docker-compose pull

echo "重新构建..."
docker-compose build --no-cache

echo "启动服务..."
docker-compose up -d

echo "更新完成！"
EOF

    # 设置执行权限
    chmod +x /usr/local/bin/wb-system/*.sh
    
    # 创建软链接
    ln -sf /usr/local/bin/wb-system/status.sh /usr/local/bin/wb-status
    ln -sf /usr/local/bin/wb-system/logs.sh /usr/local/bin/wb-logs
    ln -sf /usr/local/bin/wb-system/backup.sh /usr/local/bin/wb-backup
    ln -sf /usr/local/bin/wb-system/update.sh /usr/local/bin/wb-update
    
    log_success "管理脚本创建完成"
}

# 配置定时任务
configure_cron_jobs() {
    log_info "配置定时任务..."
    
    # 创建定时备份任务
    (crontab -l 2>/dev/null; echo "0 3 * * * /usr/local/bin/wb-backup >/dev/null 2>&1") | crontab -
    
    # 创建日志清理任务
    (crontab -l 2>/dev/null; echo "0 2 * * 0 docker system prune -f >/dev/null 2>&1") | crontab -
    
    log_success "定时任务配置完成"
    log_info "已配置每日3点自动备份数据库"
    log_info "已配置每周日2点清理Docker缓存"
}

# 显示安装完成信息
show_completion_info() {
    # 获取服务器IP
    SERVER_IP=$(ip route get ******* | awk -F"src " 'NR==1{split($2,a," ");print a[1]}')
    
    clear
    echo "======================================================="
    echo "🎉 OpenCloudOS 9 安装完成！"
    echo "======================================================="
    echo ""
    echo "🖥️  系统信息："
    echo "  操作系统: $(cat /etc/opencloudos-release 2>/dev/null || cat /etc/redhat-release)"
    echo "  服务器IP: $SERVER_IP"
    echo "  系统架构: $(uname -m)"
    echo ""
    echo "📱 访问地址："
    echo "  http://$SERVER_IP"
    echo "  http://$SERVER_IP/docs (API文档)"
    echo ""
    echo "🗄️ 数据库信息："
    echo "  主机: $SERVER_IP:3306"
    echo "  用户: wb_user"
    echo "  密码: 查看 /opt/wb_system/passwords.txt"
    echo ""
    echo "📊 管理命令："
    echo "  wb-status    # 查看系统状态"
    echo "  wb-logs      # 查看系统日志"
    echo "  wb-backup    # 备份数据库"
    echo "  wb-update    # 更新系统"
    echo ""
    echo "🔧 系统服务："
    echo "  systemctl start wb-system    # 启动服务"
    echo "  systemctl stop wb-system     # 停止服务"
    echo "  systemctl restart wb-system  # 重启服务"
    echo ""
    echo "📁 项目目录: /opt/wb_system"
    echo "🔑 密码文件: /opt/wb_system/passwords.txt"
    echo ""
    echo "⚙️ 前端配置："
    echo "  服务器地址: $SERVER_IP:8000"
    echo ""
    echo "🐧 OpenCloudOS 9特性："
    echo "  ✅ 腾讯云镜像加速已配置"
    echo "  ✅ DNF包管理器优化"
    echo "  ✅ 企业级安全配置"
    echo "  ✅ 自动备份和维护"
    echo ""
    echo "======================================================="
    echo "安装完成！请在前端应用中配置服务器地址。"
    echo "======================================================="
}

# 主函数
main() {
    show_welcome
    check_requirements
    download_project
    run_system_check
    run_deployment
    configure_system_service
    create_management_scripts
    configure_cron_jobs
    show_completion_info
}

# 运行主函数
main
