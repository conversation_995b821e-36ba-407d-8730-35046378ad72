#!/bin/bash

# 修复Docker网络问题并使用原生部署

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

echo "========================================"
echo "  修复Docker网络问题"
echo "========================================"
echo ""

# 1. 检查网络连接
log_step "1. 检查网络连接"

if ping -c 1 8.8.8.8 >/dev/null 2>&1; then
    log_info "✅ 基础网络连接正常"
else
    log_error "❌ 基础网络连接失败"
    exit 1
fi

if nslookup docker.io >/dev/null 2>&1; then
    log_info "✅ DNS解析正常"
else
    log_warn "⚠️ DNS解析有问题，尝试修复..."
    echo "nameserver 8.8.8.8" | sudo tee /etc/resolv.conf
    echo "nameserver 114.114.114.114" | sudo tee -a /etc/resolv.conf
fi

# 2. 停止Docker服务，使用原生部署
log_step "2. 由于网络问题，切换到原生部署"

# 停止Docker服务
docker-compose down 2>/dev/null || true
sudo systemctl stop docker 2>/dev/null || true

# 3. 安装MySQL
log_step "3. 安装和配置MySQL"

if ! command -v mysql >/dev/null 2>&1; then
    log_info "安装MySQL..."
    sudo dnf install -y mysql-server
fi

# 启动MySQL
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 获取临时密码（如果是新安装）
if [ -f /var/log/mysqld.log ]; then
    TEMP_PASSWORD=$(sudo grep 'temporary password' /var/log/mysqld.log 2>/dev/null | tail -1 | awk '{print $NF}' || echo "")
    if [ -n "$TEMP_PASSWORD" ]; then
        log_info "MySQL临时密码: $TEMP_PASSWORD"
        log_warn "请手动设置MySQL root密码为: 123456"
        echo "运行: mysql -u root -p'$TEMP_PASSWORD'"
        echo "然后执行: ALTER USER 'root'@'localhost' IDENTIFIED BY '123456';"
    fi
fi

# 4. 创建数据库
log_step "4. 创建数据库"

# 尝试创建数据库
if mysql -u root -p123456 -e "CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; FLUSH PRIVILEGES;" 2>/dev/null; then
    log_info "✅ 数据库创建成功"
else
    log_warn "⚠️ 数据库创建失败，可能需要手动设置密码"
    log_warn "请手动运行以下命令:"
    echo "mysql -u root -p"
    echo "CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    echo "FLUSH PRIVILEGES;"
    echo ""
    echo "然后按回车继续..."
    read -p "按回车继续..."
fi

# 5. 创建Python虚拟环境
log_step "5. 创建Python环境"

if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

source venv/bin/activate

# 安装依赖
pip install --upgrade pip
pip install -r requirements.txt

# 6. 修改配置文件
log_step "6. 修改配置文件"

# 确保config.py使用localhost
cat > app/config.py << 'EOF'
import os

# MySQL数据库配置
MYSQL_USER = os.getenv("MYSQL_USER", "root")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD", "123456")
MYSQL_HOST = os.getenv("MYSQL_HOST", "127.0.0.1")
MYSQL_PORT = os.getenv("MYSQL_PORT", "3306")
MYSQL_DB = os.getenv("MYSQL_DB", "wb")

DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}?charset=utf8mb4"

print(f"[CONFIG] Database: {MYSQL_USER}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}")
EOF

# 7. 测试数据库连接
log_step "7. 测试数据库连接"

python3 -c "
import sys
sys.path.append('.')
try:
    from app.config import DATABASE_URL
    from sqlalchemy import create_engine, text
    engine = create_engine(DATABASE_URL)
    with engine.connect() as conn:
        conn.execute(text('SELECT 1'))
    print('✅ 数据库连接成功')
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    sys.exit(1)
"

# 8. 创建启动脚本
log_step "8. 创建启动脚本"

cat > start_native.sh << 'EOF'
#!/bin/bash

cd "$(dirname "$0")"

# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export MYSQL_USER=root
export MYSQL_PASSWORD=123456
export MYSQL_HOST=127.0.0.1
export MYSQL_PORT=3306
export MYSQL_DB=wb

echo "启动微博任务管理系统 (原生部署)..."
echo "访问地址: http://***********:8000"
echo "API文档: http://***********:8000/docs"
echo ""

# 启动应用
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
EOF

chmod +x start_native.sh

# 9. 创建代理脚本
cat > start_proxy.sh << 'EOF'
#!/bin/bash

echo "启动80端口代理..."

python3 -c "
import http.server
import socketserver
import urllib.request
from datetime import datetime

class ProxyHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        self.proxy_request()
    
    def do_POST(self):
        self.proxy_request()
    
    def proxy_request(self):
        try:
            target_url = f'http://127.0.0.1:8000{self.path}'
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length) if content_length > 0 else None
            
            req = urllib.request.Request(target_url, data=post_data, method=self.command)
            
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'content-length']:
                    req.add_header(header, value)
            
            with urllib.request.urlopen(req, timeout=30) as response:
                self.send_response(response.getcode())
                for header, value in response.headers.items():
                    if header.lower() not in ['server', 'date']:
                        self.send_header(header, value)
                self.end_headers()
                self.wfile.write(response.read())
                
        except Exception as e:
            self.send_error(502, f'Bad Gateway: {str(e)}')

with socketserver.TCPServer(('', 80), ProxyHandler) as httpd:
    print('🚀 代理服务器启动在80端口')
    print('🔄 转发到: http://127.0.0.1:8000')
    print('🌐 访问地址: http://***********/')
    httpd.serve_forever()
"
EOF

chmod +x start_proxy.sh

# 10. 创建系统服务
log_step "10. 创建系统服务"

sudo tee /etc/systemd/system/wb-native.service << EOF
[Unit]
Description=微博任务管理系统 (原生部署)
After=network.target mysqld.service

[Service]
Type=simple
User=$(whoami)
WorkingDirectory=$(pwd)
Environment=PATH=$(pwd)/venv/bin
Environment=MYSQL_USER=root
Environment=MYSQL_PASSWORD=123456
Environment=MYSQL_HOST=127.0.0.1
Environment=MYSQL_PORT=3306
Environment=MYSQL_DB=wb
ExecStart=$(pwd)/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

sudo tee /etc/systemd/system/wb-proxy.service << EOF
[Unit]
Description=微博任务管理系统代理
After=network.target wb-native.service

[Service]
Type=simple
User=root
WorkingDirectory=$(pwd)
ExecStart=/usr/bin/python3 $(pwd)/start_proxy.sh
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl enable wb-native
sudo systemctl enable wb-proxy

echo ""
echo "========================================"
echo "  原生部署完成！"
echo "========================================"
echo ""
log_info "启动方式:"
echo "  手动启动: ./start_native.sh"
echo "  代理启动: sudo ./start_proxy.sh"
echo "  服务启动: sudo systemctl start wb-native && sudo systemctl start wb-proxy"
echo ""
log_info "访问地址:"
echo "  直接访问: http://***********:8000"
echo "  代理访问: http://***********"
echo ""
log_info "测试命令:"
echo "  curl http://localhost:8000/health"
echo "  mysql -u root -p123456 -e 'SELECT 1;'"
echo ""
echo "🎉 原生部署完成！避免了Docker网络问题"
