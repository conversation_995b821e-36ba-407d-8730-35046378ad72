#!/bin/bash

# Docker构建优化脚本
# 解决网络连接问题和依赖安装失败

set -e

echo "=== Docker构建优化脚本 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否运行
check_docker() {
    log_info "检查Docker状态..."
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker未运行，请启动Docker服务"
        exit 1
    fi
    log_info "Docker运行正常"
}

# 清理旧的构建缓存
clean_cache() {
    log_info "清理Docker构建缓存..."
    docker builder prune -f >/dev/null 2>&1 || true
    docker system prune -f >/dev/null 2>&1 || true
    log_info "缓存清理完成"
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    
    # 测试DNS解析
    if ! nslookup pypi.tuna.tsinghua.edu.cn >/dev/null 2>&1; then
        log_warn "清华源DNS解析失败，尝试其他源"
        return 1
    fi
    
    # 测试网络连接
    if ! curl -s --connect-timeout 10 https://pypi.tuna.tsinghua.edu.cn/simple/ >/dev/null; then
        log_warn "清华源网络连接失败"
        return 1
    fi
    
    log_info "网络连接正常"
    return 0
}

# 构建Docker镜像
build_image() {
    local attempt=$1
    log_info "开始构建Docker镜像 (尝试 $attempt/3)..."
    
    # 使用BuildKit进行构建
    export DOCKER_BUILDKIT=1
    export BUILDKIT_PROGRESS=plain
    
    # 构建参数
    local build_args=(
        --no-cache
        --progress=plain
        --build-arg BUILDKIT_INLINE_CACHE=1
        --network=host
        -t wb_backend:latest
        .
    )
    
    if docker build "${build_args[@]}"; then
        log_info "Docker镜像构建成功"
        return 0
    else
        log_error "Docker镜像构建失败"
        return 1
    fi
}

# 主构建流程
main() {
    log_info "开始Docker构建优化流程..."
    
    # 检查Docker
    check_docker
    
    # 清理缓存
    clean_cache
    
    # 检查网络
    if ! check_network; then
        log_warn "网络检查失败，但继续构建..."
    fi
    
    # 尝试构建（最多3次）
    for attempt in {1..3}; do
        if build_image $attempt; then
            log_info "构建成功！"
            
            # 显示镜像信息
            log_info "镜像信息："
            docker images wb_backend:latest
            
            # 提示下一步
            echo ""
            log_info "构建完成！可以使用以下命令启动服务："
            echo "  docker-compose up -d"
            echo ""
            log_info "或者单独运行后端服务："
            echo "  docker run -p 8000:8000 wb_backend:latest"
            
            exit 0
        else
            if [ $attempt -lt 3 ]; then
                log_warn "构建失败，等待10秒后重试..."
                sleep 10
                clean_cache
            fi
        fi
    done
    
    log_error "构建失败，已尝试3次"
    
    # 提供故障排除建议
    echo ""
    log_info "故障排除建议："
    echo "1. 检查网络连接是否正常"
    echo "2. 尝试更换DNS服务器"
    echo "3. 检查防火墙设置"
    echo "4. 尝试使用VPN或代理"
    echo "5. 手动下载依赖包"
    
    exit 1
}

# 运行主函数
main "$@"
