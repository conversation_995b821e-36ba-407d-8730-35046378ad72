#!/usr/bin/env python3

"""
简化版后端 - 不依赖数据库，用于快速测试Docker部署
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
from datetime import datetime
import socket
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="微博任务管理系统",
    description="微博任务管理系统API - 简化版",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

@app.get("/")
async def root():
    return {
        "message": "微博任务管理系统",
        "status": "running",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "deployment": "docker-simple",
        "note": "简化版本，数据库功能暂未启用"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "service": "wb_backend",
        "deployment": "docker-simple",
        "server_ip": get_local_ip(),
        "database": "not_connected",
        "environment": {
            "MYSQL_HOST": os.getenv("MYSQL_HOST", "not_set"),
            "MYSQL_USER": os.getenv("MYSQL_USER", "not_set"),
            "REDIS_URL": os.getenv("REDIS_URL", "not_set")
        }
    }

@app.get("/system")
async def system_info():
    return {
        "hostname": socket.gethostname(),
        "local_ip": get_local_ip(),
        "python_version": os.sys.version,
        "working_directory": os.getcwd(),
        "environment_variables": {
            "MYSQL_HOST": os.getenv("MYSQL_HOST", "not_set"),
            "MYSQL_USER": os.getenv("MYSQL_USER", "not_set"),
            "MYSQL_PASSWORD": "***" if os.getenv("MYSQL_PASSWORD") else "not_set",
            "MYSQL_DATABASE": os.getenv("MYSQL_DATABASE", "not_set"),
            "REDIS_URL": os.getenv("REDIS_URL", "not_set")
        },
        "deployment": "docker-simple"
    }

# 模拟API端点
@app.get("/api/devices")
async def get_devices():
    return {
        "devices": [],
        "message": "设备列表API",
        "note": "数据库未连接，返回空列表",
        "status": "ok"
    }

@app.get("/api/tasks")
async def get_tasks():
    return {
        "tasks": [],
        "message": "任务列表API",
        "note": "数据库未连接，返回空列表",
        "status": "ok"
    }

@app.get("/api/groups")
async def get_groups():
    return {
        "groups": [],
        "message": "分组列表API",
        "note": "数据库未连接，返回空列表",
        "status": "ok"
    }

@app.post("/api/devices")
async def create_device():
    return {
        "message": "创建设备API",
        "note": "数据库未连接，模拟成功",
        "status": "simulated"
    }

@app.post("/api/tasks")
async def create_task():
    return {
        "message": "创建任务API",
        "note": "数据库未连接，模拟成功",
        "status": "simulated"
    }

@app.post("/api/groups")
async def create_group():
    return {
        "message": "创建分组API",
        "note": "数据库未连接，模拟成功",
        "status": "simulated"
    }

@app.on_event("startup")
async def startup_event():
    logger.info("微博任务管理系统启动成功 (简化版)")
    logger.info(f"服务运行在: {get_local_ip()}:8000")
    logger.info("注意: 这是简化版本，数据库功能未启用")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
