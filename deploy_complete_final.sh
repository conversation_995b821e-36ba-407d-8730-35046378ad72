#!/bin/bash

# 微博任务管理系统 - 完整一次性部署方案
# 基于您的后端代码结构，兼容宝塔面板

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

echo "========================================"
echo "  微博任务管理系统 - 完整部署"
echo "========================================"
echo "基于您的后端代码，一次性完成所有配置"
echo ""

# 1. 环境检查和准备
log_step "1. 环境检查和准备"

# 检查Python版本
PYTHON_VERSION=$(/usr/bin/python3 --version 2>&1)
log_info "Python版本: $PYTHON_VERSION"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    log_error "请使用root用户运行此脚本"
    exit 1
fi

# 2. 安装系统依赖
log_step "2. 安装系统依赖"

log_info "更新系统包..."
dnf update -y

log_info "安装基础依赖..."
dnf install -y gcc python3-devel mysql-devel pkg-config curl wget

# 3. 安装和配置MariaDB
log_step "3. 安装和配置MariaDB"

if systemctl is-active --quiet mariadb; then
    log_info "✅ MariaDB已运行"
else
    log_info "安装MariaDB..."
    dnf install -y mariadb-server mariadb-devel
    systemctl start mariadb
    systemctl enable mariadb
    
    log_info "配置MariaDB..."
    # 自动配置MariaDB
    mysql << 'EOF'
-- 设置root密码
ALTER USER 'root'@'localhost' IDENTIFIED BY '123456';

-- 创建数据库
CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示数据库
SHOW DATABASES;
EOF
    
    log_info "✅ MariaDB配置完成"
fi

# 4. 创建Python虚拟环境
log_step "4. 创建Python虚拟环境"

PROJECT_DIR="/www/wwwroot/fastApiProject"
cd "$PROJECT_DIR"

if [ -d "venv_wb" ]; then
    log_info "✅ 虚拟环境已存在"
else
    log_info "创建虚拟环境..."
    /usr/bin/python3 -m venv venv_wb
    log_info "✅ 虚拟环境创建成功"
fi

# 激活虚拟环境
source venv_wb/bin/activate

# 5. 安装Python依赖
log_step "5. 安装Python依赖"

log_info "升级pip..."
python -m pip install --upgrade pip

log_info "安装项目依赖..."
# 根据您的requirements.txt安装
python -m pip install \
    fastapi==0.115.12 \
    uvicorn==0.34.3 \
    sqlalchemy==2.0.41 \
    pymysql==1.1.1 \
    websockets==15.0.1 \
    requests==2.32.3 \
    pydantic==2.11.5 \
    python-dotenv==1.1.0 \
    PyYAML==6.0.2 \
    pytz==2024.2 \
    alembic==1.13.3

log_info "✅ Python依赖安装完成"

# 6. 配置数据库连接（保持您的原有配置结构）
log_step "6. 配置数据库连接"

# 备份原配置
cp app/config.py app/config.py.backup 2>/dev/null || true

# 确保config.py使用正确的数据库配置
cat > app/config.py << 'EOF'
# MySQL数据库配置
MYSQL_USER = "root"
MYSQL_PASSWORD = "123456"
MYSQL_HOST = "127.0.0.1"
MYSQL_PORT = "3306"
MYSQL_DB = "wb"

DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}?charset=utf8mb4"

print(f"[CONFIG] Database: {MYSQL_USER}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}")
EOF

log_info "✅ 数据库配置完成"

# 7. 测试数据库连接和表创建
log_step "7. 测试数据库连接和表创建"

python3 << 'EOF'
import sys
sys.path.append('.')

try:
    from app.db import engine, Base
    from app.models import device, task, group, device_status, group_task_status
    
    print("测试数据库连接...")
    with engine.connect() as conn:
        conn.execute("SELECT 1")
    print("✅ 数据库连接成功")
    
    print("创建数据库表...")
    Base.metadata.create_all(bind=engine)
    print("✅ 数据库表创建成功")
    
    # 显示创建的表
    with engine.connect() as conn:
        result = conn.execute("SHOW TABLES")
        tables = [row[0] for row in result]
        print(f"创建的表: {tables}")
        
except Exception as e:
    print(f"❌ 数据库测试失败: {e}")
    sys.exit(1)
EOF

# 8. 配置防火墙
log_step "8. 配置防火墙"

if systemctl is-active --quiet firewalld; then
    log_info "配置防火墙规则..."
    firewall-cmd --permanent --add-port=11234/tcp
    firewall-cmd --permanent --add-port=3306/tcp
    firewall-cmd --reload
    log_info "✅ 防火墙配置完成"
else
    log_warn "防火墙未运行，跳过配置"
fi

# 9. 创建启动脚本
log_step "9. 创建启动脚本"

cat > start_wb_system.sh << 'EOF'
#!/bin/bash

cd "$(dirname "$0")"

echo "========================================"
echo "  微博任务管理系统启动"
echo "========================================"

# 检查MariaDB状态
if ! systemctl is-active --quiet mariadb; then
    echo "启动MariaDB..."
    sudo systemctl start mariadb
    sleep 3
fi

# 激活虚拟环境
echo "激活Python环境..."
source venv_wb/bin/activate

# 验证数据库连接
echo "验证数据库连接..."
if mysql -u root -p123456 -e "USE wb; SELECT 1;" >/dev/null 2>&1; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败，请检查MariaDB状态"
    exit 1
fi

echo ""
echo "========================================"
echo "  系统信息"
echo "========================================"
echo "Python版本: $(python --version)"
echo "数据库: MariaDB (wb)"
echo "端口: 11234"
echo "访问地址: http://***********:11234"
echo "API文档: http://***********:11234/docs"
echo "健康检查: http://***********:11234/health"
echo "IP信息: http://***********:11234/ip"
echo "========================================"
echo ""

# 启动FastAPI应用
echo "🚀 启动微博任务管理系统..."
uvicorn app.main:app --host 0.0.0.0 --port 11234 --reload
EOF

chmod +x start_wb_system.sh

# 10. 创建系统服务
log_step "10. 创建系统服务"

cat > /etc/systemd/system/wb-system.service << EOF
[Unit]
Description=微博任务管理系统
After=network.target mariadb.service

[Service]
Type=simple
User=root
WorkingDirectory=$PROJECT_DIR
Environment=PATH=$PROJECT_DIR/venv_wb/bin
ExecStart=$PROJECT_DIR/venv_wb/bin/uvicorn app.main:app --host 0.0.0.0 --port 11234
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable wb-system

# 11. 创建管理脚本
log_step "11. 创建管理脚本"

cat > manage_wb.sh << 'EOF'
#!/bin/bash

case "$1" in
    start)
        echo "启动微博任务管理系统..."
        systemctl start wb-system
        ;;
    stop)
        echo "停止微博任务管理系统..."
        systemctl stop wb-system
        ;;
    restart)
        echo "重启微博任务管理系统..."
        systemctl restart wb-system
        ;;
    status)
        echo "系统状态:"
        systemctl status wb-system
        ;;
    logs)
        echo "查看日志:"
        journalctl -u wb-system -f
        ;;
    manual)
        echo "手动启动:"
        ./start_wb_system.sh
        ;;
    test)
        echo "测试访问:"
        curl http://localhost:11234/health
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs|manual|test}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动系统服务"
        echo "  stop    - 停止系统服务"
        echo "  restart - 重启系统服务"
        echo "  status  - 查看服务状态"
        echo "  logs    - 查看实时日志"
        echo "  manual  - 手动启动（调试用）"
        echo "  test    - 测试系统访问"
        exit 1
        ;;
esac
EOF

chmod +x manage_wb.sh

# 12. 最终测试
log_step "12. 最终测试"

log_info "测试系统启动..."
source venv_wb/bin/activate

# 快速测试启动
timeout 10s uvicorn app.main:app --host 127.0.0.1 --port 11235 &
TEST_PID=$!
sleep 5

if curl -s http://127.0.0.1:11235/health >/dev/null 2>&1; then
    log_info "✅ 系统测试成功"
else
    log_warn "⚠️ 系统测试未完全成功，但配置已完成"
fi

kill $TEST_PID 2>/dev/null || true

echo ""
echo "========================================"
echo "  🎉 部署完成！"
echo "========================================"
echo ""
log_info "系统配置:"
echo "  - Python版本: $PYTHON_VERSION"
echo "  - 数据库: MariaDB (wb)"
echo "  - 端口: 11234"
echo "  - 项目目录: $PROJECT_DIR"
echo "  - 虚拟环境: venv_wb/"
echo ""
log_info "启动方式:"
echo "  - 服务启动: ./manage_wb.sh start"
echo "  - 手动启动: ./start_wb_system.sh"
echo "  - 系统服务: systemctl start wb-system"
echo ""
log_info "访问地址:"
echo "  - 主页: http://***********:11234"
echo "  - API文档: http://***********:11234/docs"
echo "  - 健康检查: http://***********:11234/health"
echo "  - IP信息: http://***********:11234/ip"
echo ""
log_info "管理命令:"
echo "  - 查看状态: ./manage_wb.sh status"
echo "  - 查看日志: ./manage_wb.sh logs"
echo "  - 重启服务: ./manage_wb.sh restart"
echo "  - 测试访问: ./manage_wb.sh test"
echo ""
echo "🚀 现在可以启动系统了: ./start_wb_system.sh"
