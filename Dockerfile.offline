# 离线构建版本的Dockerfile
# 当网络连接有问题时使用此版本

FROM python:3.9-bullseye

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    DEBIAN_FRONTEND=noninteractive

# 安装系统依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制预下载的wheel文件（如果有的话）
COPY wheels/ ./wheels/ 2>/dev/null || true

# 复制requirements文件
COPY requirements.txt .

# 尝试从本地wheel安装，如果失败则从PyPI安装
RUN if [ -d "wheels" ] && [ "$(ls -A wheels)" ]; then \
        echo "使用本地wheel文件安装依赖..." && \
        pip install --no-index --find-links wheels -r requirements.txt; \
    else \
        echo "从PyPI安装依赖..." && \
        pip install --no-cache-dir -r requirements.txt; \
    fi

# 复制应用代码
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
