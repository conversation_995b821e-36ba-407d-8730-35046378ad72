# 数据库配置
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=wb_user
MYSQL_PASSWORD=wb_password
MYSQL_DATABASE=wb
DATABASE_URL=mysql+pymysql://wb_user:wb_password@mysql:3306/wb

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_URL=redis://redis:6379/0

# 应用配置
DEBUG=false
LOG_LEVEL=info
HOST=0.0.0.0
PORT=8000

# 安全配置
SECRET_KEY=your-secret-key-change-in-production-environment
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
ALLOWED_HEADERS=["*"]

# WebSocket配置
WEBSOCKET_HEARTBEAT_INTERVAL=30
WEBSOCKET_TIMEOUT=60

# 任务配置
TASK_TIMEOUT_MINUTES=5
MAX_CONCURRENT_TASKS=100
TASK_RETRY_COUNT=3

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=./uploads

# 日志配置
LOG_DIR=./logs
LOG_ROTATION=1 day
LOG_RETENTION=30 days
