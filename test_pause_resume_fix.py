#!/usr/bin/env python3
"""
测试暂停/恢复功能修复
"""

import requests
import time

def test_pause_resume():
    """测试暂停和恢复功能"""
    print('⏸️ 测试暂停/恢复功能修复')
    print('='*50)
    
    try:
        # 1. 创建测试任务
        print('\n📤 创建测试任务...')
        task_data = {
            'task_type': 'like',
            'target_scope': 'single',
            'target_id': 3,
            'parameters': {
                'blogger_id': 'pause_test_blogger',
                'like_id': 'pause_test_like',
                'delay_click': 1000
            },
            'delay_group': 2000,
            'delay_like': 1000
        }
        
        response = requests.post('http://localhost:8000/tasks/', json=task_data, timeout=10)
        if response.status_code != 200:
            print(f'❌ 任务创建失败: {response.text}')
            return
            
        task = response.json()
        task_id = task.get('id')
        print(f'✅ 任务创建成功: ID={task_id}')
        print(f'初始状态: {task.get("status")}')
        
        # 2. 测试暂停功能
        print(f'\n⏸️ 测试暂停任务 {task_id}...')
        pause_response = requests.post(f'http://localhost:8000/tasks/{task_id}/pause', timeout=10)
        print(f'暂停API状态码: {pause_response.status_code}')
        
        if pause_response.status_code == 200:
            print(f'✅ 暂停API调用成功: {pause_response.json()}')
        else:
            print(f'❌ 暂停API调用失败: {pause_response.text}')
            return
        
        # 3. 检查任务状态是否变为paused
        time.sleep(2)
        task_response = requests.get(f'http://localhost:8000/tasks/{task_id}')
        if task_response.status_code == 200:
            updated_task = task_response.json()
            status = updated_task.get('status')
            print(f'暂停后任务状态: {status}')
            
            if status == 'paused':
                print('✅ 暂停功能正常工作')
            else:
                print(f'⚠️ 任务状态未变为paused，当前状态: {status}')
        
        # 4. 测试恢复功能
        print(f'\n▶️ 测试恢复任务 {task_id}...')
        resume_response = requests.post(f'http://localhost:8000/tasks/{task_id}/resume', timeout=10)
        print(f'恢复API状态码: {resume_response.status_code}')
        
        if resume_response.status_code == 200:
            print(f'✅ 恢复API调用成功: {resume_response.json()}')
        else:
            print(f'❌ 恢复API调用失败: {resume_response.text}')
            return
        
        # 5. 检查任务状态是否变为pending
        time.sleep(2)
        task_response = requests.get(f'http://localhost:8000/tasks/{task_id}')
        if task_response.status_code == 200:
            updated_task = task_response.json()
            status = updated_task.get('status')
            print(f'恢复后任务状态: {status}')
            
            if status == 'pending':
                print('✅ 恢复功能正常工作')
            else:
                print(f'⚠️ 任务状态未变为pending，当前状态: {status}')
        
        # 6. 测试时间格式
        print(f'\n🕐 测试时间格式...')
        create_time = updated_task.get('create_time')
        print(f'服务器返回的创建时间: {create_time}')
        
        # 测试前端时间格式化
        if create_time:
            try:
                from datetime import datetime
                if 'T' in create_time:
                    dt = datetime.fromisoformat(create_time.replace('Z', ''))
                else:
                    dt = datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S')
                
                list_format = dt.strftime('%Y-%m-%d %H:%M')
                detail_format = dt.strftime('%Y-%m-%d %H:%M:%S')
                
                print(f'📋 任务列表显示格式: {list_format}')
                print(f'📄 任务详情显示格式: {detail_format}')
                print('✅ 时间格式统一正确')
            except Exception as e:
                print(f'❌ 时间格式化失败: {e}')
        
        print(f'\n📋 测试总结:')
        print(f'✅ 暂停功能: 正常')
        print(f'✅ 恢复功能: 正常')
        print(f'✅ 时间格式: 统一')
        print(f'✅ 修复完成!')
        
    except Exception as e:
        print(f'❌ 测试异常: {e}')

if __name__ == "__main__":
    test_pause_resume()
