# 🐳 Docker容器部署指南

本文档详细介绍如何使用Docker容器部署微博设备管理系统。

## 📋 目录

- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [详细部署步骤](#详细部署步骤)
- [配置说明](#配置说明)
- [服务管理](#服务管理)
- [故障排除](#故障排除)
- [性能优化](#性能优化)

## 🔧 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 20GB以上可用空间
- **网络**: 稳定的网络连接

### 软件要求
- **Docker**: 20.10.0+
- **Docker Compose**: 2.0.0+
- **操作系统**: 
  - Linux (推荐 Ubuntu 20.04+)
  - Windows 10/11 with WSL2
  - macOS 10.15+

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <your-repo-url>
cd fastApiProject
```

### 2. 一键部署

**Linux/macOS:**
```bash
chmod +x deploy.sh
./deploy.sh
```

**Windows:**
```cmd
deploy.bat
```

### 3. 访问系统
- 主服务: http://localhost
- API文档: http://localhost/docs
- 健康检查: http://localhost/health

## 📝 详细部署步骤

### 1. 环境准备

#### 安装Docker (Ubuntu)
```bash
# 更新包索引
sudo apt update

# 安装必要的包
sudo apt install apt-transport-https ca-certificates curl gnupg lsb-release

# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 设置稳定版仓库
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装Docker Engine
sudo apt update
sudo apt install docker-ce docker-ce-cli containerd.io

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 安装Docker (Windows)
1. 下载并安装 [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)
2. 启用WSL2后端
3. 重启计算机

### 2. 配置环境变量

复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，修改以下配置：
```env
# 数据库配置
MYSQL_PASSWORD=your_secure_password
MYSQL_DATABASE=wb

# 安全配置
SECRET_KEY=your_very_secure_secret_key

# 应用配置
DEBUG=false
LOG_LEVEL=info
```

### 3. 手动部署步骤

#### 步骤1: 构建镜像
```bash
docker-compose build --no-cache
```

#### 步骤2: 启动数据库
```bash
docker-compose up -d mysql redis
```

#### 步骤3: 等待数据库启动
```bash
# 检查数据库状态
docker-compose logs mysql
```

#### 步骤4: 启动后端服务
```bash
docker-compose up -d backend
```

#### 步骤5: 启动Nginx代理
```bash
docker-compose up -d nginx
```

#### 步骤6: 验证部署
```bash
# 检查所有服务状态
docker-compose ps

# 检查健康状态
curl http://localhost/health
```

## ⚙️ 配置说明

### 服务架构
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Nginx    │───▶│   Backend   │───▶│    MySQL    │
│   (Port 80) │    │ (Port 8000) │    │ (Port 3306) │
└─────────────┘    └─────────────┘    └─────────────┘
                           │
                           ▼
                   ┌─────────────┐
                   │    Redis    │
                   │ (Port 6379) │
                   └─────────────┘
```

### 端口映射
| 服务 | 容器端口 | 主机端口 | 说明 |
|------|----------|----------|------|
| Nginx | 80 | 80 | HTTP代理 |
| Nginx | 443 | 443 | HTTPS代理 |
| Backend | 8000 | 8000 | API服务 |
| MySQL | 3306 | 3306 | 数据库 |
| Redis | 6379 | 6379 | 缓存 |

### 数据卷
| 卷名 | 挂载点 | 说明 |
|------|--------|------|
| mysql_data | /var/lib/mysql | MySQL数据 |
| redis_data | /data | Redis数据 |
| ./logs | /app/logs | 应用日志 |

## 🛠️ 服务管理

### 常用命令

#### 查看服务状态
```bash
docker-compose ps
```

#### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f mysql
```

#### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend
```

#### 停止服务
```bash
# 停止所有服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v
```

#### 更新服务
```bash
# 重新构建并启动
docker-compose up -d --build
```

### 数据库管理

#### 连接数据库
```bash
# 进入MySQL容器
docker-compose exec mysql mysql -u wb_user -p wb

# 或使用root用户
docker-compose exec mysql mysql -u root -p
```

#### 备份数据库
```bash
# 备份数据库
docker-compose exec mysql mysqldump -u root -p wb > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u root -p wb < backup.sql
```

## 🔧 故障排除

### 常见问题

#### 1. 端口冲突
**问题**: 端口已被占用
**解决**: 修改 `docker-compose.yml` 中的端口映射

#### 2. 数据库连接失败
**问题**: 后端无法连接数据库
**解决**: 
```bash
# 检查数据库状态
docker-compose logs mysql

# 重启数据库
docker-compose restart mysql
```

#### 3. 内存不足
**问题**: 容器启动失败，内存不足
**解决**: 增加系统内存或调整Docker内存限制

#### 4. 权限问题
**问题**: 文件权限错误
**解决**:
```bash
# 修复日志目录权限
sudo chown -R $USER:$USER logs/

# 修复数据目录权限
sudo chown -R 999:999 mysql/
```

### 日志分析

#### 查看详细日志
```bash
# 后端应用日志
docker-compose logs backend | tail -100

# 数据库日志
docker-compose logs mysql | tail -100

# Nginx访问日志
docker-compose exec nginx tail -f /var/log/nginx/access.log
```

#### 健康检查
```bash
# 检查服务健康状态
curl -v http://localhost/health

# 检查数据库连接
docker-compose exec backend python -c "
from app.db import engine
try:
    with engine.connect() as conn:
        result = conn.execute('SELECT 1')
        print('Database connection: OK')
except Exception as e:
    print(f'Database connection failed: {e}')
"
```

## 🚀 性能优化

### 1. 资源限制
在 `docker-compose.yml` 中添加资源限制：
```yaml
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

### 2. 数据库优化
创建 `mysql/conf.d/my.cnf`：
```ini
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
max_connections = 200
query_cache_size = 64M
```

### 3. Nginx优化
在 `nginx/nginx.conf` 中优化配置：
```nginx
worker_processes auto;
worker_connections 2048;
keepalive_timeout 65;
gzip on;
gzip_comp_level 6;
```

## 📊 监控和维护

### 1. 系统监控
```bash
# 查看容器资源使用情况
docker stats

# 查看磁盘使用情况
docker system df
```

### 2. 定期维护
```bash
# 清理未使用的镜像
docker image prune -f

# 清理未使用的容器
docker container prune -f

# 清理未使用的网络
docker network prune -f
```

### 3. 自动备份脚本
创建 `backup.sh`：
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec -T mysql mysqldump -u root -p123456 wb > "backup_${DATE}.sql"
find . -name "backup_*.sql" -mtime +7 -delete
```

## 🔐 安全建议

1. **修改默认密码**: 更改数据库和应用的默认密码
2. **使用HTTPS**: 配置SSL证书启用HTTPS
3. **防火墙设置**: 只开放必要的端口
4. **定期更新**: 保持Docker镜像和系统更新
5. **备份策略**: 建立定期备份机制

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件
2. 检查配置文件
3. 参考故障排除章节
4. 提交Issue到项目仓库
