# 🐧 CentOS服务器部署指南

本文档详细介绍如何在CentOS服务器上部署微博设备管理系统。

## 📋 目录

- [系统要求](#系统要求)
- [一键安装](#一键安装)
- [手动安装](#手动安装)
- [配置说明](#配置说明)
- [服务管理](#服务管理)
- [故障排除](#故障排除)
- [安全配置](#安全配置)

## 🔧 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上 (最低2GB)
- **存储**: 20GB以上可用空间
- **网络**: 稳定的网络连接

### 系统要求
- **CentOS 7.x** 或 **CentOS 8.x/Stream**
- **Root权限** 或 **sudo权限**
- **网络连接** 能访问Docker Hub

### 端口要求
- **80**: HTTP服务
- **443**: HTTPS服务 (可选)
- **8000**: 后端API服务
- **3306**: MySQL数据库
- **6379**: Redis缓存

## 🚀 一键安装 (推荐)

### 1. 上传项目文件

将项目文件上传到CentOS服务器，可以使用以下方法之一：

#### 方法1: 使用SCP上传
```bash
# 在本地电脑上执行
scp -r fastApiProject/ root@your-server-ip:/root/
```

#### 方法2: 使用Git克隆
```bash
# 在服务器上执行
git clone <your-repo-url>
cd fastApiProject
```

#### 方法3: 使用FTP/SFTP工具
使用FileZilla、WinSCP等工具上传项目文件夹

### 2. 执行一键安装脚本

```bash
# 进入项目目录
cd fastApiProject

# 给脚本执行权限
chmod +x install_centos.sh

# 执行一键安装
sudo ./install_centos.sh
```

### 3. 等待安装完成

脚本会自动完成以下操作：
- ✅ 检测系统环境
- ✅ 安装Docker和Docker Compose
- ✅ 配置防火墙和SELinux
- ✅ 部署应用服务
- ✅ 配置系统服务
- ✅ 创建管理脚本

## 🛠️ 手动安装

如果一键安装失败，可以按以下步骤手动安装：

### 1. 系统环境检测

```bash
# 检测系统环境
chmod +x check_centos_system.sh
./check_centos_system.sh
```

### 2. 安装Docker (CentOS 7)

```bash
# 更新系统
sudo yum update -y

# 安装必要工具
sudo yum install -y yum-utils device-mapper-persistent-data lvm2

# 添加Docker仓库
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 安装Docker
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker
```

### 3. 安装Docker (CentOS 8/Stream)

```bash
# 更新系统
sudo dnf update -y

# 安装必要工具
sudo dnf install -y dnf-plugins-core

# 添加Docker仓库
sudo dnf config-manager --add-repo=https://download.docker.com/linux/centos/docker-ce.repo

# 安装Docker
sudo dnf install -y docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker
```

### 4. 安装Docker Compose

```bash
# 下载Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 设置执行权限
sudo chmod +x /usr/local/bin/docker-compose

# 创建软链接
sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose

# 验证安装
docker-compose --version
```

### 5. 配置防火墙

```bash
# 开放必要端口
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=8000/tcp

# 配置Docker网络
sudo firewall-cmd --permanent --zone=trusted --add-interface=docker0
sudo firewall-cmd --permanent --zone=trusted --add-masquerade

# 重载防火墙
sudo firewall-cmd --reload
```

### 6. 配置SELinux

```bash
# 检查SELinux状态
getenforce

# 如果是Enforcing，配置相关策略
sudo setsebool -P httpd_can_network_connect 1
sudo setsebool -P container_manage_cgroup 1
```

### 7. 部署应用

```bash
# 执行部署脚本
chmod +x deploy_centos.sh
sudo ./deploy_centos.sh
```

## ⚙️ 配置说明

### 环境变量配置

部署完成后，配置文件位于 `/opt/wb_system/.env`：

```env
# 数据库配置
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=wb_user
MYSQL_PASSWORD=<自动生成的密码>
MYSQL_DATABASE=wb

# 应用配置
SECRET_KEY=<自动生成的密钥>
DEBUG=false
LOG_LEVEL=info
```

### 数据库密码

数据库密码保存在 `/opt/wb_system/passwords.txt` 文件中：
```bash
# 查看数据库密码
sudo cat /opt/wb_system/passwords.txt
```

### 项目目录结构

```
/opt/wb_system/
├── app/                    # 应用代码
├── nginx/                  # Nginx配置
├── logs/                   # 日志文件
├── data/                   # 数据目录
├── docker-compose.yml      # Docker编排文件
├── .env                    # 环境配置
└── passwords.txt           # 数据库密码
```

## 🛠️ 服务管理

### 系统服务管理

```bash
# 启动服务
sudo systemctl start wb-system

# 停止服务
sudo systemctl stop wb-system

# 重启服务
sudo systemctl restart wb-system

# 查看服务状态
sudo systemctl status wb-system

# 开机自启
sudo systemctl enable wb-system
```

### Docker服务管理

```bash
# 进入项目目录
cd /opt/wb_system

# 查看服务状态
sudo docker-compose ps

# 查看日志
sudo docker-compose logs -f

# 重启特定服务
sudo docker-compose restart backend

# 停止所有服务
sudo docker-compose down

# 启动所有服务
sudo docker-compose up -d
```

### 快捷管理命令

安装完成后，可以使用以下快捷命令：

```bash
# 查看系统状态
wb-status

# 查看系统日志
wb-logs

# 查看特定服务日志
wb-logs backend

# 备份数据库
wb-backup
```

## 🔍 故障排除

### 常见问题

#### 1. Docker安装失败
```bash
# 检查系统版本
cat /etc/centos-release

# 清理旧版本Docker
sudo yum remove docker docker-client docker-client-latest docker-common docker-latest docker-latest-logrotate docker-logrotate docker-engine

# 重新安装
sudo yum install -y docker-ce docker-ce-cli containerd.io
```

#### 2. 防火墙问题
```bash
# 检查防火墙状态
sudo firewall-cmd --state

# 查看开放端口
sudo firewall-cmd --list-ports

# 临时关闭防火墙测试
sudo systemctl stop firewalld
```

#### 3. SELinux问题
```bash
# 检查SELinux状态
getenforce

# 临时关闭SELinux测试
sudo setenforce 0

# 永久关闭SELinux (不推荐)
sudo sed -i 's/SELINUX=enforcing/SELINUX=disabled/' /etc/selinux/config
```

#### 4. 端口占用
```bash
# 检查端口占用
sudo netstat -tulnp | grep :80
sudo netstat -tulnp | grep :8000

# 杀死占用进程
sudo kill -9 <PID>
```

#### 5. 内存不足
```bash
# 检查内存使用
free -h

# 检查Docker容器资源使用
sudo docker stats

# 清理Docker缓存
sudo docker system prune -f
```

### 日志查看

```bash
# 查看系统日志
sudo journalctl -u wb-system -f

# 查看Docker日志
cd /opt/wb_system
sudo docker-compose logs -f

# 查看特定服务日志
sudo docker-compose logs backend
sudo docker-compose logs mysql
sudo docker-compose logs nginx
```

### 数据库连接测试

```bash
# 进入MySQL容器
cd /opt/wb_system
sudo docker-compose exec mysql mysql -u root -p

# 测试数据库连接
sudo docker-compose exec mysql mysql -u wb_user -p wb
```

## 🔐 安全配置

### 1. 防火墙配置

```bash
# 只开放必要端口
sudo firewall-cmd --permanent --remove-service=ssh
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

### 2. SSH安全配置

```bash
# 修改SSH配置
sudo vi /etc/ssh/sshd_config

# 建议配置：
# Port 2222                    # 修改SSH端口
# PermitRootLogin no           # 禁止root登录
# PasswordAuthentication no    # 禁用密码登录，使用密钥

# 重启SSH服务
sudo systemctl restart sshd
```

### 3. 定期更新

```bash
# 创建自动更新脚本
sudo crontab -e

# 添加定时任务（每周日凌晨2点更新）
0 2 * * 0 yum update -y && systemctl restart wb-system
```

### 4. 备份策略

```bash
# 创建自动备份任务
sudo crontab -e

# 添加每日备份任务（每天凌晨3点）
0 3 * * * /usr/local/bin/wb-backup
```

## 📊 性能优化

### 1. 系统优化

```bash
# 优化内核参数
echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
echo 'fs.file-max=65536' >> /etc/sysctl.conf
sysctl -p
```

### 2. Docker优化

```bash
# 配置Docker daemon
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json <<EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2"
}
EOF

sudo systemctl restart docker
```

## 📞 技术支持

如果遇到问题：

1. **查看日志**: 使用 `wb-logs` 命令查看详细日志
2. **检查状态**: 使用 `wb-status` 命令检查服务状态
3. **重启服务**: 使用 `systemctl restart wb-system` 重启服务
4. **查看文档**: 参考本文档的故障排除章节

## 🎉 部署完成

部署完成后，您可以：

1. **访问系统**: http://your-server-ip
2. **查看API文档**: http://your-server-ip/docs
3. **配置前端**: 在前端设置中配置服务器地址为 `your-server-ip:8000`

恭喜！您已成功在CentOS服务器上部署了微博设备管理系统！
