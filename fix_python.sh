#!/bin/bash

# 修复Python3软链接问题

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "========================================"
echo "  修复Python3软链接问题"
echo "========================================"
echo ""

# 1. 查找Python3的实际位置
log_info "1. 查找Python3的实际位置..."

echo "查找所有Python可执行文件:"
find /usr -name "python*" -type f 2>/dev/null | grep -E "python[0-9.]*$" | sort

echo ""
echo "查看/usr/bin目录:"
ls -la /usr/bin/*python* 2>/dev/null || echo "没有找到python文件"

echo ""
echo "查看/usr/libexec目录:"
ls -la /usr/libexec/*python* 2>/dev/null || echo "没有找到python文件"

# 2. 尝试不同的Python路径
PYTHON_CANDIDATES=(
    "/usr/bin/python3.9"
    "/usr/libexec/platform-python"
    "/usr/bin/python3.11"
    "/usr/bin/python3.10"
    "/usr/local/bin/python3"
)

WORKING_PYTHON=""

log_info "2. 测试Python候选路径..."
for candidate in "${PYTHON_CANDIDATES[@]}"; do
    if [ -f "$candidate" ] && [ -x "$candidate" ]; then
        echo "测试: $candidate"
        if "$candidate" --version >/dev/null 2>&1; then
            VERSION=$("$candidate" --version 2>&1)
            echo "✅ $candidate 可用: $VERSION"
            WORKING_PYTHON="$candidate"
            break
        else
            echo "❌ $candidate 不可用"
        fi
    else
        echo "⚠️ $candidate 不存在或不可执行"
    fi
done

if [ -z "$WORKING_PYTHON" ]; then
    log_error "没有找到可用的Python3"
    exit 1
fi

# 3. 创建软链接
log_info "3. 创建Python3软链接..."

# 删除现有的软链接
sudo rm -f /usr/bin/python3

# 创建新的软链接
sudo ln -sf "$WORKING_PYTHON" /usr/bin/python3

echo "创建软链接: /usr/bin/python3 -> $WORKING_PYTHON"

# 验证
if python3 --version >/dev/null 2>&1; then
    VERSION=$(python3 --version)
    log_info "✅ Python3软链接创建成功: $VERSION"
else
    log_error "❌ Python3软链接创建失败"
    exit 1
fi

# 4. 处理pip3
log_info "4. 处理pip3..."

# 查找pip3
PIP_CANDIDATES=(
    "/usr/bin/pip3.9"
    "/usr/bin/pip-3.9"
    "/usr/local/bin/pip3"
    "/usr/bin/pip3"
)

WORKING_PIP=""

for candidate in "${PIP_CANDIDATES[@]}"; do
    if [ -f "$candidate" ] && [ -x "$candidate" ]; then
        echo "测试pip: $candidate"
        # 修复pip的shebang
        FIRST_LINE=$(head -1 "$candidate")
        if [[ "$FIRST_LINE" == *"python3"* ]]; then
            if "$candidate" --version >/dev/null 2>&1; then
                echo "✅ $candidate 可用"
                WORKING_PIP="$candidate"
                break
            fi
        fi
    fi
done

if [ -n "$WORKING_PIP" ]; then
    sudo rm -f /usr/bin/pip3
    sudo ln -sf "$WORKING_PIP" /usr/bin/pip3
    log_info "✅ pip3软链接创建成功"
else
    log_warn "⚠️ 没有找到可用的pip3，尝试使用python3 -m pip"
    
    # 测试python3 -m pip
    if python3 -m pip --version >/dev/null 2>&1; then
        # 创建pip3包装脚本
        sudo tee /usr/bin/pip3 << 'EOF'
#!/bin/bash
exec python3 -m pip "$@"
EOF
        sudo chmod +x /usr/bin/pip3
        log_info "✅ 创建pip3包装脚本成功"
    else
        log_error "❌ pip不可用，需要手动安装"
    fi
fi

# 5. 安装venv模块
log_info "5. 安装venv模块..."

if python3 -m venv --help >/dev/null 2>&1; then
    log_info "✅ venv模块可用"
else
    log_warn "⚠️ venv模块不可用，尝试安装..."
    
    # 尝试安装python3-venv包
    if sudo dnf install -y python3*venv* 2>/dev/null; then
        log_info "✅ venv模块安装成功"
    else
        log_warn "⚠️ 无法安装venv模块，将使用virtualenv替代"
        
        # 安装virtualenv
        if python3 -m pip install virtualenv 2>/dev/null; then
            log_info "✅ virtualenv安装成功"
        else
            log_error "❌ 无法安装virtualenv"
        fi
    fi
fi

# 6. 最终验证
log_info "6. 最终验证..."

echo "Python3版本:"
python3 --version

echo "pip3版本:"
pip3 --version 2>/dev/null || echo "pip3不可用，但可以使用 python3 -m pip"

echo "venv测试:"
python3 -m venv --help >/dev/null 2>&1 && echo "✅ venv可用" || echo "❌ venv不可用"

echo ""
echo "========================================"
echo "  Python3修复完成！"
echo "========================================"
echo ""
log_info "现在可以使用:"
echo "  python3 --version"
echo "  pip3 --version"
echo "  python3 -m venv myenv"
echo ""
echo "🎉 Python3环境修复成功！"
