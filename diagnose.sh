#!/bin/bash

# Docker构建问题诊断脚本

echo "=== Docker构建问题诊断 ==="
echo "时间: $(date)"
echo "系统: $(uname -a)"
echo ""

# 1. 检查Docker状态
echo "1. Docker状态检查"
echo "=================="
if docker --version; then
    echo "✅ Docker已安装"
    if docker info >/dev/null 2>&1; then
        echo "✅ Docker服务运行正常"
        docker info | grep -E "(Server Version|Storage Driver|Cgroup Driver)"
    else
        echo "❌ Docker服务未运行"
        echo "请运行: systemctl start docker"
        exit 1
    fi
else
    echo "❌ Docker未安装"
    exit 1
fi
echo ""

# 2. 检查网络连接
echo "2. 网络连接检查"
echo "==============="

# 检查DNS解析
echo "DNS解析测试:"
for host in "pypi.tuna.tsinghua.edu.cn" "mirrors.aliyun.com" "pypi.douban.com"; do
    if nslookup $host >/dev/null 2>&1; then
        echo "✅ $host - DNS解析正常"
    else
        echo "❌ $host - DNS解析失败"
    fi
done

# 检查网络连接
echo ""
echo "网络连接测试:"
for url in "https://pypi.tuna.tsinghua.edu.cn/simple/" "https://mirrors.aliyun.com/pypi/simple/" "https://registry-1.docker.io/v2/"; do
    if curl -s --connect-timeout 10 --max-time 30 "$url" >/dev/null; then
        echo "✅ $url - 连接正常"
    else
        echo "❌ $url - 连接失败"
    fi
done
echo ""

# 3. 检查Docker镜像拉取
echo "3. Docker镜像拉取测试"
echo "===================="
echo "测试拉取Python基础镜像..."
if docker pull python:3.9-slim >/dev/null 2>&1; then
    echo "✅ Python镜像拉取成功"
    docker images python:3.9-slim
else
    echo "❌ Python镜像拉取失败"
    echo "这可能是网络问题或Docker Hub访问受限"
fi
echo ""

# 4. 检查文件结构
echo "4. 项目文件检查"
echo "==============="
required_files=("Dockerfile" "requirements.txt" "docker-compose.yml")
for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 缺失"
    fi
done

if [ -d "app" ]; then
    echo "✅ app/ 目录存在"
else
    echo "❌ app/ 目录缺失"
fi
echo ""

# 5. 检查系统资源
echo "5. 系统资源检查"
echo "==============="
echo "内存使用情况:"
free -h
echo ""
echo "磁盘空间:"
df -h /
echo ""

# 6. 检查防火墙状态
echo "6. 防火墙状态"
echo "============="
if command -v firewall-cmd >/dev/null 2>&1; then
    echo "FirewallD状态:"
    firewall-cmd --state 2>/dev/null || echo "FirewallD未运行"
elif command -v ufw >/dev/null 2>&1; then
    echo "UFW状态:"
    ufw status
elif command -v iptables >/dev/null 2>&1; then
    echo "iptables规则数量:"
    iptables -L | wc -l
else
    echo "未检测到防火墙工具"
fi
echo ""

# 7. 生成建议
echo "7. 建议解决方案"
echo "==============="

# 检查是否有网络问题
network_issues=0
if ! nslookup pypi.tuna.tsinghua.edu.cn >/dev/null 2>&1; then
    ((network_issues++))
fi
if ! curl -s --connect-timeout 5 https://pypi.tuna.tsinghua.edu.cn/simple/ >/dev/null; then
    ((network_issues++))
fi

if [ $network_issues -gt 0 ]; then
    echo "🔧 网络连接有问题，建议："
    echo "   1. 检查DNS设置: cat /etc/resolv.conf"
    echo "   2. 尝试更换DNS: echo 'nameserver *******' > /etc/resolv.conf"
    echo "   3. 检查代理设置"
    echo "   4. 使用简化版Dockerfile: docker build -f Dockerfile.simple -t wb_backend:latest ."
    echo ""
fi

# 检查Docker镜像拉取
if ! docker pull python:3.9-slim >/dev/null 2>&1; then
    echo "🔧 Docker镜像拉取失败，建议："
    echo "   1. 配置Docker镜像加速器"
    echo "   2. 检查Docker Hub访问"
    echo "   3. 使用国内Docker镜像源"
    echo ""
fi

echo "🚀 推荐的构建命令："
echo "   # 方法1: 使用简化版Dockerfile"
echo "   docker build -f Dockerfile.simple -t wb_backend:latest ."
echo ""
echo "   # 方法2: 禁用BuildKit"
echo "   DOCKER_BUILDKIT=0 docker build -t wb_backend:latest ."
echo ""
echo "   # 方法3: 使用离线构建"
echo "   python3 download_wheels.py"
echo "   docker build -f Dockerfile.offline -t wb_backend:latest ."

echo ""
echo "=== 诊断完成 ==="
