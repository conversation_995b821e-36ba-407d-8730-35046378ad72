#!/bin/bash

# DNS修复构建脚本

echo "=== DNS修复Docker构建 ==="

# 1. 停止所有容器
echo "停止运行中的容器..."
docker stop $(docker ps -q) 2>/dev/null || true

# 2. 检查宿主机DNS
echo "检查宿主机DNS配置..."
echo "当前DNS配置:"
cat /etc/resolv.conf

# 3. 测试宿主机网络
echo ""
echo "测试宿主机网络连接..."
if ping -c 1 ******* >/dev/null 2>&1; then
    echo "✅ 宿主机网络正常"
else
    echo "❌ 宿主机网络异常"
    exit 1
fi

# 4. 测试域名解析
echo "测试域名解析..."
if nslookup pypi.org >/dev/null 2>&1; then
    echo "✅ 域名解析正常"
else
    echo "❌ 域名解析失败，尝试修复..."
    
    # 备份原DNS配置
    cp /etc/resolv.conf /etc/resolv.conf.backup
    
    # 设置新的DNS
    cat > /etc/resolv.conf << EOF
nameserver *******
nameserver ***************
nameserver *********
EOF
    
    echo "已更新DNS配置"
fi

# 5. 重启Docker服务
echo "重启Docker服务..."
systemctl restart docker
sleep 5

# 6. 创建带DNS配置的Dockerfile
echo "创建带DNS配置的Dockerfile..."
cat > Dockerfile.dns << 'EOF'
FROM python:3.9-slim

# 设置DNS
RUN echo "nameserver *******" > /etc/resolv.conf && \
    echo "nameserver ***************" >> /etc/resolv.conf && \
    echo "nameserver *********" >> /etc/resolv.conf

WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_DEFAULT_TIMEOUT=600

# 复制文件
COPY requirements.txt .

# 安装依赖，使用多个镜像源
RUN pip install --no-cache-dir -r requirements.txt \
    -i https://pypi.tuna.tsinghua.edu.cn/simple \
    --trusted-host pypi.tuna.tsinghua.edu.cn || \
    pip install --no-cache-dir -r requirements.txt \
    -i https://mirrors.aliyun.com/pypi/simple/ \
    --trusted-host mirrors.aliyun.com || \
    pip install --no-cache-dir -r requirements.txt

COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF

# 7. 使用主机网络构建
echo "使用主机网络模式构建..."
export DOCKER_BUILDKIT=0

if docker build -f Dockerfile.dns --network=host --dns=******* --dns=*************** -t wb_backend:latest .; then
    echo "✅ DNS修复构建成功！"
    
    # 测试镜像
    echo "测试构建的镜像..."
    docker images wb_backend:latest
    
    echo ""
    echo "构建完成！现在可以启动服务："
    echo "docker run --rm -p 8000:8000 wb_backend:latest"
    
else
    echo "❌ DNS修复构建失败"
    echo ""
    echo "尝试完全离线构建..."
    
    # 下载依赖到本地
    echo "下载Python依赖包..."
    mkdir -p wheels
    
    # 在宿主机上下载wheel文件
    pip3 download -d wheels -r requirements.txt || \
    pip download -d wheels -r requirements.txt
    
    if [ -d "wheels" ] && [ "$(ls -A wheels)" ]; then
        echo "✅ 依赖包下载完成"
        
        # 创建离线Dockerfile
        cat > Dockerfile.offline_fixed << 'EOF'
FROM python:3.9-slim
WORKDIR /app
COPY wheels/ ./wheels/
COPY requirements.txt .
RUN pip install --no-index --find-links wheels -r requirements.txt
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF
        
        echo "使用离线方式构建..."
        if docker build -f Dockerfile.offline_fixed -t wb_backend:latest .; then
            echo "✅ 离线构建成功！"
        else
            echo "❌ 离线构建也失败了"
        fi
    else
        echo "❌ 无法下载依赖包"
    fi
fi

# 清理临时文件
rm -f Dockerfile.dns Dockerfile.offline_fixed

echo ""
echo "=== 构建完成 ==="
