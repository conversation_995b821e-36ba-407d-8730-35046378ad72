#!/usr/bin/env python3

"""
简单的HTTP反向代理服务器
将80端口的请求转发到8000端口
"""

import http.server
import socketserver
import urllib.request
import urllib.parse
import urllib.error
import json
from datetime import datetime

class ProxyHandler(http.server.BaseHTTPRequestHandler):
    
    def do_GET(self):
        self.proxy_request()
    
    def do_POST(self):
        self.proxy_request()
    
    def do_PUT(self):
        self.proxy_request()
    
    def do_DELETE(self):
        self.proxy_request()
    
    def proxy_request(self):
        try:
            # 构建目标URL
            target_url = f"http://127.0.0.1:8000{self.path}"
            
            # 准备请求数据
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length) if content_length > 0 else None
            
            # 创建请求
            req = urllib.request.Request(target_url, data=post_data, method=self.command)
            
            # 复制请求头（排除一些不需要的）
            skip_headers = ['host', 'content-length']
            for header, value in self.headers.items():
                if header.lower() not in skip_headers:
                    req.add_header(header, value)
            
            # 发送请求
            with urllib.request.urlopen(req, timeout=30) as response:
                # 设置响应状态码
                self.send_response(response.getcode())
                
                # 复制响应头
                for header, value in response.headers.items():
                    if header.lower() not in ['server', 'date']:
                        self.send_header(header, value)
                
                self.send_header('Server', 'WB-Proxy/1.0')
                self.end_headers()
                
                # 复制响应内容
                self.wfile.write(response.read())
                
        except urllib.error.URLError as e:
            # 后端服务不可用
            self.send_error(502, f"Bad Gateway: {str(e)}")
        except Exception as e:
            # 其他错误
            self.send_error(500, f"Internal Server Error: {str(e)}")
    
    def log_message(self, format, *args):
        # 自定义日志格式
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {format % args}")

def run_proxy_server(port=80):
    """运行代理服务器"""
    try:
        with socketserver.TCPServer(("", port), ProxyHandler) as httpd:
            print(f"🚀 代理服务器启动成功")
            print(f"📍 监听端口: {port}")
            print(f"🔄 转发到: http://127.0.0.1:8000")
            print(f"🌐 访问地址: http://***********/")
            print(f"⏰ 启动时间: {datetime.now()}")
            print("按 Ctrl+C 停止服务")
            print("-" * 50)
            
            httpd.serve_forever()
            
    except PermissionError:
        print("❌ 权限错误：需要root权限运行80端口")
        print("请使用: sudo python3 proxy_server.py")
    except OSError as e:
        if "Address already in use" in str(e):
            print("❌ 端口80已被占用")
            print("请先停止占用80端口的服务")
        else:
            print(f"❌ 网络错误: {e}")
    except KeyboardInterrupt:
        print("\n🛑 代理服务器已停止")

if __name__ == "__main__":
    run_proxy_server()
