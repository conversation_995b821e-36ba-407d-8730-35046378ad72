#!/bin/bash

# 微博任务管理系统 - 仅部署数据库Docker + 原生后端

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

echo "========================================"
echo "  Docker数据库 + 原生后端部署"
echo "========================================"
echo ""

# 1. 清理之前的部署
log_step "1. 清理之前的部署"

# 停止所有服务
docker-compose down 2>/dev/null || true
sudo systemctl stop wb-* 2>/dev/null || true
sudo pkill -f "uvicorn.*8000" 2>/dev/null || true

# 2. 创建仅数据库的Docker配置
log_step "2. 创建数据库Docker配置"

cat > docker-compose-db.yml << 'EOF'
version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: wb_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: "123456"
      MYSQL_DATABASE: "wb"
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO
      --innodb-use-native-aio=0
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123456"]
      timeout: 20s
      retries: 10
      interval: 10s

volumes:
  mysql_data:
    driver: local
EOF

# 3. 创建MySQL初始化脚本
log_step "3. 创建MySQL初始化脚本"

mkdir -p mysql/init

cat > mysql/init/01-init.sql << 'EOF'
-- 创建数据库
CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE wb;

-- 设置时区
SET time_zone = '+08:00';

-- 表结构将由SQLAlchemy自动创建
EOF

# 4. 启动数据库容器
log_step "4. 启动数据库容器"

log_info "启动MySQL容器..."
docker-compose -f docker-compose-db.yml up -d

log_info "等待MySQL启动..."
sleep 20

# 检查MySQL状态
if docker-compose -f docker-compose-db.yml ps | grep -q "Up"; then
    log_info "✅ MySQL容器启动成功"
else
    log_error "❌ MySQL容器启动失败"
    docker-compose -f docker-compose-db.yml logs
    exit 1
fi

# 5. 测试数据库连接
log_step "5. 测试数据库连接"

for i in {1..30}; do
    if docker exec wb_mysql mysql -u root -p123456 -e "SELECT 1;" >/dev/null 2>&1; then
        log_info "✅ 数据库连接成功"
        break
    fi
    echo "等待数据库就绪... ($i/30)"
    sleep 2
done

# 6. 创建Python虚拟环境
log_step "6. 创建Python环境"

if [ ! -d "venv" ]; then
    python3 -m venv venv
    log_info "虚拟环境创建成功"
fi

source venv/bin/activate

# 安装依赖
log_info "安装Python依赖..."
pip install --upgrade pip
pip install -r requirements.txt

# 7. 确保config.py正确配置
log_step "7. 配置数据库连接"

# 备份原配置
cp app/config.py app/config.py.backup 2>/dev/null || true

# 创建适配Docker数据库的配置
cat > app/config.py << 'EOF'
import os

# MySQL数据库配置 - 连接Docker容器
MYSQL_USER = os.getenv("MYSQL_USER", "root")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD", "123456")
MYSQL_HOST = os.getenv("MYSQL_HOST", "127.0.0.1")  # Docker映射到本地3306
MYSQL_PORT = os.getenv("MYSQL_PORT", "3306")
MYSQL_DB = os.getenv("MYSQL_DB", "wb")

DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}?charset=utf8mb4"

print(f"[CONFIG] Database: {MYSQL_USER}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}")
EOF

# 8. 测试应用数据库连接
log_step "8. 测试应用数据库连接"

python3 -c "
import sys
sys.path.append('.')
try:
    from app.config import DATABASE_URL
    from sqlalchemy import create_engine, text
    engine = create_engine(DATABASE_URL)
    with engine.connect() as conn:
        result = conn.execute(text('SELECT 1'))
        print('✅ 应用数据库连接成功')
except Exception as e:
    print(f'❌ 应用数据库连接失败: {e}')
    sys.exit(1)
"

# 9. 测试表自动创建
log_step "9. 测试表自动创建"

python3 -c "
import sys
sys.path.append('.')
try:
    from app.db import Base, engine
    from app.models import device, task, group  # 导入所有模型
    
    print('正在创建数据库表...')
    Base.metadata.create_all(bind=engine)
    print('✅ 数据库表创建成功')
    
    # 检查创建的表
    from sqlalchemy import text
    with engine.connect() as conn:
        result = conn.execute(text('SHOW TABLES'))
        tables = [row[0] for row in result]
        print(f'创建的表: {tables}')
        
except Exception as e:
    print(f'❌ 表创建失败: {e}')
    import traceback
    traceback.print_exc()
"

# 10. 创建启动脚本
log_step "10. 创建启动脚本"

cat > start_backend.sh << 'EOF'
#!/bin/bash

cd "$(dirname "$0")"

# 检查数据库容器状态
if ! docker ps | grep -q wb_mysql; then
    echo "启动数据库容器..."
    docker-compose -f docker-compose-db.yml up -d
    echo "等待数据库启动..."
    sleep 10
fi

# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export MYSQL_USER=root
export MYSQL_PASSWORD=123456
export MYSQL_HOST=127.0.0.1
export MYSQL_PORT=3306
export MYSQL_DB=wb

echo "========================================"
echo "  微博任务管理系统启动"
echo "========================================"
echo "数据库: Docker MySQL容器"
echo "后端: 原生Python应用"
echo "访问地址: http://***********:8000"
echo "API文档: http://***********:8000/docs"
echo "========================================"
echo ""

# 启动应用
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
EOF

chmod +x start_backend.sh

# 11. 创建管理脚本
cat > manage_db.sh << 'EOF'
#!/bin/bash

case "$1" in
    start)
        echo "启动数据库容器..."
        docker-compose -f docker-compose-db.yml up -d
        ;;
    stop)
        echo "停止数据库容器..."
        docker-compose -f docker-compose-db.yml down
        ;;
    restart)
        echo "重启数据库容器..."
        docker-compose -f docker-compose-db.yml restart
        ;;
    status)
        echo "数据库容器状态:"
        docker-compose -f docker-compose-db.yml ps
        ;;
    logs)
        echo "数据库日志:"
        docker-compose -f docker-compose-db.yml logs -f
        ;;
    backup)
        echo "备份数据库..."
        docker exec wb_mysql mysqldump -u root -p123456 wb > backup_$(date +%Y%m%d_%H%M%S).sql
        echo "备份完成: backup_$(date +%Y%m%d_%H%M%S).sql"
        ;;
    connect)
        echo "连接数据库..."
        docker exec -it wb_mysql mysql -u root -p123456 wb
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs|backup|connect}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动数据库容器"
        echo "  stop    - 停止数据库容器"
        echo "  restart - 重启数据库容器"
        echo "  status  - 查看容器状态"
        echo "  logs    - 查看数据库日志"
        echo "  backup  - 备份数据库"
        echo "  connect - 连接数据库"
        exit 1
        ;;
esac
EOF

chmod +x manage_db.sh

echo ""
echo "========================================"
echo "  部署完成！"
echo "========================================"
echo ""
log_info "部署方案: Docker数据库 + 原生后端"
log_info "数据库: MySQL 8.0 容器 (端口3306)"
log_info "后端: 原生Python应用 (端口8000)"
echo ""
log_info "启动命令:"
echo "  ./start_backend.sh"
echo ""
log_info "数据库管理:"
echo "  ./manage_db.sh start    - 启动数据库"
echo "  ./manage_db.sh stop     - 停止数据库"
echo "  ./manage_db.sh status   - 查看状态"
echo "  ./manage_db.sh connect  - 连接数据库"
echo "  ./manage_db.sh backup   - 备份数据库"
echo ""
log_info "访问地址:"
echo "  - 后端API: http://***********:8000"
echo "  - API文档: http://***********:8000/docs"
echo "  - 健康检查: http://***********:8000/health"
echo ""
log_info "数据库信息:"
echo "  - 主机: localhost:3306"
echo "  - 用户: root"
echo "  - 密码: 123456"
echo "  - 数据库: wb"
echo ""
echo "🎉 部署完成！您的表结构会自动创建"
