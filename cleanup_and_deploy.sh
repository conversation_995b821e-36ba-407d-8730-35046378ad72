#!/bin/bash

# 微博任务管理系统 - 清理并重新部署
# 适用于OpenCloudOS 9 + 腾讯云

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

echo "========================================"
echo "  微博任务管理系统 - 清理重新部署"
echo "========================================"
echo "适用于: OpenCloudOS 9 + 腾讯云"
echo "后端端口: 8000"
echo ""

# 1. 清理测试文件
log_step "1. 清理测试文件和脚本"

# 停止所有相关服务
docker-compose down 2>/dev/null || true
sudo systemctl stop wb-* 2>/dev/null || true
sudo pkill -f "uvicorn.*8000" 2>/dev/null || true
sudo pkill -f "proxy_server" 2>/dev/null || true

# 删除测试脚本和临时文件
log_info "删除测试脚本..."
rm -f deploy_*.sh fix_*.sh test_*.py debug_*.py check_*.py
rm -f simple_*.py simulate_*.py validate_*.py
rm -f *.bat *.spec build.sh install_*.sh
rm -f quick_*.sh start_*.sh emergency_*.sh
rm -f clear_database*.py migrate_*.py update_*.py restore_*.py
rm -f *.md API_DOCUMENTATION.md BUILD_GUIDE.md
rm -f web_frontend.html frontend_demo.py api_client.py
rm -f proxy_server.py simple_backend.py

# 删除临时目录
rm -rf __pycache__ build dist
rm -rf nginx/conf.d 2>/dev/null || true

log_info "✅ 测试文件清理完成"

# 2. 基于现有代码创建Docker配置
log_step "2. 基于现有代码创建Docker配置"

# 备份原配置文件
cp app/config.py app/config.py.backup

# 修改config.py支持环境变量
cat > app/config.py << 'EOF'
import os

# MySQL数据库配置 - 支持环境变量和默认值
MYSQL_USER = os.getenv("MYSQL_USER", "root")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD", "123456")
MYSQL_HOST = os.getenv("MYSQL_HOST", "127.0.0.1")
MYSQL_PORT = os.getenv("MYSQL_PORT", "3306")
MYSQL_DB = os.getenv("MYSQL_DB", "wb")

DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}?charset=utf8mb4"

# 打印配置信息（用于调试）
print(f"[CONFIG] Database URL: mysql+pymysql://{MYSQL_USER}:***@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}")
EOF

# 创建适配现有代码的Dockerfile
cat > Dockerfile << 'EOF'
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    TZ=Asia/Shanghai

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    tzdata \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制requirements文件并安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 复制应用代码（保持原有结构）
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

# 创建必要目录
RUN mkdir -p /app/logs

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF

# 创建适配现有代码的docker-compose.yml
cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  # MySQL数据库 - 使用与您代码兼容的配置
  mysql:
    image: mysql:8.0
    container_name: wb_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: "123456"
      MYSQL_DATABASE: "wb"
      MYSQL_USER: "root"
      MYSQL_PASSWORD: "123456"
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - wb_network
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO
      --innodb-use-native-aio=0
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123456"]
      timeout: 20s
      retries: 10
      interval: 10s

  # 后端应用 - 使用您现有的代码结构
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wb_backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      # 数据库配置 - 匹配您的config.py
      MYSQL_USER: "root"
      MYSQL_PASSWORD: "123456"
      MYSQL_HOST: "mysql"
      MYSQL_PORT: "3306"
      MYSQL_DB: "wb"

      # 时区配置
      TZ: "Asia/Shanghai"

    volumes:
      - ./logs:/app/logs
    networks:
      - wb_network
    depends_on:
      mysql:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      timeout: 30s
      retries: 5
      interval: 30s
      start_period: 90s

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: wb_nginx
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - wb_network
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      timeout: 10s
      retries: 3
      interval: 30s

volumes:
  mysql_data:
    driver: local

networks:
  wb_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
EOF

# 3. 创建环境配置文件（匹配您的代码）
log_step "3. 创建环境配置文件"

cat > .env << 'EOF'
# MySQL配置 - 匹配您现有的config.py
MYSQL_USER=root
MYSQL_PASSWORD=123456
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_DB=wb

# 应用配置
TZ=Asia/Shanghai
DEBUG=false
LOG_LEVEL=info
EOF

# 4. 创建Nginx配置
log_step "4. 创建Nginx配置"

mkdir -p nginx/conf.d logs/nginx

cat > nginx/nginx.conf << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    # 基本配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
}
EOF

cat > nginx/conf.d/wb_system.conf << 'EOF'
upstream backend {
    server backend:8000;
    keepalive 32;
}

server {
    listen 80;
    server_name _;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # API代理
    location / {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # WebSocket代理
    location /ws {
        proxy_pass http://backend/ws;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket特殊设置
        proxy_buffering off;
        proxy_read_timeout 86400;
    }
    
    # 健康检查
    location /nginx-health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

# 5. 创建MySQL初始化脚本（适配您的数据库结构）
mkdir -p mysql/init

cat > mysql/init/01-init.sql << 'EOF'
-- 创建数据库
CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE wb;

-- 设置root用户权限（匹配您的配置）
GRANT ALL PRIVILEGES ON wb.* TO 'root'@'%';
FLUSH PRIVILEGES;

-- 表结构由您的SQLAlchemy模型自动创建
-- 支持的枚举类型会自动处理
EOF

# 6. 创建管理脚本
log_step "6. 创建管理脚本"

cat > manage.sh << 'EOF'
#!/bin/bash

# 微博任务管理系统管理脚本

set -e

case "$1" in
    start)
        echo "启动微博任务管理系统..."
        docker-compose up -d
        echo "等待服务启动..."
        sleep 30
        docker-compose ps
        ;;
    stop)
        echo "停止微博任务管理系统..."
        docker-compose down
        ;;
    restart)
        echo "重启微博任务管理系统..."
        docker-compose down
        docker-compose up -d
        ;;
    status)
        echo "服务状态:"
        docker-compose ps
        ;;
    logs)
        if [ -n "$2" ]; then
            docker-compose logs -f "$2"
        else
            docker-compose logs -f
        fi
        ;;
    health)
        echo "健康检查:"
        curl -s http://localhost/health | python3 -m json.tool 2>/dev/null || curl -s http://localhost/health
        ;;
    build)
        echo "重新构建镜像..."
        docker-compose build --no-cache
        ;;
    clean)
        echo "清理Docker资源..."
        docker-compose down -v
        docker system prune -f
        ;;
    backup)
        echo "备份数据库..."
        docker-compose exec mysql mysqldump -u root -p wb > backup_$(date +%Y%m%d_%H%M%S).sql
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs|health|build|clean|backup}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动所有服务"
        echo "  stop    - 停止所有服务"
        echo "  restart - 重启所有服务"
        echo "  status  - 查看服务状态"
        echo "  logs    - 查看日志 (可指定服务名)"
        echo "  health  - 健康检查"
        echo "  build   - 重新构建镜像"
        echo "  clean   - 清理Docker资源"
        echo "  backup  - 备份数据库"
        exit 1
        ;;
esac
EOF

chmod +x manage.sh

echo ""
echo "========================================"
echo "  清理和重新部署完成！"
echo "========================================"
echo ""
log_info "项目已优化，删除了所有测试文件"
log_info "Docker配置已针对OpenCloudOS 9和腾讯云优化"
echo ""
log_info "启动命令:"
echo "  ./manage.sh start"
echo ""
log_info "管理命令:"
echo "  ./manage.sh status   - 查看状态"
echo "  ./manage.sh logs     - 查看日志"
echo "  ./manage.sh health   - 健康检查"
echo "  ./manage.sh restart  - 重启服务"
echo ""
log_info "访问地址:"
echo "  - 主页: http://43.138.50.5/"
echo "  - API文档: http://43.138.50.5/docs"
echo "  - 健康检查: http://43.138.50.5/health"
echo ""
echo "🎉 优化部署完成！请运行 ./manage.sh start 启动服务"
