#!/bin/bash

# 微博任务管理系统 - 终极部署解决方案
# 三种方案：Docker完整版、原生MySQL版、SQLite简化版

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

echo "========================================"
echo "  微博任务管理系统 - 终极部署方案"
echo "========================================"
echo "选择部署方案："
echo "1. Docker完整版（推荐生产环境）"
echo "2. 原生MySQL版（推荐开发环境）"
echo "3. SQLite简化版（推荐快速测试）"
echo ""

read -p "请选择部署方案 (1/2/3): " DEPLOY_CHOICE

case $DEPLOY_CHOICE in
    1)
        echo "选择：Docker完整版"
        DEPLOY_TYPE="docker"
        ;;
    2)
        echo "选择：原生MySQL版"
        DEPLOY_TYPE="mysql"
        ;;
    3)
        echo "选择：SQLite简化版"
        DEPLOY_TYPE="sqlite"
        ;;
    *)
        echo "无效选择，默认使用SQLite简化版"
        DEPLOY_TYPE="sqlite"
        ;;
esac

PROJECT_DIR="/www/wwwroot/fastApiProject"
cd "$PROJECT_DIR"

# ==================== Docker完整版 ====================
if [ "$DEPLOY_TYPE" = "docker" ]; then
    log_step "Docker完整版部署"
    
    # 1. 修复Docker网络问题
    log_info "修复Docker网络配置..."
    sudo firewall-cmd --permanent --zone=trusted --add-interface=docker0 2>/dev/null || true
    sudo firewall-cmd --permanent --zone=trusted --add-masquerade 2>/dev/null || true
    sudo firewall-cmd --reload 2>/dev/null || true
    sudo systemctl restart docker
    
    # 2. 创建优化的docker-compose.yml
    cat > docker-compose-ultimate.yml << 'EOF'
services:
  mysql:
    image: mysql:8.0
    container_name: wb_mysql_ultimate
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: "WbRoot123456!"
      MYSQL_DATABASE: "wb"
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-use-native-aio=0
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pWbRoot123456!"]
      timeout: 20s
      retries: 10
      interval: 10s

  backend:
    build:
      context: .
      dockerfile: Dockerfile-ultimate
    container_name: wb_backend_ultimate
    restart: unless-stopped
    ports:
      - "11234:11234"
    environment:
      MYSQL_USER: "root"
      MYSQL_PASSWORD: "WbRoot123456!"
      MYSQL_HOST: "mysql"
      MYSQL_PORT: "3306"
      MYSQL_DB: "wb"
    depends_on:
      mysql:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:11234/health || exit 1"]
      timeout: 30s
      retries: 5
      interval: 30s

volumes:
  mysql_data:
    driver: local
EOF

    # 3. 创建优化的Dockerfile
    cat > Dockerfile-ultimate << 'EOF'
FROM python:3.9-slim

WORKDIR /app

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    curl \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

EXPOSE 11234

HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:11234/health || exit 1

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "11234"]
EOF

    # 4. 启动Docker服务
    log_info "启动Docker服务..."
    docker-compose -f docker-compose-ultimate.yml down 2>/dev/null || true
    docker-compose -f docker-compose-ultimate.yml build --no-cache
    docker-compose -f docker-compose-ultimate.yml up -d
    
    STARTUP_SCRIPT="start_docker.sh"
    cat > $STARTUP_SCRIPT << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
docker-compose -f docker-compose-ultimate.yml up -d
echo "Docker服务已启动"
echo "访问地址: http://***********:11234"
EOF

# ==================== 原生MySQL版 ====================
elif [ "$DEPLOY_TYPE" = "mysql" ]; then
    log_step "原生MySQL版部署"
    
    # 1. 安装MySQL
    log_info "安装MySQL..."
    if ! systemctl is-active --quiet mysqld; then
        dnf install -y mysql-server
        systemctl start mysqld
        systemctl enable mysqld
        
        # 配置MySQL
        TEMP_PASSWORD=$(grep 'temporary password' /var/log/mysqld.log 2>/dev/null | tail -1 | awk '{print $NF}' || echo "")
        if [ -n "$TEMP_PASSWORD" ]; then
            mysql -u root -p"$TEMP_PASSWORD" --connect-expired-password << 'EOF'
ALTER USER 'root'@'localhost' IDENTIFIED BY 'WbRoot123456!';
CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
FLUSH PRIVILEGES;
EOF
        else
            mysql -u root << 'EOF'
ALTER USER 'root'@'localhost' IDENTIFIED BY 'WbRoot123456!';
CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
FLUSH PRIVILEGES;
EOF
        fi
    fi
    
    # 2. 创建Python环境
    log_info "创建Python环境..."
    /usr/bin/python3 -m venv venv_ultimate
    source venv_ultimate/bin/activate
    python -m pip install --upgrade pip
    python -m pip install -r requirements.txt
    
    # 3. 配置数据库连接
    cp app/config.py app/config.py.backup 2>/dev/null || true
    cat > app/config.py << 'EOF'
MYSQL_USER = "root"
MYSQL_PASSWORD = "WbRoot123456!"
MYSQL_HOST = "127.0.0.1"
MYSQL_PORT = "3306"
MYSQL_DB = "wb"

DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}?charset=utf8mb4"
print(f"[CONFIG] Database: {MYSQL_USER}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}")
EOF
    
    STARTUP_SCRIPT="start_mysql.sh"
    cat > $STARTUP_SCRIPT << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"

if ! systemctl is-active --quiet mysqld; then
    sudo systemctl start mysqld
fi

source venv_ultimate/bin/activate
echo "启动微博任务管理系统 (MySQL版)"
echo "访问地址: http://***********:11234"
uvicorn app.main:app --host 0.0.0.0 --port 11234 --reload
EOF

# ==================== SQLite简化版 ====================
else
    log_step "SQLite简化版部署"
    
    # 1. 创建Python环境
    log_info "创建Python环境..."
    /usr/bin/python3 -m venv venv_ultimate
    source venv_ultimate/bin/activate
    python -m pip install --upgrade pip
    python -m pip install fastapi uvicorn sqlalchemy python-dotenv pydantic websockets requests
    
    # 2. 配置SQLite数据库
    cp app/config.py app/config.py.backup 2>/dev/null || true
    cat > app/config.py << 'EOF'
DATABASE_URL = "sqlite:///./wb.db"
print(f"[CONFIG] Database: SQLite - {DATABASE_URL}")
EOF
    
    STARTUP_SCRIPT="start_sqlite.sh"
    cat > $STARTUP_SCRIPT << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"

source venv_ultimate/bin/activate
echo "启动微博任务管理系统 (SQLite版)"
echo "访问地址: http://***********:11234"
uvicorn app.main:app --host 0.0.0.0 --port 11234 --reload
EOF
fi

# ==================== 通用配置 ====================

# 配置防火墙
log_info "配置防火墙..."
if systemctl is-active --quiet firewalld; then
    firewall-cmd --permanent --add-port=11234/tcp 2>/dev/null || true
    firewall-cmd --reload 2>/dev/null || true
fi

# 创建启动脚本
chmod +x $STARTUP_SCRIPT

# 创建管理脚本
cat > manage_ultimate.sh << 'EOF'
#!/bin/bash

case "$1" in
    start)
        echo "启动系统..."
        if [ -f "start_docker.sh" ]; then
            ./start_docker.sh
        elif [ -f "start_mysql.sh" ]; then
            ./start_mysql.sh
        else
            ./start_sqlite.sh
        fi
        ;;
    stop)
        echo "停止系统..."
        if [ -f "docker-compose-ultimate.yml" ]; then
            docker-compose -f docker-compose-ultimate.yml down
        else
            pkill -f "uvicorn.*11234"
        fi
        ;;
    status)
        echo "系统状态:"
        if [ -f "docker-compose-ultimate.yml" ]; then
            docker-compose -f docker-compose-ultimate.yml ps
        else
            ps aux | grep "uvicorn.*11234" | grep -v grep || echo "服务未运行"
        fi
        ;;
    test)
        echo "测试访问:"
        curl -s http://localhost:11234/health | python3 -m json.tool 2>/dev/null || curl -s http://localhost:11234/health
        ;;
    logs)
        echo "查看日志:"
        if [ -f "docker-compose-ultimate.yml" ]; then
            docker-compose -f docker-compose-ultimate.yml logs -f backend
        else
            echo "原生部署请查看终端输出"
        fi
        ;;
    *)
        echo "用法: $0 {start|stop|status|test|logs}"
        exit 1
        ;;
esac
EOF

chmod +x manage_ultimate.sh

# 测试数据库连接和表创建
if [ "$DEPLOY_TYPE" != "docker" ]; then
    log_info "测试数据库连接和表创建..."
    source venv_ultimate/bin/activate
    python3 << 'EOF'
import sys
sys.path.append('.')

try:
    from app.db import engine, Base
    from app.models import device, task, group, device_status, group_task_status
    
    print("测试数据库连接...")
    with engine.connect() as conn:
        conn.execute("SELECT 1")
    print("✅ 数据库连接成功")
    
    print("创建数据库表...")
    Base.metadata.create_all(bind=engine)
    print("✅ 数据库表创建成功")
    
except Exception as e:
    print(f"❌ 数据库操作失败: {e}")
EOF
fi

echo ""
echo "========================================"
echo "  🎉 部署完成！"
echo "========================================"
echo ""
log_info "部署类型: $DEPLOY_TYPE"
log_info "启动脚本: ./$STARTUP_SCRIPT"
log_info "管理脚本: ./manage_ultimate.sh"
echo ""
log_info "访问地址:"
echo "  - 主页: http://***********:11234"
echo "  - API文档: http://***********:11234/docs"
echo "  - 健康检查: http://***********:11234/health"
echo "  - WebSocket测试: http://***********:11234/ws/test"
echo ""
log_info "前端配置:"
echo "  SERVER_URL = \"http://***********:11234\""
echo "  API_BASE_URL = \"http://***********:11234/api\""
echo "  WS_URL = \"ws://***********:11234/ws\""
echo ""
log_info "管理命令:"
echo "  ./manage_ultimate.sh start   # 启动系统"
echo "  ./manage_ultimate.sh stop    # 停止系统"
echo "  ./manage_ultimate.sh status  # 查看状态"
echo "  ./manage_ultimate.sh test    # 测试访问"
echo "  ./manage_ultimate.sh logs    # 查看日志"
echo ""
echo "🚀 现在可以启动系统: ./$STARTUP_SCRIPT"
