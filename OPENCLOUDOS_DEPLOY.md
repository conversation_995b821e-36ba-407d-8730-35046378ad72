# 🐧 OpenCloudOS 9服务器部署指南

本文档详细介绍如何在OpenCloudOS 9服务器上部署微博设备管理系统。

## 📋 目录

- [系统介绍](#系统介绍)
- [一键安装](#一键安装)
- [手动安装](#手动安装)
- [配置说明](#配置说明)
- [服务管理](#服务管理)
- [故障排除](#故障排除)
- [性能优化](#性能优化)

## 🖥️ 系统介绍

### OpenCloudOS 9特性
- **基于**: CentOS 8/RHEL 8
- **包管理器**: DNF (YUM的升级版)
- **容器支持**: 原生Docker和Podman支持
- **云优化**: 腾讯云深度优化
- **企业级**: 稳定性和安全性保障
- **兼容性**: 完全兼容CentOS 8生态

### 系统要求
- **CPU**: 2核心以上
- **内存**: 4GB以上 (最低2GB)
- **存储**: 20GB以上可用空间
- **架构**: x86_64 或 aarch64
- **网络**: 稳定的网络连接

## 🚀 一键安装 (推荐)

### 1. 上传项目文件

**方法1: 使用SCP上传**
```bash
# 在本地电脑上执行
scp -r fastApiProject/ root@your-server-ip:/root/
```

**方法2: 使用Git克隆**
```bash
# 在OpenCloudOS 9服务器上执行
dnf install -y git
git clone <your-repo-url>
cd fastApiProject
```

**方法3: 使用FTP/SFTP工具**
- 使用FileZilla、WinSCP等工具上传项目文件夹

### 2. 执行一键安装脚本

```bash
# 进入项目目录
cd fastApiProject

# 给脚本执行权限
chmod +x install_opencloudos.sh

# 执行一键安装
sudo ./install_opencloudos.sh
```

### 3. 安装过程

脚本会自动完成以下操作：
- ✅ 检测OpenCloudOS 9系统环境
- ✅ 配置腾讯云镜像源和软件仓库
- ✅ 安装Docker CE和Docker Compose
- ✅ 配置防火墙、SELinux和Docker镜像加速
- ✅ 部署微博设备管理系统
- ✅ 配置系统服务和管理脚本
- ✅ 设置定时备份和维护任务

## 🛠️ 手动安装

如果一键安装失败，可以按以下步骤手动安装：

### 1. 系统环境检测

```bash
# 检测系统环境
chmod +x check_opencloudos_system.sh
./check_opencloudos_system.sh
```

### 2. 更新系统和安装基础工具

```bash
# 更新系统
sudo dnf update -y

# 安装基础工具
sudo dnf install -y dnf-plugins-core curl wget git vim net-tools
```

### 3. 配置软件源 (可选，提升速度)

```bash
# 配置腾讯云镜像源
sudo dnf config-manager --add-repo http://mirrors.tencent.com/docker-ce/linux/centos/docker-ce.repo
```

### 4. 安装Docker

```bash
# 卸载旧版本
sudo dnf remove -y docker docker-client docker-client-latest docker-common docker-latest docker-latest-logrotate docker-logrotate docker-engine podman runc

# 添加Docker仓库
sudo dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 安装Docker
sudo dnf install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker
```

### 5. 安装Docker Compose

```bash
# 方法1: 使用Docker Compose Plugin (推荐)
docker compose version

# 方法2: 安装独立版本
sudo curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
```

### 6. 配置Docker镜像加速

```bash
# 创建Docker配置目录
sudo mkdir -p /etc/docker

# 配置镜像加速
sudo tee /etc/docker/daemon.json <<EOF
{
  "registry-mirrors": [
    "https://mirror.ccs.tencentyun.com",
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "exec-opts": ["native.cgroupdriver=systemd"],
  "live-restore": true
}
EOF

# 重启Docker
sudo systemctl daemon-reload
sudo systemctl restart docker
```

### 7. 配置防火墙

```bash
# 开放必要端口
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --permanent --add-port=8000/tcp

# 配置Docker网络
sudo firewall-cmd --permanent --zone=trusted --add-interface=docker0
sudo firewall-cmd --permanent --zone=trusted --add-masquerade
sudo firewall-cmd --permanent --zone=public --add-masquerade

# 重载防火墙
sudo firewall-cmd --reload
```

### 8. 配置SELinux

```bash
# 检查SELinux状态
getenforce

# 如果是Enforcing，配置相关策略
sudo dnf install -y policycoreutils-python-utils container-selinux
sudo setsebool -P httpd_can_network_connect 1
sudo setsebool -P container_manage_cgroup 1
```

### 9. 部署应用

```bash
# 执行部署脚本
chmod +x deploy_opencloudos.sh
sudo ./deploy_opencloudos.sh
```

## ⚙️ 配置说明

### 环境变量配置

部署完成后，配置文件位于 `/opt/wb_system/.env`：

```env
# 数据库配置
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=wb_user
MYSQL_PASSWORD=<自动生成的密码>
MYSQL_DATABASE=wb

# 应用配置
SECRET_KEY=<自动生成的密钥>
DEBUG=false
LOG_LEVEL=info
```

### 数据库密码

数据库密码保存在 `/opt/wb_system/passwords.txt` 文件中：
```bash
# 查看数据库密码
sudo cat /opt/wb_system/passwords.txt
```

### 项目目录结构

```
/opt/wb_system/
├── app/                    # 应用代码
├── nginx/                  # Nginx配置
├── logs/                   # 日志文件
├── data/                   # 数据目录
├── backups/                # 备份目录
├── docker-compose.yml      # Docker编排文件
├── .env                    # 环境配置
└── passwords.txt           # 数据库密码
```

## 🛠️ 服务管理

### 系统服务管理

```bash
# 启动服务
sudo systemctl start wb-system

# 停止服务
sudo systemctl stop wb-system

# 重启服务
sudo systemctl restart wb-system

# 查看服务状态
sudo systemctl status wb-system

# 开机自启
sudo systemctl enable wb-system
```

### Docker服务管理

```bash
# 进入项目目录
cd /opt/wb_system

# 查看服务状态
sudo docker-compose ps

# 查看日志
sudo docker-compose logs -f

# 重启特定服务
sudo docker-compose restart backend

# 停止所有服务
sudo docker-compose down

# 启动所有服务
sudo docker-compose up -d
```

### OpenCloudOS 9专用管理命令

```bash
# 查看系统状态 (包含OpenCloudOS信息)
wb-status

# 查看系统日志
wb-logs

# 查看特定服务日志
wb-logs backend

# 备份数据库 (自动压缩)
wb-backup

# 更新系统
wb-update
```

## 🔍 故障排除

### OpenCloudOS 9特有问题

#### 1. DNF包管理器问题
```bash
# 清理DNF缓存
sudo dnf clean all

# 重建缓存
sudo dnf makecache

# 检查仓库
sudo dnf repolist
```

#### 2. 腾讯云镜像源问题
```bash
# 检查腾讯云镜像源连接
curl -I http://mirrors.tencent.com

# 如果连接失败，使用默认源
sudo dnf config-manager --disable tencent-*
```

#### 3. Docker Compose Plugin问题
```bash
# 检查Docker Compose Plugin
docker compose version

# 如果失败，使用独立版本
sudo curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 通用问题

#### 1. 防火墙问题
```bash
# 检查防火墙状态
sudo firewall-cmd --state

# 查看开放端口
sudo firewall-cmd --list-ports

# 临时关闭防火墙测试
sudo systemctl stop firewalld
```

#### 2. SELinux问题
```bash
# 检查SELinux状态
getenforce

# 查看SELinux日志
sudo ausearch -m avc -ts recent

# 临时关闭SELinux测试
sudo setenforce 0
```

#### 3. Docker问题
```bash
# 检查Docker状态
sudo systemctl status docker

# 查看Docker日志
sudo journalctl -u docker -f

# 重启Docker服务
sudo systemctl restart docker
```

### 日志查看

```bash
# 查看系统日志
sudo journalctl -u wb-system -f

# 查看Docker日志
cd /opt/wb_system
sudo docker-compose logs -f

# 查看特定服务日志
sudo docker-compose logs backend
sudo docker-compose logs mysql
sudo docker-compose logs nginx
```

## 🚀 性能优化

### OpenCloudOS 9特有优化

#### 1. 腾讯云优化
```bash
# 使用腾讯云镜像加速
sudo tee /etc/docker/daemon.json <<EOF
{
  "registry-mirrors": [
    "https://mirror.ccs.tencentyun.com"
  ]
}
EOF
sudo systemctl restart docker
```

#### 2. DNF优化
```bash
# 配置DNF并行下载
echo "max_parallel_downloads=10" >> /etc/dnf/dnf.conf
echo "fastestmirror=True" >> /etc/dnf/dnf.conf
```

#### 3. 系统优化
```bash
# 优化内核参数
echo 'vm.max_map_count=262144' >> /etc/sysctl.conf
echo 'fs.file-max=65536' >> /etc/sysctl.conf
echo 'net.core.somaxconn=65535' >> /etc/sysctl.conf
sysctl -p
```

### 容器优化

#### 1. 资源限制
```yaml
# 在docker-compose.yml中添加
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

#### 2. 存储优化
```bash
# 使用SSD存储
# 配置Docker存储驱动
sudo mkdir -p /etc/docker
echo '{"storage-driver": "overlay2"}' > /etc/docker/daemon.json
sudo systemctl restart docker
```

## 🔐 安全配置

### 1. 防火墙安全
```bash
# 只开放必要端口
sudo firewall-cmd --permanent --remove-service=ssh
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

### 2. SSH安全
```bash
# 修改SSH配置
sudo vi /etc/ssh/sshd_config

# 建议配置：
# Port 2222
# PermitRootLogin no
# PasswordAuthentication no

sudo systemctl restart sshd
```

### 3. 定期更新
```bash
# 配置自动更新
sudo dnf install -y dnf-automatic
sudo systemctl enable --now dnf-automatic.timer
```

## 📊 监控和维护

### 1. 系统监控
```bash
# 查看系统资源
wb-status

# 查看容器资源
docker stats

# 查看磁盘使用
df -h
```

### 2. 定期维护
```bash
# 清理Docker缓存
docker system prune -f

# 清理DNF缓存
sudo dnf clean all

# 备份数据库
wb-backup
```

## 🎉 部署完成

部署完成后，您可以：

1. **访问系统**: http://your-server-ip
2. **查看API文档**: http://your-server-ip/docs
3. **配置前端**: 在前端设置中配置服务器地址为 `your-server-ip:8000`

### OpenCloudOS 9特色功能

- ✅ **腾讯云优化**: 自动配置腾讯云镜像加速
- ✅ **企业级稳定性**: 基于RHEL 8的稳定内核
- ✅ **现代化工具**: DNF包管理器和最新容器技术
- ✅ **安全增强**: 企业级安全配置和策略
- ✅ **云原生支持**: 完美支持容器化部署

恭喜！您已成功在OpenCloudOS 9服务器上部署了微博设备管理系统！
