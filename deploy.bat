@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Docker部署脚本 (Windows版本)
:: 用于快速部署微博设备管理系统

echo 🚀 开始部署微博设备管理系统...
echo.

:: 检查Docker是否安装
echo [INFO] 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose未安装，请先安装Docker Compose
    pause
    exit /b 1
)

echo [SUCCESS] Docker环境检查通过

:: 创建必要的目录
echo [INFO] 创建必要的目录...
if not exist "logs\nginx" mkdir logs\nginx
if not exist "mysql\init" mkdir mysql\init
if not exist "nginx\ssl" mkdir nginx\ssl
echo [SUCCESS] 目录创建完成

:: 设置环境配置
echo [INFO] 设置环境配置...
if not exist ".env" (
    if exist ".env.example" (
        copy .env.example .env >nul
        echo [WARNING] 已复制.env.example到.env，请根据需要修改配置
    ) else (
        echo [ERROR] .env.example文件不存在
        pause
        exit /b 1
    )
) else (
    echo [INFO] .env文件已存在，跳过复制
)

:: 处理命令行参数
if "%1"=="build" goto build_only
if "%1"=="start" goto start_only
if "%1"=="stop" goto stop_only
if "%1"=="restart" goto restart_only
if "%1"=="logs" goto logs_only
if "%1"=="status" goto status_only
if "%1"=="clean" goto clean_only

:: 默认完整部署流程
goto full_deploy

:build_only
echo [INFO] 仅构建镜像...
docker-compose build --no-cache
if errorlevel 1 (
    echo [ERROR] 镜像构建失败
    pause
    exit /b 1
)
echo [SUCCESS] 镜像构建完成
goto end

:start_only
echo [INFO] 启动服务...
goto start_services

:stop_only
echo [INFO] 停止服务...
docker-compose down
goto end

:restart_only
echo [INFO] 重启服务...
docker-compose restart
goto end

:logs_only
echo [INFO] 查看日志...
docker-compose logs -f
goto end

:status_only
echo [INFO] 查看状态...
docker-compose ps
goto end

:clean_only
echo [WARNING] 清理所有容器和镜像...
set /p confirm="确定要清理所有容器和镜像吗？(y/N): "
if /i "!confirm!"=="y" (
    docker-compose down -v --rmi all
    echo [SUCCESS] 清理完成
) else (
    echo [INFO] 取消清理操作
)
goto end

:full_deploy
:: 构建镜像
echo [INFO] 构建Docker镜像...
docker-compose build --no-cache
if errorlevel 1 (
    echo [ERROR] 镜像构建失败
    pause
    exit /b 1
)
echo [SUCCESS] 镜像构建完成

:start_services
:: 启动服务
echo [INFO] 启动服务...

:: 先启动数据库
echo [INFO] 启动数据库服务...
docker-compose up -d mysql redis
if errorlevel 1 (
    echo [ERROR] 数据库启动失败
    pause
    exit /b 1
)

:: 等待数据库启动
echo [INFO] 等待数据库启动...
timeout /t 30 /nobreak >nul

:: 启动后端服务
echo [INFO] 启动后端服务...
docker-compose up -d backend
if errorlevel 1 (
    echo [ERROR] 后端服务启动失败
    pause
    exit /b 1
)

:: 等待后端启动
echo [INFO] 等待后端服务启动...
timeout /t 20 /nobreak >nul

:: 启动Nginx
echo [INFO] 启动Nginx服务...
docker-compose up -d nginx
if errorlevel 1 (
    echo [ERROR] Nginx启动失败
    pause
    exit /b 1
)

echo [SUCCESS] 所有服务启动完成

:: 检查服务状态
echo [INFO] 检查服务状态...
echo.
docker-compose ps
echo.

:: 等待服务完全启动
echo [INFO] 等待服务完全启动...
timeout /t 10 /nobreak >nul

:: 检查健康状态
echo [INFO] 检查服务健康状态...

:: 检查后端健康状态
curl -f http://localhost:8000/health >nul 2>&1
if errorlevel 1 (
    echo [WARNING] 后端服务健康检查失败，请检查日志
) else (
    echo [SUCCESS] 后端服务健康检查通过
)

:: 检查Nginx
curl -f http://localhost/health >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Nginx代理健康检查失败，请检查配置
) else (
    echo [SUCCESS] Nginx代理健康检查通过
)

:: 显示访问信息
echo.
echo [SUCCESS] 部署完成！
echo.
echo ===================================
echo 🎉 微博设备管理系统部署成功！
echo ===================================
echo.
echo 📱 访问地址：
echo   - 主服务: http://localhost
echo   - API文档: http://localhost/docs
echo   - 后端直连: http://localhost:8000
echo   - 健康检查: http://localhost/health
echo.
echo 🗄️ 数据库信息：
echo   - MySQL: localhost:3306
echo   - 用户名: wb_user
echo   - 密码: wb_password
echo   - 数据库: wb
echo.
echo 📊 管理命令：
echo   - 查看日志: deploy.bat logs
echo   - 停止服务: deploy.bat stop
echo   - 重启服务: deploy.bat restart
echo   - 查看状态: deploy.bat status
echo.
echo ⚙️ 前端配置：
echo   请在前端设置中配置服务器地址为：
echo   - 本地访问: localhost:8000
echo   - 局域网访问: ^<你的IP地址^>:8000
echo.

:end
pause
