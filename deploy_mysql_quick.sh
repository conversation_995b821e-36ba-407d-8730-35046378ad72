#!/bin/bash

# 微博任务管理系统 - MySQL快速部署方案
# 一键部署，快速使用

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

echo "========================================"
echo "  微博任务管理系统 - MySQL快速部署"
echo "========================================"
echo "一键部署，快速使用"
echo ""

# 1. 环境检查
log_step "1. 环境检查"

if [ "$EUID" -ne 0 ]; then
    log_error "请使用root用户运行此脚本"
    exit 1
fi

PYTHON_VERSION=$(/usr/bin/python3 --version 2>&1)
log_info "Python版本: $PYTHON_VERSION"

PROJECT_DIR="/www/wwwroot/fastApiProject"
cd "$PROJECT_DIR"

# 2. 安装MySQL服务器
log_step "2. 安装MySQL服务器"

if systemctl is-active --quiet mysqld; then
    log_info "✅ MySQL已运行"
else
    log_info "安装MySQL服务器..."
    dnf install -y mysql-server
    systemctl start mysqld
    systemctl enable mysqld
    log_info "✅ MySQL安装完成"
fi

# 3. 配置MySQL
log_step "3. 配置MySQL"

# 获取临时密码
TEMP_PASSWORD=""
if [ -f /var/log/mysqld.log ]; then
    TEMP_PASSWORD=$(grep 'temporary password' /var/log/mysqld.log 2>/dev/null | tail -1 | awk '{print $NF}' || echo "")
fi

if [ -n "$TEMP_PASSWORD" ]; then
    log_info "找到MySQL临时密码: $TEMP_PASSWORD"
    log_info "自动配置MySQL..."
    
    # 自动配置MySQL
    mysql -u root -p"$TEMP_PASSWORD" --connect-expired-password << 'EOF'
-- 设置新密码
ALTER USER 'root'@'localhost' IDENTIFIED BY 'WbRoot123456!';

-- 创建数据库
CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 刷新权限
FLUSH PRIVILEGES;
EOF
    
    log_info "✅ MySQL配置完成"
else
    log_info "尝试直接配置MySQL..."
    # 尝试无密码连接
    mysql -u root << 'EOF' 2>/dev/null || {
        log_warn "需要手动配置MySQL密码"
        echo "请运行: mysql_secure_installation"
        echo "设置root密码为: WbRoot123456!"
        echo "然后重新运行此脚本"
        exit 1
    }
-- 设置密码
ALTER USER 'root'@'localhost' IDENTIFIED BY 'WbRoot123456!';

-- 创建数据库
CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 刷新权限
FLUSH PRIVILEGES;
EOF
    log_info "✅ MySQL配置完成"
fi

# 4. 验证MySQL配置
log_step "4. 验证MySQL配置"

if mysql -u root -p'WbRoot123456!' -e "USE wb; SELECT 1;" >/dev/null 2>&1; then
    log_info "✅ MySQL数据库验证成功"
else
    log_error "❌ MySQL数据库验证失败"
    exit 1
fi

# 5. 创建Python虚拟环境
log_step "5. 创建Python虚拟环境"

if [ -d "venv_wb" ]; then
    log_info "✅ 虚拟环境已存在"
else
    log_info "创建虚拟环境..."
    /usr/bin/python3 -m venv venv_wb
    log_info "✅ 虚拟环境创建成功"
fi

# 激活虚拟环境
source venv_wb/bin/activate

# 6. 安装Python依赖
log_step "6. 安装Python依赖"

log_info "升级pip..."
python -m pip install --upgrade pip

log_info "安装项目依赖..."
python -m pip install \
    fastapi==0.115.12 \
    uvicorn==0.34.3 \
    sqlalchemy==2.0.41 \
    pymysql==1.1.1 \
    websockets==15.0.1 \
    requests==2.32.3 \
    pydantic==2.11.5 \
    python-dotenv==1.1.0 \
    PyYAML==6.0.2 \
    pytz==2024.2 \
    alembic==1.13.3

log_info "✅ Python依赖安装完成"

# 7. 配置数据库连接
log_step "7. 配置数据库连接"

# 备份原配置
cp app/config.py app/config.py.backup 2>/dev/null || true

cat > app/config.py << 'EOF'
# MySQL数据库配置
MYSQL_USER = "root"
MYSQL_PASSWORD = "WbRoot123456!"
MYSQL_HOST = "127.0.0.1"
MYSQL_PORT = "3306"
MYSQL_DB = "wb"

DATABASE_URL = f"mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}?charset=utf8mb4"

print(f"[CONFIG] Database: {MYSQL_USER}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DB}")
EOF

log_info "✅ 数据库配置完成"

# 8. 创建数据库表
log_step "8. 创建数据库表"

python3 << 'EOF'
import sys
sys.path.append('.')

try:
    from app.db import engine, Base
    from app.models import device, task, group, device_status, group_task_status
    
    print("测试数据库连接...")
    with engine.connect() as conn:
        conn.execute("SELECT 1")
    print("✅ 数据库连接成功")
    
    print("创建数据库表...")
    Base.metadata.create_all(bind=engine)
    print("✅ 数据库表创建成功")
    
    # 显示创建的表
    with engine.connect() as conn:
        result = conn.execute("SHOW TABLES")
        tables = [row[0] for row in result]
        print(f"创建的表: {tables}")
        
except Exception as e:
    print(f"❌ 数据库操作失败: {e}")
    sys.exit(1)
EOF

# 9. 配置防火墙
log_step "9. 配置防火墙"

if systemctl is-active --quiet firewalld; then
    log_info "配置防火墙规则..."
    firewall-cmd --permanent --add-port=11234/tcp >/dev/null 2>&1 || true
    firewall-cmd --reload >/dev/null 2>&1 || true
    log_info "✅ 防火墙配置完成"
else
    log_warn "防火墙未运行，跳过配置"
fi

# 10. 创建启动脚本
log_step "10. 创建启动脚本"

cat > start_wb_mysql.sh << 'EOF'
#!/bin/bash

cd "$(dirname "$0")"

echo "========================================"
echo "  微博任务管理系统启动"
echo "========================================"

# 检查MySQL状态
if ! systemctl is-active --quiet mysqld; then
    echo "启动MySQL..."
    sudo systemctl start mysqld
    sleep 3
fi

# 激活虚拟环境
echo "激活Python环境..."
source venv_wb/bin/activate

# 验证数据库连接
echo "验证数据库连接..."
if mysql -u root -p'WbRoot123456!' -e "USE wb; SELECT 1;" >/dev/null 2>&1; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败"
    echo "请检查MySQL状态: systemctl status mysqld"
    exit 1
fi

echo ""
echo "========================================"
echo "  系统信息"
echo "========================================"
echo "Python版本: $(python --version)"
echo "数据库: MySQL 8.0 (wb)"
echo "端口: 11234"
echo "访问地址: http://***********:11234"
echo "API文档: http://***********:11234/docs"
echo "健康检查: http://***********:11234/health"
echo "WebSocket: ws://***********:11234/ws"
echo "========================================"
echo ""

# 启动FastAPI应用
echo "🚀 启动微博任务管理系统..."
uvicorn app.main:app --host 0.0.0.0 --port 11234 --reload
EOF

chmod +x start_wb_mysql.sh

# 11. 创建管理脚本
log_step "11. 创建管理脚本"

cat > manage_wb_mysql.sh << 'EOF'
#!/bin/bash

case "$1" in
    start)
        echo "启动微博任务管理系统..."
        ./start_wb_mysql.sh
        ;;
    db-status)
        echo "MySQL状态:"
        systemctl status mysqld
        ;;
    db-connect)
        echo "连接MySQL数据库..."
        mysql -u root -p'WbRoot123456!' wb
        ;;
    db-backup)
        echo "备份数据库..."
        mysqldump -u root -p'WbRoot123456!' wb > wb_backup_$(date +%Y%m%d_%H%M%S).sql
        echo "备份完成: wb_backup_$(date +%Y%m%d_%H%M%S).sql"
        ;;
    test)
        echo "测试系统访问..."
        curl -s http://localhost:11234/health | python3 -m json.tool 2>/dev/null || curl -s http://localhost:11234/health
        ;;
    logs)
        echo "查看MySQL日志..."
        tail -f /var/log/mysqld.log
        ;;
    restart-db)
        echo "重启MySQL..."
        sudo systemctl restart mysqld
        ;;
    *)
        echo "用法: $0 {start|db-status|db-connect|db-backup|test|logs|restart-db}"
        echo ""
        echo "命令说明:"
        echo "  start      - 启动系统"
        echo "  db-status  - 查看MySQL状态"
        echo "  db-connect - 连接MySQL数据库"
        echo "  db-backup  - 备份数据库"
        echo "  test       - 测试系统访问"
        echo "  logs       - 查看MySQL日志"
        echo "  restart-db - 重启MySQL"
        exit 1
        ;;
esac
EOF

chmod +x manage_wb_mysql.sh

# 12. 创建系统服务
log_step "12. 创建系统服务"

cat > /etc/systemd/system/wb-mysql.service << EOF
[Unit]
Description=微博任务管理系统 MySQL版本
After=network.target mysqld.service

[Service]
Type=simple
User=root
WorkingDirectory=$PROJECT_DIR
Environment=PATH=$PROJECT_DIR/venv_wb/bin
ExecStart=$PROJECT_DIR/venv_wb/bin/uvicorn app.main:app --host 0.0.0.0 --port 11234
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable wb-mysql

# 13. 最终测试
log_step "13. 最终测试"

log_info "快速测试系统启动..."
source venv_wb/bin/activate

# 快速测试
timeout 10s uvicorn app.main:app --host 127.0.0.1 --port 11235 &
TEST_PID=$!
sleep 5

if curl -s http://127.0.0.1:11235/health >/dev/null 2>&1; then
    log_info "✅ 系统测试成功"
else
    log_warn "⚠️ 系统测试未完全成功，但配置已完成"
fi

kill $TEST_PID 2>/dev/null || true

echo ""
echo "========================================"
echo "  🎉 MySQL版本部署完成！"
echo "========================================"
echo ""
log_info "系统配置:"
echo "  - Python版本: $PYTHON_VERSION"
echo "  - 数据库: MySQL 8.0"
echo "  - 数据库名: wb"
echo "  - 用户名: root"
echo "  - 密码: WbRoot123456!"
echo "  - 端口: 11234"
echo ""
log_info "启动方式:"
echo "  - 手动启动: ./start_wb_mysql.sh"
echo "  - 系统服务: systemctl start wb-mysql"
echo "  - 管理脚本: ./manage_wb_mysql.sh start"
echo ""
log_info "访问地址:"
echo "  - 主页: http://***********:11234"
echo "  - API文档: http://***********:11234/docs"
echo "  - 健康检查: http://***********:11234/health"
echo "  - WebSocket: ws://***********:11234/ws"
echo ""
log_info "管理命令:"
echo "  - 数据库状态: ./manage_wb_mysql.sh db-status"
echo "  - 连接数据库: ./manage_wb_mysql.sh db-connect"
echo "  - 备份数据库: ./manage_wb_mysql.sh db-backup"
echo "  - 测试访问: ./manage_wb_mysql.sh test"
echo ""
log_info "前端配置:"
echo "  SERVER_URL = \"http://***********:11234\""
echo "  API_BASE_URL = \"http://***********:11234/api\""
echo "  WS_URL = \"ws://***********:11234/ws\""
echo ""
echo "🚀 现在可以启动系统: ./start_wb_mysql.sh"
