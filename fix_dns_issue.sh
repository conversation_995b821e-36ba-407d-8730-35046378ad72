#!/bin/bash

# 修复DNS反向查找问题

echo "=== 修复DNS反向查找问题 ==="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 1. 检查DNS解析问题
echo "1. DNS解析诊断"
echo "==============="

log_info "检查正向DNS解析:"
nslookup *********** 2>/dev/null || echo "  正向DNS解析失败"

log_info "检查反向DNS解析:"
nslookup *********** ******* 2>/dev/null || echo "  反向DNS解析失败"

log_info "当前DNS配置:"
cat /etc/resolv.conf

echo ""

# 2. 修复DNS配置
echo "2. 修复DNS配置"
echo "=============="

log_info "备份原DNS配置..."
cp /etc/resolv.conf /etc/resolv.conf.backup

log_info "设置可靠的DNS服务器..."
cat > /etc/resolv.conf << 'EOF'
nameserver *******
nameserver 114.114.114.114
nameserver 223.5.5.5
EOF

log_info "重启DNS解析服务..."
systemctl restart systemd-resolved 2>/dev/null || true

echo ""

# 3. 配置应用禁用反向DNS查找
echo "3. 配置应用"
echo "==========="

APP_DIR="/opt/wb_system"
if [ -d "$APP_DIR" ]; then
    cd $APP_DIR
    
    log_info "修改应用配置禁用反向DNS查找..."
    
    # 创建优化的启动脚本
    cat > start_optimized.sh << 'EOF'
#!/bin/bash
cd /opt/wb_system
source venv/bin/activate
export $(cat .env | grep -v '^#' | xargs) 2>/dev/null || true

# 启动uvicorn，禁用访问日志中的主机名解析
uvicorn app.main:app \
    --host 0.0.0.0 \
    --port 8000 \
    --access-log \
    --use-colors \
    --loop uvloop
EOF
    
    chmod +x start_optimized.sh
    
    log_info "创建优化的systemd服务配置..."
    cat > /etc/systemd/system/wb-system-optimized.service << 'EOF'
[Unit]
Description=微博任务管理系统 (优化版)
After=network.target mysql.service redis.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/wb_system
Environment=PATH=/opt/wb_system/venv/bin
EnvironmentFile=/opt/wb_system/.env
ExecStart=/opt/wb_system/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000 --access-log
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    
else
    log_warn "应用目录不存在，跳过应用配置"
fi

echo ""

# 4. 测试网络连接
echo "4. 网络连接测试"
echo "==============="

log_info "测试本地回环:"
curl -s --connect-timeout 5 http://127.0.0.1:8000/health >/dev/null && echo "  ✅ 本地回环正常" || echo "  ❌ 本地回环失败"

log_info "测试内网IP:"
INTERNAL_IP=$(ip route get ******* | awk '{print $7; exit}')
curl -s --connect-timeout 5 http://$INTERNAL_IP:8000/health >/dev/null && echo "  ✅ 内网IP正常" || echo "  ❌ 内网IP失败"

log_info "测试外网IP (无DNS解析):"
curl -s --connect-timeout 10 --resolve ***********:8000:*********** http://***********:8000/health >/dev/null && echo "  ✅ 外网IP正常" || echo "  ❌ 外网IP失败"

echo ""

# 5. 配置hosts文件
echo "5. 配置hosts文件"
echo "================"

log_info "添加本地hosts条目..."
if ! grep -q "***********" /etc/hosts; then
    echo "*********** myserver wb-system" >> /etc/hosts
    log_info "已添加hosts条目"
else
    log_info "hosts条目已存在"
fi

log_info "测试hosts解析:"
curl -s --connect-timeout 5 http://myserver:8000/health >/dev/null && echo "  ✅ hosts解析正常" || echo "  ❌ hosts解析失败"

echo ""

# 6. 重启服务
echo "6. 重启服务"
echo "==========="

log_info "停止原服务..."
systemctl stop wb-system 2>/dev/null || true
pkill -f "uvicorn.*8000" 2>/dev/null || true

sleep 3

log_info "启动优化服务..."
if [ -f "/etc/systemd/system/wb-system-optimized.service" ]; then
    systemctl enable wb-system-optimized
    systemctl start wb-system-optimized
    
    sleep 5
    
    if systemctl is-active wb-system-optimized >/dev/null 2>&1; then
        log_info "✅ 优化服务启动成功"
    else
        log_warn "优化服务启动失败，使用手动启动..."
        cd $APP_DIR && ./start_optimized.sh &
    fi
else
    log_warn "使用原服务启动..."
    systemctl start wb-system
fi

echo ""

# 7. 最终测试
echo "7. 最终测试"
echo "==========="

sleep 5

log_info "测试服务状态:"
if curl -s --connect-timeout 5 http://localhost:8000/health >/dev/null; then
    echo "  ✅ 服务运行正常"
    
    log_info "测试外部访问:"
    curl -s --connect-timeout 10 http://***********:8000/health && echo "  ✅ 外部访问成功" || echo "  ❌ 外部访问失败"
    
else
    echo "  ❌ 服务未正常运行"
fi

echo ""
echo "=== DNS修复完成 ==="
echo ""
log_info "如果问题仍然存在，建议:"
echo "1. 使用80端口: 配置Nginx反向代理"
echo "2. 检查云服务商网络策略"
echo "3. 联系云服务商技术支持"
