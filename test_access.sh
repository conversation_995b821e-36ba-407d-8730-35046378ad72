#!/bin/bash

# 测试服务访问脚本

echo "=== 微博任务管理系统访问测试 ==="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 1. 检查服务进程
echo "1. 检查服务进程"
echo "==============="
log_info "查找uvicorn进程:"
ps aux | grep uvicorn | grep -v grep || echo "  未找到uvicorn进程"

log_info "查找wb-system服务:"
systemctl is-active wb-system 2>/dev/null && echo "  ✅ wb-system服务运行中" || echo "  ❌ wb-system服务未运行"

echo ""

# 2. 检查端口监听
echo "2. 检查端口监听"
echo "==============="
log_info "检查8000端口监听:"
if netstat -tlnp | grep ":8000 "; then
    echo "  ✅ 8000端口正在监听"
else
    echo "  ❌ 8000端口未监听"
fi

echo ""

# 3. 测试本地访问
echo "3. 测试本地访问"
echo "==============="

log_info "测试localhost:8000/health:"
if curl -s --connect-timeout 5 http://localhost:8000/health >/dev/null 2>&1; then
    echo "  ✅ localhost访问成功"
    echo "  响应内容:"
    curl -s http://localhost:8000/health | python3 -m json.tool 2>/dev/null || curl -s http://localhost:8000/health
else
    echo "  ❌ localhost访问失败"
    log_debug "详细错误信息:"
    curl -v http://localhost:8000/health 2>&1 | head -10
fi

echo ""

log_info "测试127.0.0.1:8000/health:"
if curl -s --connect-timeout 5 http://127.0.0.1:8000/health >/dev/null 2>&1; then
    echo "  ✅ 127.0.0.1访问成功"
else
    echo "  ❌ 127.0.0.1访问失败"
fi

echo ""

# 4. 测试内网IP访问
echo "4. 测试内网IP访问"
echo "=================="

INTERNAL_IP=$(ip route get ******* | awk '{print $7; exit}')
log_info "内网IP: $INTERNAL_IP"

if curl -s --connect-timeout 5 http://$INTERNAL_IP:8000/health >/dev/null 2>&1; then
    echo "  ✅ 内网IP访问成功"
else
    echo "  ❌ 内网IP访问失败"
fi

echo ""

# 5. 测试外网IP访问
echo "5. 测试外网IP访问"
echo "=================="

EXTERNAL_IP=$(curl -s --connect-timeout 5 ifconfig.me 2>/dev/null || curl -s --connect-timeout 5 ipinfo.io/ip 2>/dev/null)
log_info "外网IP: $EXTERNAL_IP"

if [ -n "$EXTERNAL_IP" ]; then
    log_info "从服务器内部测试外网IP访问:"
    if curl -s --connect-timeout 5 http://$EXTERNAL_IP:8000/health >/dev/null 2>&1; then
        echo "  ✅ 外网IP访问成功"
    else
        echo "  ❌ 外网IP访问失败（可能是安全组问题）"
    fi
else
    echo "  ❌ 无法获取外网IP"
fi

echo ""

# 6. 检查防火墙设置
echo "6. 检查防火墙设置"
echo "=================="

if systemctl is-active firewalld >/dev/null 2>&1; then
    log_info "Firewalld状态: 运行中"
    
    if firewall-cmd --query-port=8000/tcp 2>/dev/null; then
        echo "  ✅ 8000端口已在防火墙中开放"
    else
        echo "  ❌ 8000端口未在防火墙中开放"
        log_warn "需要运行: firewall-cmd --add-port=8000/tcp --permanent && firewall-cmd --reload"
    fi
    
    log_info "当前开放的端口:"
    firewall-cmd --list-ports
else
    log_info "Firewalld状态: 未运行"
fi

echo ""

# 7. 检查应用日志
echo "7. 检查应用日志"
echo "==============="

log_info "系统服务日志 (最后10行):"
journalctl -u wb-system --no-pager -n 10 2>/dev/null || echo "  无法获取系统服务日志"

echo ""

if [ -f "/opt/wb_system/logs/app.log" ]; then
    log_info "应用日志 (最后10行):"
    tail -10 /opt/wb_system/logs/app.log
fi

echo ""

# 8. 网络诊断
echo "8. 网络诊断"
echo "==========="

log_info "路由表:"
ip route | head -3

log_info "网络接口:"
ip addr show | grep -E "(inet |UP)" | head -5

echo ""

# 9. 生成测试报告
echo "9. 测试总结"
echo "==========="

# 检查本地访问
if curl -s --connect-timeout 3 http://localhost:8000/health >/dev/null 2>&1; then
    echo "✅ 应用本地运行正常"
    
    if firewall-cmd --query-port=8000/tcp 2>/dev/null; then
        echo "✅ 防火墙端口已开放"
        echo ""
        echo "🔧 问题可能是云服务器安全组未开放8000端口"
        echo "   请在云服务器控制台配置安全组，开放8000端口"
        echo ""
        echo "🌐 配置完成后访问: http://$EXTERNAL_IP:8000/health"
    else
        echo "❌ 防火墙端口未开放"
        echo ""
        echo "🔧 解决方案:"
        echo "   sudo firewall-cmd --add-port=8000/tcp --permanent"
        echo "   sudo firewall-cmd --reload"
    fi
else
    echo "❌ 应用本地访问失败"
    echo ""
    echo "🔧 可能的问题:"
    echo "   1. 应用未正常启动"
    echo "   2. 端口绑定问题"
    echo "   3. 应用配置错误"
    echo ""
    echo "🔧 建议检查:"
    echo "   sudo systemctl status wb-system"
    echo "   sudo journalctl -u wb-system -f"
fi

echo ""
echo "=== 测试完成 ==="
