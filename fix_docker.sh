#!/bin/bash

# Docker服务修复脚本

echo "=== Docker服务修复 ==="

# 检查Docker服务状态
echo "1. 检查Docker服务状态..."
systemctl status docker.service --no-pager

echo ""
echo "2. 查看Docker服务错误日志..."
journalctl -xeu docker.service --no-pager -n 20

echo ""
echo "3. 检查Docker配置文件..."
if [ -f "/etc/docker/daemon.json" ]; then
    echo "当前daemon.json内容:"
    cat /etc/docker/daemon.json
    echo ""
    
    # 验证JSON格式
    if python3 -m json.tool /etc/docker/daemon.json >/dev/null 2>&1; then
        echo "✅ daemon.json格式正确"
    else
        echo "❌ daemon.json格式错误，正在修复..."
        
        # 备份错误的配置
        cp /etc/docker/daemon.json /etc/docker/daemon.json.error
        
        # 创建简单的配置
        cat > /etc/docker/daemon.json << 'EOF'
{
    "dns": ["8.8.8.8", "114.114.114.114"]
}
EOF
        echo "已创建简化的daemon.json"
    fi
else
    echo "daemon.json不存在，创建新的..."
    mkdir -p /etc/docker
    cat > /etc/docker/daemon.json << 'EOF'
{
    "dns": ["8.8.8.8", "114.114.114.114"]
}
EOF
fi

echo ""
echo "4. 尝试重启Docker服务..."

# 停止Docker服务
systemctl stop docker.service
systemctl stop docker.socket
sleep 3

# 清理Docker进程
pkill -f dockerd 2>/dev/null || true
sleep 2

# 启动Docker服务
systemctl start docker.service

# 等待服务启动
sleep 5

# 检查服务状态
if systemctl is-active docker.service >/dev/null 2>&1; then
    echo "✅ Docker服务启动成功"
    
    # 验证Docker功能
    echo ""
    echo "5. 验证Docker功能..."
    if docker info >/dev/null 2>&1; then
        echo "✅ Docker功能正常"
        
        # 测试DNS配置
        echo ""
        echo "6. 测试Docker DNS配置..."
        if docker run --rm alpine nslookup google.com >/dev/null 2>&1; then
            echo "✅ Docker DNS配置正常"
        else
            echo "❌ Docker DNS配置有问题，但Docker服务已启动"
        fi
        
    else
        echo "❌ Docker功能异常"
    fi
    
else
    echo "❌ Docker服务启动失败"
    
    echo ""
    echo "尝试无配置启动..."
    
    # 移除配置文件
    mv /etc/docker/daemon.json /etc/docker/daemon.json.backup 2>/dev/null || true
    
    # 重启服务
    systemctl restart docker.service
    sleep 5
    
    if systemctl is-active docker.service >/dev/null 2>&1; then
        echo "✅ 无配置启动成功"
        
        # 重新创建简单配置
        cat > /etc/docker/daemon.json << 'EOF'
{
    "dns": ["8.8.8.8"]
}
EOF
        
        echo "重新应用DNS配置..."
        systemctl restart docker.service
        sleep 5
        
        if systemctl is-active docker.service >/dev/null 2>&1; then
            echo "✅ 带DNS配置启动成功"
        else
            echo "❌ 带DNS配置启动失败，保持无配置状态"
            rm -f /etc/docker/daemon.json
        fi
        
    else
        echo "❌ Docker服务完全无法启动"
        echo ""
        echo "请检查："
        echo "1. Docker是否正确安装"
        echo "2. 系统内核版本是否支持"
        echo "3. 是否有其他容器运行时冲突"
        
        # 显示详细错误信息
        echo ""
        echo "详细错误信息："
        journalctl -xeu docker.service --no-pager -n 10
    fi
fi

echo ""
echo "=== 修复完成 ==="

# 显示最终状态
echo "最终状态："
systemctl is-active docker.service && echo "Docker服务: 运行中" || echo "Docker服务: 停止"
docker info >/dev/null 2>&1 && echo "Docker功能: 正常" || echo "Docker功能: 异常"
