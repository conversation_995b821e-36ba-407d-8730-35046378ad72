#!/bin/bash

# 快速修复Docker防火墙冲突

echo "=== 快速修复Docker防火墙冲突 ==="

# 1. 停止Docker
echo "1. 停止Docker服务..."
systemctl stop docker.service
systemctl stop docker.socket
sleep 2

# 2. 清理docker0接口
echo "2. 清理docker0接口..."
if ip link show docker0 >/dev/null 2>&1; then
    ip link set docker0 down
    ip link delete docker0
    echo "docker0接口已删除"
fi

# 3. 从防火墙中移除docker0
echo "3. 从防火墙中移除docker0..."
if systemctl is-active firewalld >/dev/null 2>&1; then
    firewall-cmd --zone=trusted --remove-interface=docker0 2>/dev/null || true
    firewall-cmd --permanent --zone=trusted --remove-interface=docker0 2>/dev/null || true
    firewall-cmd --reload
    echo "已从防火墙中移除docker0"
else
    echo "防火墙未运行"
fi

# 4. 清理Docker网络数据
echo "4. 清理Docker网络数据..."
rm -rf /var/lib/docker/network/* 2>/dev/null || true

# 5. 重启Docker
echo "5. 重启Docker服务..."
systemctl start docker.service
sleep 5

# 6. 检查状态
if systemctl is-active docker.service >/dev/null 2>&1; then
    echo "✅ Docker启动成功！"
    
    if docker info >/dev/null 2>&1; then
        echo "✅ Docker功能正常"
        echo ""
        echo "🎉 修复完成！现在可以构建镜像了"
        echo "运行: ./simple_build.sh"
    else
        echo "❌ Docker功能异常"
    fi
else
    echo "❌ Docker仍然启动失败"
    echo ""
    echo "尝试禁用防火墙..."
    
    # 临时禁用防火墙
    systemctl stop firewalld
    systemctl restart docker.service
    sleep 5
    
    if systemctl is-active docker.service >/dev/null 2>&1; then
        echo "✅ 禁用防火墙后Docker启动成功"
        echo "⚠️  防火墙已被禁用，请注意安全"
    else
        echo "❌ 仍然失败，需要进一步诊断"
    fi
fi
