#!/bin/bash

# 修复Docker防火墙冲突问题

echo "=== 修复Docker防火墙冲突 ==="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root权限运行此脚本"
    exit 1
fi

echo "1. 停止Docker服务..."
systemctl stop docker.service
systemctl stop docker.socket
sleep 3

echo "2. 检查防火墙状态..."
if systemctl is-active firewalld >/dev/null 2>&1; then
    echo "✅ FirewallD正在运行"
    
    echo "3. 检查docker0接口状态..."
    if firewall-cmd --get-zone-of-interface=docker0 2>/dev/null; then
        current_zone=$(firewall-cmd --get-zone-of-interface=docker0 2>/dev/null)
        echo "docker0当前在区域: $current_zone"
        
        echo "4. 从防火墙中移除docker0接口..."
        firewall-cmd --zone=$current_zone --remove-interface=docker0 --permanent 2>/dev/null || true
        firewall-cmd --zone=$current_zone --remove-interface=docker0 2>/dev/null || true
        
        echo "5. 重新加载防火墙配置..."
        firewall-cmd --reload
    else
        echo "docker0接口未绑定到任何区域"
    fi
    
    echo "6. 检查docker区域..."
    if firewall-cmd --get-zones | grep -q docker; then
        echo "docker区域存在"
    else
        echo "创建docker区域..."
        firewall-cmd --permanent --new-zone=docker 2>/dev/null || true
        firewall-cmd --reload
    fi
    
else
    echo "❌ FirewallD未运行"
    
    echo "3. 启动FirewallD..."
    systemctl start firewalld
    sleep 3
    
    if systemctl is-active firewalld >/dev/null 2>&1; then
        echo "✅ FirewallD已启动"
    else
        echo "❌ FirewallD启动失败，尝试禁用防火墙..."
        systemctl disable firewalld
        systemctl stop firewalld
        echo "防火墙已禁用"
    fi
fi

echo "7. 清理Docker网络接口..."
# 删除docker0接口（如果存在）
if ip link show docker0 >/dev/null 2>&1; then
    echo "删除docker0接口..."
    ip link set docker0 down 2>/dev/null || true
    ip link delete docker0 2>/dev/null || true
fi

# 清理其他Docker网络接口
for iface in $(ip link show | grep -o 'br-[a-f0-9]*' | head -5); do
    echo "删除接口: $iface"
    ip link set $iface down 2>/dev/null || true
    ip link delete $iface 2>/dev/null || true
done

echo "8. 清理Docker数据..."
# 清理Docker网络数据
rm -rf /var/lib/docker/network/* 2>/dev/null || true

echo "9. 重启网络服务..."
systemctl restart NetworkManager 2>/dev/null || true
sleep 3

echo "10. 配置防火墙规则..."
if systemctl is-active firewalld >/dev/null 2>&1; then
    # 为Docker配置防火墙规则
    firewall-cmd --permanent --zone=trusted --add-interface=docker0 2>/dev/null || true
    firewall-cmd --permanent --zone=trusted --add-masquerade 2>/dev/null || true
    firewall-cmd --reload
    echo "防火墙规则已配置"
else
    echo "防火墙已禁用，跳过规则配置"
fi

echo "11. 启动Docker服务..."
systemctl start docker.service
sleep 10

# 检查Docker状态
if systemctl is-active docker.service >/dev/null 2>&1; then
    echo "✅ Docker服务启动成功！"
    
    # 验证Docker功能
    if docker info >/dev/null 2>&1; then
        echo "✅ Docker功能正常"
        
        # 显示网络信息
        echo ""
        echo "Docker网络信息:"
        docker network ls
        
        echo ""
        echo "网络接口信息:"
        ip addr show docker0 2>/dev/null || echo "docker0接口未创建（正常）"
        
    else
        echo "❌ Docker功能异常"
    fi
    
else
    echo "❌ Docker服务仍然启动失败"
    
    echo ""
    echo "尝试替代方案..."
    
    # 方案1: 完全禁用防火墙
    echo "方案1: 临时禁用防火墙..."
    systemctl stop firewalld
    systemctl disable firewalld
    
    # 清理iptables规则
    iptables -F
    iptables -X
    iptables -t nat -F
    iptables -t nat -X
    
    # 重启Docker
    systemctl restart docker.service
    sleep 10
    
    if systemctl is-active docker.service >/dev/null 2>&1; then
        echo "✅ 禁用防火墙后Docker启动成功"
    else
        echo "❌ 仍然失败，尝试重新安装Docker..."
        
        # 方案2: 重新安装Docker
        echo "方案2: 重新安装Docker..."
        
        # 完全卸载Docker
        systemctl stop docker.service
        yum remove -y docker-ce docker-ce-cli containerd.io docker-compose-plugin 2>/dev/null || \
        dnf remove -y docker-ce docker-ce-cli containerd.io docker-compose-plugin 2>/dev/null || true
        
        # 清理Docker数据
        rm -rf /var/lib/docker
        rm -rf /etc/docker
        
        # 重新安装
        echo "重新安装Docker..."
        yum install -y yum-utils 2>/dev/null || dnf install -y dnf-utils 2>/dev/null
        yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo 2>/dev/null || \
        dnf config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo 2>/dev/null
        
        yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin 2>/dev/null || \
        dnf install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin 2>/dev/null
        
        # 启动Docker
        systemctl enable docker
        systemctl start docker
        sleep 10
        
        if systemctl is-active docker.service >/dev/null 2>&1; then
            echo "✅ 重新安装后Docker启动成功"
        else
            echo "❌ 重新安装也失败了"
            echo ""
            echo "请手动检查系统兼容性："
            echo "1. 内核版本: $(uname -r)"
            echo "2. 系统版本: $(cat /etc/os-release | grep PRETTY_NAME)"
            echo "3. 可用内存: $(free -h | grep Mem)"
            echo "4. 磁盘空间: $(df -h / | tail -1)"
        fi
    fi
fi

echo ""
echo "=== 修复完成 ==="

# 显示最终状态
echo "最终状态："
if systemctl is-active docker.service >/dev/null 2>&1; then
    echo "✅ Docker服务: 运行中"
    if docker info >/dev/null 2>&1; then
        echo "✅ Docker功能: 正常"
        echo ""
        echo "🎉 现在可以进行Docker构建了！"
        echo "运行: ./simple_build.sh"
    else
        echo "❌ Docker功能: 异常"
    fi
else
    echo "❌ Docker服务: 停止"
fi
