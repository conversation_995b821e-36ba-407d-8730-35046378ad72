('E:\\PythonWBYK\\fastApiProject\\build\\main\\PYZ-00.pyz',
 [('MySQLdb',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\__init__.py',
   'PYMODULE'),
  ('MySQLdb._exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\_exceptions.py',
   'PYMODULE'),
  ('MySQLdb.connections',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\connections.py',
   'PYMODULE'),
  ('MySQLdb.constants',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\constants\\__init__.py',
   'PYMODULE'),
  ('MySQLdb.constants.CLIENT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\constants\\CLIENT.py',
   'PYMODULE'),
  ('MySQLdb.constants.FIELD_TYPE',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('MySQLdb.constants.FLAG',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\constants\\FLAG.py',
   'PYMODULE'),
  ('MySQLdb.converters',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\converters.py',
   'PYMODULE'),
  ('MySQLdb.cursors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\cursors.py',
   'PYMODULE'),
  ('MySQLdb.release',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\release.py',
   'PYMODULE'),
  ('MySQLdb.times',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\MySQLdb\\times.py',
   'PYMODULE'),
  ('__future__', 'D:\\myProgram\\Python39\\lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\myProgram\\Python39\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'D:\\myProgram\\Python39\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\myProgram\\Python39\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression', 'D:\\myProgram\\Python39\\lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\myProgram\\Python39\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\myProgram\\Python39\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\myProgram\\Python39\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\myProgram\\Python39\\lib\\_threading_local.py',
   'PYMODULE'),
  ('annotated_types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\annotated_types\\__init__.py',
   'PYMODULE'),
  ('anyio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\__init__.py',
   'PYMODULE'),
  ('anyio._backends',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('anyio._core',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio._core._asyncio_selector_thread',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_asyncio_selector_thread.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._tempfile',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_tempfile.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio.abc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('anyio.abc._eventloop',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\_eventloop.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.from_thread',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('anyio.streams',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('app', 'E:\\PythonWBYK\\fastApiProject\\app\\__init__.py', 'PYMODULE'),
  ('app.config', 'E:\\PythonWBYK\\fastApiProject\\app\\config.py', 'PYMODULE'),
  ('app.crud', 'E:\\PythonWBYK\\fastApiProject\\app\\crud.py', 'PYMODULE'),
  ('app.db', 'E:\\PythonWBYK\\fastApiProject\\app\\db.py', 'PYMODULE'),
  ('app.models',
   'E:\\PythonWBYK\\fastApiProject\\app\\models\\__init__.py',
   'PYMODULE'),
  ('app.models.device',
   'E:\\PythonWBYK\\fastApiProject\\app\\models\\device.py',
   'PYMODULE'),
  ('app.models.device_status',
   'E:\\PythonWBYK\\fastApiProject\\app\\models\\device_status.py',
   'PYMODULE'),
  ('app.models.group',
   'E:\\PythonWBYK\\fastApiProject\\app\\models\\group.py',
   'PYMODULE'),
  ('app.models.group_task_status',
   'E:\\PythonWBYK\\fastApiProject\\app\\models\\group_task_status.py',
   'PYMODULE'),
  ('app.models.task',
   'E:\\PythonWBYK\\fastApiProject\\app\\models\\task.py',
   'PYMODULE'),
  ('app.routes', '-', 'PYMODULE'),
  ('app.routes.device',
   'E:\\PythonWBYK\\fastApiProject\\app\\routes\\device.py',
   'PYMODULE'),
  ('app.routes.group',
   'E:\\PythonWBYK\\fastApiProject\\app\\routes\\group.py',
   'PYMODULE'),
  ('app.routes.task',
   'E:\\PythonWBYK\\fastApiProject\\app\\routes\\task.py',
   'PYMODULE'),
  ('app.routes.task_sync',
   'E:\\PythonWBYK\\fastApiProject\\app\\routes\\task_sync.py',
   'PYMODULE'),
  ('app.routes.timeout_monitor',
   'E:\\PythonWBYK\\fastApiProject\\app\\routes\\timeout_monitor.py',
   'PYMODULE'),
  ('app.schemas',
   'E:\\PythonWBYK\\fastApiProject\\app\\schemas\\__init__.py',
   'PYMODULE'),
  ('app.schemas.device',
   'E:\\PythonWBYK\\fastApiProject\\app\\schemas\\device.py',
   'PYMODULE'),
  ('app.schemas.group',
   'E:\\PythonWBYK\\fastApiProject\\app\\schemas\\group.py',
   'PYMODULE'),
  ('app.schemas.task',
   'E:\\PythonWBYK\\fastApiProject\\app\\schemas\\task.py',
   'PYMODULE'),
  ('app.services',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\__init__.py',
   'PYMODULE'),
  ('app.services.device',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\device.py',
   'PYMODULE'),
  ('app.services.dispatcher',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\dispatcher.py',
   'PYMODULE'),
  ('app.services.group',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\group.py',
   'PYMODULE'),
  ('app.services.optimized_scheduler',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\optimized_scheduler.py',
   'PYMODULE'),
  ('app.services.scheduler',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\scheduler.py',
   'PYMODULE'),
  ('app.services.task_dispatcher',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\task_dispatcher.py',
   'PYMODULE'),
  ('app.services.task_sync',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\task_sync.py',
   'PYMODULE'),
  ('app.services.task_timeout_monitor',
   'E:\\PythonWBYK\\fastApiProject\\app\\services\\task_timeout_monitor.py',
   'PYMODULE'),
  ('app.utils',
   'E:\\PythonWBYK\\fastApiProject\\app\\utils\\__init__.py',
   'PYMODULE'),
  ('app.utils.exception_handler',
   'E:\\PythonWBYK\\fastApiProject\\app\\utils\\exception_handler.py',
   'PYMODULE'),
  ('app.utils.time_utils',
   'E:\\PythonWBYK\\fastApiProject\\app\\utils\\time_utils.py',
   'PYMODULE'),
  ('app.websocket', '-', 'PYMODULE'),
  ('app.websocket.ws_manager',
   'E:\\PythonWBYK\\fastApiProject\\app\\websocket\\ws_manager.py',
   'PYMODULE'),
  ('argparse', 'D:\\myProgram\\Python39\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\myProgram\\Python39\\lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\myProgram\\Python39\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\myProgram\\Python39\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\myProgram\\Python39\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\myProgram\\Python39\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\myProgram\\Python39\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\myProgram\\Python39\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\myProgram\\Python39\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\myProgram\\Python39\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\myProgram\\Python39\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\myProgram\\Python39\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\myProgram\\Python39\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\myProgram\\Python39\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\myProgram\\Python39\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\myProgram\\Python39\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\myProgram\\Python39\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\myProgram\\Python39\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\myProgram\\Python39\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\myProgram\\Python39\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\myProgram\\Python39\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\myProgram\\Python39\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\myProgram\\Python39\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\myProgram\\Python39\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\myProgram\\Python39\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\myProgram\\Python39\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\myProgram\\Python39\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\myProgram\\Python39\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\myProgram\\Python39\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\myProgram\\Python39\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\myProgram\\Python39\\lib\\calendar.py', 'PYMODULE'),
  ('click',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'D:\\myProgram\\Python39\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\myProgram\\Python39\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\myProgram\\Python39\\lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'D:\\myProgram\\Python39\\lib\\colorsys.py', 'PYMODULE'),
  ('concurrent',
   'D:\\myProgram\\Python39\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\myProgram\\Python39\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\myProgram\\Python39\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\myProgram\\Python39\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\myProgram\\Python39\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser', 'D:\\myProgram\\Python39\\lib\\configparser.py', 'PYMODULE'),
  ('contextlib', 'D:\\myProgram\\Python39\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\myProgram\\Python39\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\myProgram\\Python39\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('csv', 'D:\\myProgram\\Python39\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\myProgram\\Python39\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'D:\\myProgram\\Python39\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\myProgram\\Python39\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses', 'D:\\myProgram\\Python39\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\myProgram\\Python39\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\myProgram\\Python39\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\myProgram\\Python39\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\myProgram\\Python39\\lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\myProgram\\Python39\\lib\\doctest.py', 'PYMODULE'),
  ('dotenv',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email', 'D:\\myProgram\\Python39\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\myProgram\\Python39\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\myProgram\\Python39\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\myProgram\\Python39\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\myProgram\\Python39\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\myProgram\\Python39\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\myProgram\\Python39\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\myProgram\\Python39\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\myProgram\\Python39\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\myProgram\\Python39\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\myProgram\\Python39\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\myProgram\\Python39\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\myProgram\\Python39\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\myProgram\\Python39\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\myProgram\\Python39\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\myProgram\\Python39\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\myProgram\\Python39\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\myProgram\\Python39\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\myProgram\\Python39\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'D:\\myProgram\\Python39\\lib\\email\\utils.py', 'PYMODULE'),
  ('exceptiongroup',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\exceptiongroup\\__init__.py',
   'PYMODULE'),
  ('exceptiongroup._catch',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\exceptiongroup\\_catch.py',
   'PYMODULE'),
  ('exceptiongroup._exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\exceptiongroup\\_exceptions.py',
   'PYMODULE'),
  ('exceptiongroup._formatting',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\exceptiongroup\\_formatting.py',
   'PYMODULE'),
  ('exceptiongroup._suppress',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\exceptiongroup\\_suppress.py',
   'PYMODULE'),
  ('exceptiongroup._version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\exceptiongroup\\_version.py',
   'PYMODULE'),
  ('fastapi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\__init__.py',
   'PYMODULE'),
  ('fastapi._compat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\_compat.py',
   'PYMODULE'),
  ('fastapi.applications',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\applications.py',
   'PYMODULE'),
  ('fastapi.background',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\background.py',
   'PYMODULE'),
  ('fastapi.concurrency',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\concurrency.py',
   'PYMODULE'),
  ('fastapi.datastructures',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\datastructures.py',
   'PYMODULE'),
  ('fastapi.dependencies',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\dependencies\\__init__.py',
   'PYMODULE'),
  ('fastapi.dependencies.models',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\dependencies\\models.py',
   'PYMODULE'),
  ('fastapi.dependencies.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\dependencies\\utils.py',
   'PYMODULE'),
  ('fastapi.encoders',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\encoders.py',
   'PYMODULE'),
  ('fastapi.exception_handlers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\exception_handlers.py',
   'PYMODULE'),
  ('fastapi.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\exceptions.py',
   'PYMODULE'),
  ('fastapi.logger',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\logger.py',
   'PYMODULE'),
  ('fastapi.middleware',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\middleware\\__init__.py',
   'PYMODULE'),
  ('fastapi.middleware.cors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\middleware\\cors.py',
   'PYMODULE'),
  ('fastapi.openapi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\openapi\\__init__.py',
   'PYMODULE'),
  ('fastapi.openapi.constants',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\openapi\\constants.py',
   'PYMODULE'),
  ('fastapi.openapi.docs',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\openapi\\docs.py',
   'PYMODULE'),
  ('fastapi.openapi.models',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\openapi\\models.py',
   'PYMODULE'),
  ('fastapi.openapi.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\openapi\\utils.py',
   'PYMODULE'),
  ('fastapi.param_functions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\param_functions.py',
   'PYMODULE'),
  ('fastapi.params',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\params.py',
   'PYMODULE'),
  ('fastapi.requests',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\requests.py',
   'PYMODULE'),
  ('fastapi.responses',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\responses.py',
   'PYMODULE'),
  ('fastapi.routing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\routing.py',
   'PYMODULE'),
  ('fastapi.security',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\security\\__init__.py',
   'PYMODULE'),
  ('fastapi.security.api_key',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\security\\api_key.py',
   'PYMODULE'),
  ('fastapi.security.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\security\\base.py',
   'PYMODULE'),
  ('fastapi.security.http',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\security\\http.py',
   'PYMODULE'),
  ('fastapi.security.oauth2',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\security\\oauth2.py',
   'PYMODULE'),
  ('fastapi.security.open_id_connect_url',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\security\\open_id_connect_url.py',
   'PYMODULE'),
  ('fastapi.security.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\security\\utils.py',
   'PYMODULE'),
  ('fastapi.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\types.py',
   'PYMODULE'),
  ('fastapi.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\utils.py',
   'PYMODULE'),
  ('fastapi.websockets',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\fastapi\\websockets.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\myProgram\\Python39\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\myProgram\\Python39\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\myProgram\\Python39\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\myProgram\\Python39\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\myProgram\\Python39\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\myProgram\\Python39\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\myProgram\\Python39\\lib\\glob.py', 'PYMODULE'),
  ('greenlet',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip', 'D:\\myProgram\\Python39\\lib\\gzip.py', 'PYMODULE'),
  ('h11',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._abnf',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._events',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._readers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._writers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('hashlib', 'D:\\myProgram\\Python39\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\myProgram\\Python39\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\myProgram\\Python39\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\myProgram\\Python39\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'D:\\myProgram\\Python39\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\myProgram\\Python39\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\myProgram\\Python39\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\myProgram\\Python39\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server', 'D:\\myProgram\\Python39\\lib\\http\\server.py', 'PYMODULE'),
  ('httptools',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\httptools\\__init__.py',
   'PYMODULE'),
  ('httptools._version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\httptools\\_version.py',
   'PYMODULE'),
  ('httptools.parser',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\httptools\\parser\\__init__.py',
   'PYMODULE'),
  ('httptools.parser.errors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\httptools\\parser\\errors.py',
   'PYMODULE'),
  ('idna',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'D:\\myProgram\\Python39\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\myProgram\\Python39\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\myProgram\\Python39\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'D:\\myProgram\\Python39\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\myProgram\\Python39\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\myProgram\\Python39\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\myProgram\\Python39\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\myProgram\\Python39\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\myProgram\\Python39\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata._typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\_typing.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect', 'D:\\myProgram\\Python39\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\myProgram\\Python39\\lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\myProgram\\Python39\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\myProgram\\Python39\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\myProgram\\Python39\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\myProgram\\Python39\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging', 'D:\\myProgram\\Python39\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('logging.config',
   'D:\\myProgram\\Python39\\lib\\logging\\config.py',
   'PYMODULE'),
  ('logging.handlers',
   'D:\\myProgram\\Python39\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma', 'D:\\myProgram\\Python39\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\myProgram\\Python39\\lib\\mimetypes.py', 'PYMODULE'),
  ('multipart',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\multipart\\__init__.py',
   'PYMODULE'),
  ('multipart.multipart',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\multipart\\multipart.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\myProgram\\Python39\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\myProgram\\Python39\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\myProgram\\Python39\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\myProgram\\Python39\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\myProgram\\Python39\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\myProgram\\Python39\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'D:\\myProgram\\Python39\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\myProgram\\Python39\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\myProgram\\Python39\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\myProgram\\Python39\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\myProgram\\Python39\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\myProgram\\Python39\\lib\\pprint.py', 'PYMODULE'),
  ('psycopg2',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\__init__.py',
   'PYMODULE'),
  ('psycopg2._ipaddress',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\_ipaddress.py',
   'PYMODULE'),
  ('psycopg2._json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\_json.py',
   'PYMODULE'),
  ('psycopg2._range',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\_range.py',
   'PYMODULE'),
  ('psycopg2.extensions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\extensions.py',
   'PYMODULE'),
  ('psycopg2.extras',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\extras.py',
   'PYMODULE'),
  ('psycopg2.sql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\psycopg2\\sql.py',
   'PYMODULE'),
  ('py_compile', 'D:\\myProgram\\Python39\\lib\\py_compile.py', 'PYMODULE'),
  ('pydantic',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_config.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_core_utils.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_decorators.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py',
   'PYMODULE'),
  ('pydantic._internal._docs_extraction',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_docs_extraction.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_fields.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_generate_schema.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_generics.py',
   'PYMODULE'),
  ('pydantic._internal._git',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_git.py',
   'PYMODULE'),
  ('pydantic._internal._import_utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_import_utils.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_model_construction.py',
   'PYMODULE'),
  ('pydantic._internal._namespace_utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_namespace_utils.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_repr.py',
   'PYMODULE'),
  ('pydantic._internal._schema_gather',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_schema_gather.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py',
   'PYMODULE'),
  ('pydantic._internal._serializers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_serializers.py',
   'PYMODULE'),
  ('pydantic._internal._signature',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_signature.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_typing_extra.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_utils.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_internal\\_validators.py',
   'PYMODULE'),
  ('pydantic._migration',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\_migration.py',
   'PYMODULE'),
  ('pydantic.alias_generators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\alias_generators.py',
   'PYMODULE'),
  ('pydantic.aliases',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\aliases.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\annotated_handlers.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\class_validators.py',
   'PYMODULE'),
  ('pydantic.color',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\color.py',
   'PYMODULE'),
  ('pydantic.config',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\config.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.datetime_parse',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.decorator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\__init__.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\class_validators.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\config.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated.decorator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\json.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\parse.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\deprecated\\tools.py',
   'PYMODULE'),
  ('pydantic.env_settings',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\env_settings.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.errors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\errors.py',
   'PYMODULE'),
  ('pydantic.experimental',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\experimental\\__init__.py',
   'PYMODULE'),
  ('pydantic.experimental.arguments_schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\experimental\\arguments_schema.py',
   'PYMODULE'),
  ('pydantic.experimental.pipeline',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\experimental\\pipeline.py',
   'PYMODULE'),
  ('pydantic.fields',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\fields.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\functional_serializers.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\functional_validators.py',
   'PYMODULE'),
  ('pydantic.generics',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\generics.py',
   'PYMODULE'),
  ('pydantic.json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\json.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\json_schema.py',
   'PYMODULE'),
  ('pydantic.main',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\main.py',
   'PYMODULE'),
  ('pydantic.mypy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\mypy.py',
   'PYMODULE'),
  ('pydantic.networks',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\networks.py',
   'PYMODULE'),
  ('pydantic.parse',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\parse.py',
   'PYMODULE'),
  ('pydantic.plugin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\plugin\\__init__.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\plugin\\_loader.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\plugin\\_schema_validator.py',
   'PYMODULE'),
  ('pydantic.root_model',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\root_model.py',
   'PYMODULE'),
  ('pydantic.schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\schema.py',
   'PYMODULE'),
  ('pydantic.tools',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\tools.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\type_adapter.py',
   'PYMODULE'),
  ('pydantic.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\types.py',
   'PYMODULE'),
  ('pydantic.typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\typing.py',
   'PYMODULE'),
  ('pydantic.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\utils.py',
   'PYMODULE'),
  ('pydantic.v1',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\__init__.py',
   'PYMODULE'),
  ('pydantic.v1._hypothesis_plugin',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\_hypothesis_plugin.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1.class_validators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\color.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\config.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\decorator.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\env_settings.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\errors.py',
   'PYMODULE'),
  ('pydantic.v1.fields',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\fields.py',
   'PYMODULE'),
  ('pydantic.v1.generics',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\generics.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\json.py',
   'PYMODULE'),
  ('pydantic.v1.main',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\main.py',
   'PYMODULE'),
  ('pydantic.v1.mypy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\mypy.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\networks.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\parse.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\schema.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\tools.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\types.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\typing.py',
   'PYMODULE'),
  ('pydantic.v1.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\utils.py',
   'PYMODULE'),
  ('pydantic.v1.validators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\v1\\version.py',
   'PYMODULE'),
  ('pydantic.validate_call_decorator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\validate_call_decorator.py',
   'PYMODULE'),
  ('pydantic.validators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\validators.py',
   'PYMODULE'),
  ('pydantic.version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\version.py',
   'PYMODULE'),
  ('pydantic.warnings',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic\\warnings.py',
   'PYMODULE'),
  ('pydantic_core',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic_core\\__init__.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pydantic_core\\core_schema.py',
   'PYMODULE'),
  ('pydoc', 'D:\\myProgram\\Python39\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\myProgram\\Python39\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\myProgram\\Python39\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pymysql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql._auth',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.charset',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.connections',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.constants',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.converters',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.err',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.times',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('python_multipart',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\python_multipart\\__init__.py',
   'PYMODULE'),
  ('python_multipart.decoders',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\python_multipart\\decoders.py',
   'PYMODULE'),
  ('python_multipart.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\python_multipart\\exceptions.py',
   'PYMODULE'),
  ('python_multipart.multipart',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\python_multipart\\multipart.py',
   'PYMODULE'),
  ('pytz',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'D:\\myProgram\\Python39\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\myProgram\\Python39\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\myProgram\\Python39\\lib\\random.py', 'PYMODULE'),
  ('runpy', 'D:\\myProgram\\Python39\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\myProgram\\Python39\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\myProgram\\Python39\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'D:\\myProgram\\Python39\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\myProgram\\Python39\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\myProgram\\Python39\\lib\\signal.py', 'PYMODULE'),
  ('smtplib', 'D:\\myProgram\\Python39\\lib\\smtplib.py', 'PYMODULE'),
  ('sniffio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket', 'D:\\myProgram\\Python39\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\myProgram\\Python39\\lib\\socketserver.py', 'PYMODULE'),
  ('sqlalchemy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\connectors\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.aioodbc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\connectors\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.asyncio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\connectors\\asyncio.py',
   'PYMODULE'),
  ('sqlalchemy.connectors.pyodbc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.cyextension',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\cyextension\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects._typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.aioodbc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\aioodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.information_schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\information_schema.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pymssql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pymssql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mssql.pyodbc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mssql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.aiomysql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.asyncmy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\asyncmy.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.cymysql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\cymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.dml',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.enumerated',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\enumerated.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.expression',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadb',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mariadbconnector',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqlconnector',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.mysqldb',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqldb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pymysql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pymysql.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.pyodbc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\pyodbc.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reflection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.reserved_words',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reserved_words.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.mysql.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\mysql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.cx_oracle',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.dictionary',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\dictionary.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.oracledb',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\oracledb.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.oracle.vector',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\oracle\\vector.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql._psycopg_common',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\_psycopg_common.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.array',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.asyncpg',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.dml',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ext',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ext.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.hstore',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\hstore.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.named_types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\named_types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.operators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg8000',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.pg_catalog',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg_catalog.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.psycopg2cffi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2cffi.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.ranges',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.postgresql.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.aiosqlite',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\aiosqlite.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.dml',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.json',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\json.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlcipher',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py',
   'PYMODULE'),
  ('sqlalchemy.dialects.sqlite.pysqlite',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py',
   'PYMODULE'),
  ('sqlalchemy.engine',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_processors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\_py_processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_row',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\_py_row.py',
   'PYMODULE'),
  ('sqlalchemy.engine._py_util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.engine.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\base.py',
   'PYMODULE'),
  ('sqlalchemy.engine.characteristics',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\characteristics.py',
   'PYMODULE'),
  ('sqlalchemy.engine.create',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\create.py',
   'PYMODULE'),
  ('sqlalchemy.engine.cursor',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\cursor.py',
   'PYMODULE'),
  ('sqlalchemy.engine.default',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\default.py',
   'PYMODULE'),
  ('sqlalchemy.engine.events',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\events.py',
   'PYMODULE'),
  ('sqlalchemy.engine.interfaces',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.engine.mock',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\mock.py',
   'PYMODULE'),
  ('sqlalchemy.engine.processors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\processors.py',
   'PYMODULE'),
  ('sqlalchemy.engine.reflection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\reflection.py',
   'PYMODULE'),
  ('sqlalchemy.engine.result',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\result.py',
   'PYMODULE'),
  ('sqlalchemy.engine.row',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\row.py',
   'PYMODULE'),
  ('sqlalchemy.engine.url',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\url.py',
   'PYMODULE'),
  ('sqlalchemy.engine.util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\engine\\util.py',
   'PYMODULE'),
  ('sqlalchemy.event',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\event\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.event.api',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\event\\api.py',
   'PYMODULE'),
  ('sqlalchemy.event.attr',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\event\\attr.py',
   'PYMODULE'),
  ('sqlalchemy.event.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\event\\base.py',
   'PYMODULE'),
  ('sqlalchemy.event.legacy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\event\\legacy.py',
   'PYMODULE'),
  ('sqlalchemy.event.registry',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\event\\registry.py',
   'PYMODULE'),
  ('sqlalchemy.exc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.engine',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.exc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.result',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\result.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.scoping',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.ext.asyncio.session',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py',
   'PYMODULE'),
  ('sqlalchemy.ext.baked',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\baked.py',
   'PYMODULE'),
  ('sqlalchemy.ext.compiler',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\ext\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.future',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\future\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.future.engine',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\future\\engine.py',
   'PYMODULE'),
  ('sqlalchemy.inspection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\inspection.py',
   'PYMODULE'),
  ('sqlalchemy.log',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\log.py',
   'PYMODULE'),
  ('sqlalchemy.orm',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.orm._orm_constructors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\_orm_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.orm._typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.orm.attributes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\attributes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.bulk_persistence',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\bulk_persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.clsregistry',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\clsregistry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.collections',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\collections.py',
   'PYMODULE'),
  ('sqlalchemy.orm.context',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\context.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_api',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\decl_api.py',
   'PYMODULE'),
  ('sqlalchemy.orm.decl_base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\decl_base.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dependency',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\dependency.py',
   'PYMODULE'),
  ('sqlalchemy.orm.descriptor_props',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\descriptor_props.py',
   'PYMODULE'),
  ('sqlalchemy.orm.dynamic',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\dynamic.py',
   'PYMODULE'),
  ('sqlalchemy.orm.evaluator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\evaluator.py',
   'PYMODULE'),
  ('sqlalchemy.orm.events',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\events.py',
   'PYMODULE'),
  ('sqlalchemy.orm.exc',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\exc.py',
   'PYMODULE'),
  ('sqlalchemy.orm.identity',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\identity.py',
   'PYMODULE'),
  ('sqlalchemy.orm.instrumentation',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\instrumentation.py',
   'PYMODULE'),
  ('sqlalchemy.orm.interfaces',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\interfaces.py',
   'PYMODULE'),
  ('sqlalchemy.orm.loading',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\loading.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapped_collection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\mapped_collection.py',
   'PYMODULE'),
  ('sqlalchemy.orm.mapper',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\mapper.py',
   'PYMODULE'),
  ('sqlalchemy.orm.path_registry',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\path_registry.py',
   'PYMODULE'),
  ('sqlalchemy.orm.persistence',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\persistence.py',
   'PYMODULE'),
  ('sqlalchemy.orm.properties',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\properties.py',
   'PYMODULE'),
  ('sqlalchemy.orm.query',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\query.py',
   'PYMODULE'),
  ('sqlalchemy.orm.relationships',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\relationships.py',
   'PYMODULE'),
  ('sqlalchemy.orm.scoping',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\scoping.py',
   'PYMODULE'),
  ('sqlalchemy.orm.session',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\session.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\state.py',
   'PYMODULE'),
  ('sqlalchemy.orm.state_changes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\state_changes.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategies',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\strategies.py',
   'PYMODULE'),
  ('sqlalchemy.orm.strategy_options',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\strategy_options.py',
   'PYMODULE'),
  ('sqlalchemy.orm.sync',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\sync.py',
   'PYMODULE'),
  ('sqlalchemy.orm.unitofwork',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\unitofwork.py',
   'PYMODULE'),
  ('sqlalchemy.orm.util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\util.py',
   'PYMODULE'),
  ('sqlalchemy.orm.writeonly',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\orm\\writeonly.py',
   'PYMODULE'),
  ('sqlalchemy.pool',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\pool\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.pool.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\pool\\base.py',
   'PYMODULE'),
  ('sqlalchemy.pool.events',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\pool\\events.py',
   'PYMODULE'),
  ('sqlalchemy.pool.impl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\pool\\impl.py',
   'PYMODULE'),
  ('sqlalchemy.schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.sql._dml_constructors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\_dml_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._elements_constructors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\_elements_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._orm_types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\_orm_types.py',
   'PYMODULE'),
  ('sqlalchemy.sql._py_util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\_py_util.py',
   'PYMODULE'),
  ('sqlalchemy.sql._selectable_constructors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\_selectable_constructors.py',
   'PYMODULE'),
  ('sqlalchemy.sql._typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\_typing.py',
   'PYMODULE'),
  ('sqlalchemy.sql.annotation',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\annotation.py',
   'PYMODULE'),
  ('sqlalchemy.sql.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\base.py',
   'PYMODULE'),
  ('sqlalchemy.sql.cache_key',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\cache_key.py',
   'PYMODULE'),
  ('sqlalchemy.sql.coercions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\coercions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.compiler',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\compiler.py',
   'PYMODULE'),
  ('sqlalchemy.sql.crud',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\crud.py',
   'PYMODULE'),
  ('sqlalchemy.sql.ddl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\ddl.py',
   'PYMODULE'),
  ('sqlalchemy.sql.default_comparator',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\default_comparator.py',
   'PYMODULE'),
  ('sqlalchemy.sql.dml',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\dml.py',
   'PYMODULE'),
  ('sqlalchemy.sql.elements',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\elements.py',
   'PYMODULE'),
  ('sqlalchemy.sql.events',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\events.py',
   'PYMODULE'),
  ('sqlalchemy.sql.expression',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\expression.py',
   'PYMODULE'),
  ('sqlalchemy.sql.functions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\functions.py',
   'PYMODULE'),
  ('sqlalchemy.sql.lambdas',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\lambdas.py',
   'PYMODULE'),
  ('sqlalchemy.sql.naming',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\naming.py',
   'PYMODULE'),
  ('sqlalchemy.sql.operators',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\operators.py',
   'PYMODULE'),
  ('sqlalchemy.sql.roles',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\roles.py',
   'PYMODULE'),
  ('sqlalchemy.sql.schema',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\schema.py',
   'PYMODULE'),
  ('sqlalchemy.sql.selectable',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\selectable.py',
   'PYMODULE'),
  ('sqlalchemy.sql.sqltypes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\sqltypes.py',
   'PYMODULE'),
  ('sqlalchemy.sql.traversals',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\traversals.py',
   'PYMODULE'),
  ('sqlalchemy.sql.type_api',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\type_api.py',
   'PYMODULE'),
  ('sqlalchemy.sql.util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\util.py',
   'PYMODULE'),
  ('sqlalchemy.sql.visitors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\sql\\visitors.py',
   'PYMODULE'),
  ('sqlalchemy.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\types.py',
   'PYMODULE'),
  ('sqlalchemy.util',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\__init__.py',
   'PYMODULE'),
  ('sqlalchemy.util._collections',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util._concurrency_py3k',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\_concurrency_py3k.py',
   'PYMODULE'),
  ('sqlalchemy.util._has_cy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\_has_cy.py',
   'PYMODULE'),
  ('sqlalchemy.util._py_collections',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\_py_collections.py',
   'PYMODULE'),
  ('sqlalchemy.util.compat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\compat.py',
   'PYMODULE'),
  ('sqlalchemy.util.concurrency',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\concurrency.py',
   'PYMODULE'),
  ('sqlalchemy.util.deprecations',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\deprecations.py',
   'PYMODULE'),
  ('sqlalchemy.util.langhelpers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\langhelpers.py',
   'PYMODULE'),
  ('sqlalchemy.util.preloaded',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\preloaded.py',
   'PYMODULE'),
  ('sqlalchemy.util.queue',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\queue.py',
   'PYMODULE'),
  ('sqlalchemy.util.topological',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\topological.py',
   'PYMODULE'),
  ('sqlalchemy.util.typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\sqlalchemy\\util\\typing.py',
   'PYMODULE'),
  ('sqlite3', 'D:\\myProgram\\Python39\\lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\myProgram\\Python39\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\myProgram\\Python39\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'D:\\myProgram\\Python39\\lib\\ssl.py', 'PYMODULE'),
  ('starlette',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\__init__.py',
   'PYMODULE'),
  ('starlette._exception_handler',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\_exception_handler.py',
   'PYMODULE'),
  ('starlette._utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\_utils.py',
   'PYMODULE'),
  ('starlette.applications',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\applications.py',
   'PYMODULE'),
  ('starlette.background',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\background.py',
   'PYMODULE'),
  ('starlette.concurrency',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\concurrency.py',
   'PYMODULE'),
  ('starlette.convertors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\convertors.py',
   'PYMODULE'),
  ('starlette.datastructures',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\datastructures.py',
   'PYMODULE'),
  ('starlette.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\exceptions.py',
   'PYMODULE'),
  ('starlette.formparsers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\formparsers.py',
   'PYMODULE'),
  ('starlette.middleware',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\middleware\\__init__.py',
   'PYMODULE'),
  ('starlette.middleware.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\middleware\\base.py',
   'PYMODULE'),
  ('starlette.middleware.cors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\middleware\\cors.py',
   'PYMODULE'),
  ('starlette.middleware.errors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\middleware\\errors.py',
   'PYMODULE'),
  ('starlette.middleware.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\middleware\\exceptions.py',
   'PYMODULE'),
  ('starlette.requests',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\requests.py',
   'PYMODULE'),
  ('starlette.responses',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\responses.py',
   'PYMODULE'),
  ('starlette.routing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\routing.py',
   'PYMODULE'),
  ('starlette.status',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\status.py',
   'PYMODULE'),
  ('starlette.types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\types.py',
   'PYMODULE'),
  ('starlette.websockets',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\starlette\\websockets.py',
   'PYMODULE'),
  ('statistics', 'D:\\myProgram\\Python39\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\myProgram\\Python39\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\myProgram\\Python39\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\myProgram\\Python39\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\myProgram\\Python39\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\myProgram\\Python39\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\myProgram\\Python39\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\myProgram\\Python39\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\myProgram\\Python39\\lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\myProgram\\Python39\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\myProgram\\Python39\\lib\\tokenize.py', 'PYMODULE'),
  ('tomli',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\tomli\\__init__.py',
   'PYMODULE'),
  ('tomli._parser',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\tomli\\_parser.py',
   'PYMODULE'),
  ('tomli._re',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\tomli\\_re.py',
   'PYMODULE'),
  ('tomli._types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\tomli\\_types.py',
   'PYMODULE'),
  ('tracemalloc', 'D:\\myProgram\\Python39\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\myProgram\\Python39\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\myProgram\\Python39\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('typing_inspection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\typing_inspection\\__init__.py',
   'PYMODULE'),
  ('typing_inspection.introspection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\typing_inspection\\introspection.py',
   'PYMODULE'),
  ('typing_inspection.typing_objects',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\typing_inspection\\typing_objects.py',
   'PYMODULE'),
  ('unittest',
   'D:\\myProgram\\Python39\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\myProgram\\Python39\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\myProgram\\Python39\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\myProgram\\Python39\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\myProgram\\Python39\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\myProgram\\Python39\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\myProgram\\Python39\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\myProgram\\Python39\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\myProgram\\Python39\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\myProgram\\Python39\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\myProgram\\Python39\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib', 'D:\\myProgram\\Python39\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error',
   'D:\\myProgram\\Python39\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\myProgram\\Python39\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\myProgram\\Python39\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\myProgram\\Python39\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('uu', 'D:\\myProgram\\Python39\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'D:\\myProgram\\Python39\\lib\\uuid.py', 'PYMODULE'),
  ('uvicorn',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\__init__.py',
   'PYMODULE'),
  ('uvicorn.__main__',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\__main__.py',
   'PYMODULE'),
  ('uvicorn._subprocess',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\_subprocess.py',
   'PYMODULE'),
  ('uvicorn._types',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\_types.py',
   'PYMODULE'),
  ('uvicorn.config',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\config.py',
   'PYMODULE'),
  ('uvicorn.importer',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\importer.py',
   'PYMODULE'),
  ('uvicorn.lifespan',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\lifespan\\__init__.py',
   'PYMODULE'),
  ('uvicorn.lifespan.off',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\lifespan\\off.py',
   'PYMODULE'),
  ('uvicorn.lifespan.on',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\lifespan\\on.py',
   'PYMODULE'),
  ('uvicorn.logging',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\logging.py',
   'PYMODULE'),
  ('uvicorn.loops',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\loops\\__init__.py',
   'PYMODULE'),
  ('uvicorn.loops.asyncio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\loops\\asyncio.py',
   'PYMODULE'),
  ('uvicorn.loops.auto',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\loops\\auto.py',
   'PYMODULE'),
  ('uvicorn.loops.uvloop',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\loops\\uvloop.py',
   'PYMODULE'),
  ('uvicorn.main',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\main.py',
   'PYMODULE'),
  ('uvicorn.middleware',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\middleware\\__init__.py',
   'PYMODULE'),
  ('uvicorn.middleware.asgi2',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\middleware\\asgi2.py',
   'PYMODULE'),
  ('uvicorn.middleware.message_logger',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\middleware\\message_logger.py',
   'PYMODULE'),
  ('uvicorn.middleware.proxy_headers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\middleware\\proxy_headers.py',
   'PYMODULE'),
  ('uvicorn.middleware.wsgi',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\middleware\\wsgi.py',
   'PYMODULE'),
  ('uvicorn.protocols',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.http',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.auto',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.flow_control',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\flow_control.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.h11_impl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\h11_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.httptools_impl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\httptools_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\utils.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.auto',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.websockets_impl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\websockets_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.wsproto_impl',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\wsproto_impl.py',
   'PYMODULE'),
  ('uvicorn.server',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\server.py',
   'PYMODULE'),
  ('uvicorn.supervisors',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\__init__.py',
   'PYMODULE'),
  ('uvicorn.supervisors.basereload',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\basereload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.multiprocess',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\multiprocess.py',
   'PYMODULE'),
  ('uvicorn.supervisors.statreload',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\statreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.watchfilesreload',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\watchfilesreload.py',
   'PYMODULE'),
  ('uvicorn.workers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\uvicorn\\workers.py',
   'PYMODULE'),
  ('watchfiles',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\watchfiles\\__init__.py',
   'PYMODULE'),
  ('watchfiles.filters',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\watchfiles\\filters.py',
   'PYMODULE'),
  ('watchfiles.main',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\watchfiles\\main.py',
   'PYMODULE'),
  ('watchfiles.run',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\watchfiles\\run.py',
   'PYMODULE'),
  ('watchfiles.version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\watchfiles\\version.py',
   'PYMODULE'),
  ('webbrowser', 'D:\\myProgram\\Python39\\lib\\webbrowser.py', 'PYMODULE'),
  ('websockets',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\__init__.py',
   'PYMODULE'),
  ('websockets.__main__',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\__main__.py',
   'PYMODULE'),
  ('websockets.asyncio',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\__init__.py',
   'PYMODULE'),
  ('websockets.asyncio.async_timeout',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\async_timeout.py',
   'PYMODULE'),
  ('websockets.asyncio.client',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\client.py',
   'PYMODULE'),
  ('websockets.asyncio.compatibility',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\compatibility.py',
   'PYMODULE'),
  ('websockets.asyncio.connection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\connection.py',
   'PYMODULE'),
  ('websockets.asyncio.messages',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\messages.py',
   'PYMODULE'),
  ('websockets.asyncio.router',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\router.py',
   'PYMODULE'),
  ('websockets.asyncio.server',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\asyncio\\server.py',
   'PYMODULE'),
  ('websockets.auth',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\auth.py',
   'PYMODULE'),
  ('websockets.cli',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\cli.py',
   'PYMODULE'),
  ('websockets.client',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\client.py',
   'PYMODULE'),
  ('websockets.connection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\connection.py',
   'PYMODULE'),
  ('websockets.datastructures',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\datastructures.py',
   'PYMODULE'),
  ('websockets.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\exceptions.py',
   'PYMODULE'),
  ('websockets.extensions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\extensions\\__init__.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\extensions\\base.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\extensions\\permessage_deflate.py',
   'PYMODULE'),
  ('websockets.frames',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\frames.py',
   'PYMODULE'),
  ('websockets.headers',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\headers.py',
   'PYMODULE'),
  ('websockets.http',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\http.py',
   'PYMODULE'),
  ('websockets.http11',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\http11.py',
   'PYMODULE'),
  ('websockets.imports',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\imports.py',
   'PYMODULE'),
  ('websockets.legacy',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\auth.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\client.py',
   'PYMODULE'),
  ('websockets.legacy.exceptions',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\exceptions.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\framing.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\handshake.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\http.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\legacy\\server.py',
   'PYMODULE'),
  ('websockets.protocol',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\protocol.py',
   'PYMODULE'),
  ('websockets.server',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\server.py',
   'PYMODULE'),
  ('websockets.streams',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\streams.py',
   'PYMODULE'),
  ('websockets.sync',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\sync\\__init__.py',
   'PYMODULE'),
  ('websockets.sync.client',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\sync\\client.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\sync\\connection.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\sync\\messages.py',
   'PYMODULE'),
  ('websockets.sync.router',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\sync\\router.py',
   'PYMODULE'),
  ('websockets.sync.server',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\sync\\server.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\sync\\utils.py',
   'PYMODULE'),
  ('websockets.typing',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\typing.py',
   'PYMODULE'),
  ('websockets.uri',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\uri.py',
   'PYMODULE'),
  ('websockets.utils',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\utils.py',
   'PYMODULE'),
  ('websockets.version',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\websockets\\version.py',
   'PYMODULE'),
  ('xml', 'D:\\myProgram\\Python39\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'D:\\myProgram\\Python39\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\myProgram\\Python39\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\myProgram\\Python39\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\myProgram\\Python39\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client',
   'D:\\myProgram\\Python39\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile', 'D:\\myProgram\\Python39\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\myProgram\\Python39\\lib\\zipimport.py', 'PYMODULE'),
  ('zipp',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp._functools',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('zipp.compat',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.glob',
   'E:\\PythonWBYK\\fastApiProject\\.venv\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zoneinfo',
   'D:\\myProgram\\Python39\\lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'D:\\myProgram\\Python39\\lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'D:\\myProgram\\Python39\\lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'D:\\myProgram\\Python39\\lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE')])
