#!/bin/bash

# 启动所有服务脚本

echo "=== 启动微博任务管理系统 ==="

# 1. 检查Docker镜像
echo "1. 检查Docker镜像..."
if docker images wb_backend:latest | grep -q wb_backend; then
    echo "✅ wb_backend镜像存在"
else
    echo "❌ wb_backend镜像不存在，请先构建镜像"
    echo "运行: ./quick_success_build.sh"
    exit 1
fi

# 2. 停止现有服务
echo "2. 停止现有服务..."
docker-compose down 2>/dev/null || true
docker stop wb_backend wb_mysql wb_redis wb_nginx 2>/dev/null || true
docker rm wb_backend wb_mysql wb_redis wb_nginx 2>/dev/null || true

# 3. 创建必要的目录
echo "3. 创建必要的目录..."
mkdir -p logs
mkdir -p mysql/init
mkdir -p nginx/conf.d

# 4. 创建MySQL初始化脚本
echo "4. 创建MySQL初始化脚本..."
cat > mysql/init/init.sql << 'EOF'
-- 创建数据库
CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE wb;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'wb_user'@'%' IDENTIFIED BY 'wb_password';
GRANT ALL PRIVILEGES ON wb.* TO 'wb_user'@'%';
FLUSH PRIVILEGES;

-- 基础表结构会由应用自动创建
EOF

# 5. 创建Nginx配置
echo "5. 创建Nginx配置..."
cat > nginx/conf.d/default.conf << 'EOF'
server {
    listen 80;
    server_name localhost;

    # 后端API代理
    location /api/ {
        proxy_pass http://backend:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket代理
    location /ws {
        proxy_pass http://backend:8000/ws;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 直接访问后端（用于测试）
    location / {
        proxy_pass http://backend:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# 6. 启动服务
echo "6. 启动服务..."
echo "启动MySQL和Redis..."
docker-compose up -d mysql redis

# 等待数据库启动
echo "等待数据库启动..."
sleep 15

# 检查数据库状态
echo "检查数据库状态..."
for i in {1..30}; do
    if docker-compose exec -T mysql mysqladmin ping -h localhost -u root -p123456 >/dev/null 2>&1; then
        echo "✅ MySQL已启动"
        break
    fi
    echo "等待MySQL启动... ($i/30)"
    sleep 2
done

# 启动后端服务
echo "启动后端服务..."
docker-compose up -d backend

# 等待后端启动
echo "等待后端服务启动..."
sleep 10

# 检查后端状态
echo "检查后端状态..."
for i in {1..20}; do
    if curl -s http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ 后端服务已启动"
        break
    fi
    echo "等待后端启动... ($i/20)"
    sleep 3
done

# 7. 显示服务状态
echo ""
echo "7. 服务状态检查..."
echo "Docker容器状态:"
docker-compose ps

echo ""
echo "服务健康检查:"

# 检查MySQL
if docker-compose exec -T mysql mysqladmin ping -h localhost -u root -p123456 >/dev/null 2>&1; then
    echo "✅ MySQL: 正常"
else
    echo "❌ MySQL: 异常"
fi

# 检查Redis
if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
    echo "✅ Redis: 正常"
else
    echo "❌ Redis: 异常"
fi

# 检查后端
if curl -s http://localhost:8000/health >/dev/null 2>&1; then
    echo "✅ 后端API: 正常"
else
    echo "❌ 后端API: 异常"
fi

echo ""
echo "🎉 服务启动完成！"
echo ""
echo "访问地址："
echo "  - 后端API: http://localhost:8000"
echo "  - API文档: http://localhost:8000/docs"
echo "  - 健康检查: http://localhost:8000/health"
echo ""
echo "数据库连接信息："
echo "  - 主机: localhost:3306"
echo "  - 数据库: wb"
echo "  - 用户: wb_user"
echo "  - 密码: wb_password"
echo ""
echo "管理命令："
echo "  - 查看日志: docker-compose logs -f"
echo "  - 停止服务: docker-compose down"
echo "  - 重启服务: docker-compose restart"
echo ""
echo "如果需要启动Nginx反向代理："
echo "  docker-compose up -d nginx"
