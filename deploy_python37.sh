#!/bin/bash

# 基于Python3.7的微博任务管理系统部署
# 兼容宝塔面板

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

echo "========================================"
echo "  微博任务管理系统 - Python3.7部署"
echo "========================================"
echo "兼容宝塔面板，使用系统自带Python3.7"
echo ""

# 1. 检查Python3.7环境
log_step "1. 检查Python3.7环境"

if /usr/bin/python3 --version 2>&1 | grep -q "3.7"; then
    PYTHON_VERSION=$(/usr/bin/python3 --version)
    log_info "✅ 找到Python3.7: $PYTHON_VERSION"
    PYTHON_CMD="/usr/bin/python3"
    PIP_CMD="/usr/bin/pip3"
else
    log_error "❌ 未找到Python3.7，请检查系统安装"
    exit 1
fi

# 2. 检查pip3（使用python3 -m pip）
if $PYTHON_CMD -m pip --version >/dev/null 2>&1; then
    log_info "✅ pip可用（通过python3 -m pip）"
    PIP_CMD="$PYTHON_CMD -m pip"
else
    log_info "安装pip..."
    sudo dnf install -y python3-pip
    # 如果还是有问题，下载安装
    if ! $PYTHON_CMD -m pip --version >/dev/null 2>&1; then
        curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
        $PYTHON_CMD get-pip.py --user
        rm get-pip.py
        PIP_CMD="$PYTHON_CMD -m pip"
    fi
fi

# 3. 安装MariaDB（兼容MySQL）
log_step "3. 安装MariaDB数据库"

if systemctl is-active --quiet mariadb; then
    log_info "✅ MariaDB已运行"
elif systemctl is-enabled --quiet mariadb; then
    log_info "启动MariaDB..."
    sudo systemctl start mariadb
else
    log_info "安装MariaDB..."
    sudo dnf install -y mariadb-server mariadb-devel
    sudo systemctl start mariadb
    sudo systemctl enable mariadb
    
    log_warn "请设置MariaDB root密码为: 123456"
    sudo mysql_secure_installation
fi

# 4. 创建数据库
log_step "4. 创建数据库"

if mysql -u root -p123456 -e "USE wb;" 2>/dev/null; then
    log_info "✅ 数据库wb已存在"
else
    log_info "创建数据库wb..."
    mysql -u root -p123456 << 'EOF' || {
        log_error "数据库创建失败，请手动执行:"
        echo "mysql -u root -p"
        echo "CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        exit 1
    }
CREATE DATABASE IF NOT EXISTS wb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
FLUSH PRIVILEGES;
EOF
    log_info "✅ 数据库创建成功"
fi

# 5. 创建虚拟环境（使用Python3.7）
log_step "5. 创建Python虚拟环境"

if [ -d "venv37" ]; then
    log_info "✅ 虚拟环境已存在"
else
    log_info "创建Python3.7虚拟环境..."
    $PYTHON_CMD -m venv venv37
    log_info "✅ 虚拟环境创建成功"
fi

# 激活虚拟环境
source venv37/bin/activate

# 6. 安装Python依赖
log_step "6. 安装Python依赖"

log_info "升级pip..."
python -m pip install --upgrade pip

log_info "安装项目依赖..."
# 为Python3.9安装兼容版本（系统实际是3.9）
python -m pip install \
    "fastapi>=0.68.0,<1.0.0" \
    "uvicorn[standard]>=0.15.0,<1.0.0" \
    "sqlalchemy>=1.4.0,<2.0.0" \
    "pymysql>=1.0.0" \
    "python-dotenv>=0.19.0" \
    "pydantic>=1.8.0,<2.0.0" \
    "websockets>=10.0" \
    "requests>=2.25.0" \
    "python-multipart>=0.0.5"

log_info "✅ 依赖安装完成"

# 7. 修改config.py兼容Python3.7
log_step "7. 配置数据库连接"

# 备份原配置
cp app/config.py app/config.py.backup 2>/dev/null || true

cat > app/config.py << 'EOF'
import os

# MySQL数据库配置 - 兼容Python3.7
MYSQL_USER = os.getenv("MYSQL_USER", "root")
MYSQL_PASSWORD = os.getenv("MYSQL_PASSWORD", "123456")
MYSQL_HOST = os.getenv("MYSQL_HOST", "127.0.0.1")
MYSQL_PORT = os.getenv("MYSQL_PORT", "3306")
MYSQL_DB = os.getenv("MYSQL_DB", "wb")

# 使用字符串格式化（兼容Python3.7）
DATABASE_URL = "mysql+pymysql://{}:{}@{}:{}/{}?charset=utf8mb4".format(
    MYSQL_USER, MYSQL_PASSWORD, MYSQL_HOST, MYSQL_PORT, MYSQL_DB
)

print("[CONFIG] Database: {}@{}:{}/{}".format(MYSQL_USER, MYSQL_HOST, MYSQL_PORT, MYSQL_DB))
EOF

# 8. 测试数据库连接
log_step "8. 测试数据库连接"

python3 -c "
import sys
sys.path.append('.')
try:
    from app.config import DATABASE_URL
    from sqlalchemy import create_engine, text
    engine = create_engine(DATABASE_URL)
    with engine.connect() as conn:
        conn.execute(text('SELECT 1'))
    print('✅ 数据库连接成功')
except Exception as e:
    print('❌ 数据库连接失败: {}'.format(e))
    sys.exit(1)
"

# 9. 创建启动脚本
log_step "9. 创建启动脚本"

cat > start_python37.sh << 'EOF'
#!/bin/bash

cd "$(dirname "$0")"

echo "========================================"
echo "  微博任务管理系统 - Python3.7版本"
echo "========================================"

# 检查MariaDB状态
if ! systemctl is-active --quiet mariadb; then
    echo "启动MariaDB..."
    sudo systemctl start mariadb
    sleep 3
fi

# 激活Python3.7虚拟环境
echo "激活Python3.7环境..."
source venv37/bin/activate

# 设置环境变量
export MYSQL_USER=root
export MYSQL_PASSWORD=123456
export MYSQL_HOST=127.0.0.1
export MYSQL_PORT=3306
export MYSQL_DB=wb

# 测试数据库连接
echo "测试数据库连接..."
if mysql -u root -p123456 -e "SELECT 1;" >/dev/null 2>&1; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败"
    exit 1
fi

echo ""
echo "========================================"
echo "  启动信息"
echo "========================================"
echo "Python版本: $(python --version)"
echo "数据库: MariaDB"
echo "访问地址: http://***********:11234"
echo "API文档: http://***********:11234/docs"
echo "========================================"
echo ""

# 启动FastAPI应用
echo "启动微博任务管理系统..."
uvicorn app.main:app --host 0.0.0.0 --port 11234 --reload
EOF

chmod +x start_python37.sh

# 10. 创建系统服务（可选）
log_step "10. 创建系统服务"

sudo tee /etc/systemd/system/wb-python37.service << EOF
[Unit]
Description=微博任务管理系统 Python3.7版本
After=network.target mariadb.service

[Service]
Type=simple
User=$(whoami)
WorkingDirectory=$(pwd)
Environment=PATH=$(pwd)/venv37/bin
Environment=MYSQL_USER=root
Environment=MYSQL_PASSWORD=123456
Environment=MYSQL_HOST=127.0.0.1
Environment=MYSQL_PORT=3306
Environment=MYSQL_DB=wb
ExecStart=$(pwd)/venv37/bin/uvicorn app.main:app --host 0.0.0.0 --port 11234
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl enable wb-python37

echo ""
echo "========================================"
echo "  Python3.7部署完成！"
echo "========================================"
echo ""
log_info "兼容宝塔面板，使用系统Python3.7"
log_info "虚拟环境: venv37/"
log_info "数据库: MariaDB"
echo ""
log_info "启动方式:"
echo "  手动启动: ./start_python37.sh"
echo "  服务启动: sudo systemctl start wb-python37"
echo ""
log_info "访问地址:"
echo "  - 主页: http://***********:11234"
echo "  - API文档: http://***********:11234/docs"
echo "  - 健康检查: http://***********:11234/health"
echo ""
log_info "管理命令:"
echo "  - 查看状态: sudo systemctl status wb-python37"
echo "  - 查看日志: sudo journalctl -u wb-python37 -f"
echo "  - 重启服务: sudo systemctl restart wb-python37"
echo ""
echo "🎉 Python3.7部署完成！兼容宝塔面板"
