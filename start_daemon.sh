#!/bin/bash

# 后台启动脚本

echo "=== 微博任务管理系统 - 后台启动 ==="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 检查应用目录
APP_DIR="/opt/wb_system"
if [ ! -d "$APP_DIR" ]; then
    log_warn "应用目录不存在，使用当前目录"
    APP_DIR=$(pwd)
fi

cd $APP_DIR

# 检查虚拟环境
if [ ! -d "venv" ]; then
    log_warn "虚拟环境不存在"
    exit 1
fi

# 激活虚拟环境
source venv/bin/activate

# 加载环境变量
if [ -f ".env" ]; then
    export $(cat .env | grep -v '^#' | xargs)
    log_info "环境变量已加载"
fi

# 检查端口占用
if netstat -tlnp | grep :8000 >/dev/null 2>&1; then
    log_warn "端口8000已被占用"
    echo "当前占用进程:"
    netstat -tlnp | grep :8000
    echo ""
    read -p "是否要停止现有进程并继续？(y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        pkill -f "uvicorn.*8000" || true
        sleep 2
    else
        log_info "退出启动"
        exit 0
    fi
fi

# 创建日志目录
mkdir -p logs

# 后台启动应用
log_info "后台启动应用..."
nohup uvicorn app.main:app --host 0.0.0.0 --port 8000 > logs/app.log 2>&1 &

# 获取进程ID
PID=$!
echo $PID > logs/app.pid

# 等待启动
sleep 5

# 检查进程是否还在运行
if kill -0 $PID 2>/dev/null; then
    log_info "✅ 应用启动成功！"
    echo ""
    echo "🎉 微博任务管理系统已在后台运行"
    echo "📍 应用目录: $APP_DIR"
    echo "🆔 进程ID: $PID"
    echo "📄 日志文件: $APP_DIR/logs/app.log"
    echo "🌐 访问地址: http://***********:8000"
    echo ""
    echo "管理命令:"
    echo "  查看日志: tail -f $APP_DIR/logs/app.log"
    echo "  查看进程: ps aux | grep $PID"
    echo "  停止服务: kill $PID"
    echo "  重启服务: $0"
    echo ""
    echo "现在可以安全退出终端，服务将继续运行！"
else
    log_warn "❌ 应用启动失败"
    echo "查看日志: cat logs/app.log"
    exit 1
fi
