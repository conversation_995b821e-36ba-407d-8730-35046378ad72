#!/bin/bash

# 直接运行应用，不依赖复杂的构建

echo "=== 直接运行简单应用 ==="

# 1. 停止现有容器
echo "1. 停止现有容器..."
docker stop wb_backend 2>/dev/null || true

# 2. 创建超简单的应用
echo "2. 创建超简单应用..."
cat > run_app.py << 'EOF'
#!/usr/bin/env python3

print("正在启动微博任务管理系统...")

try:
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    import uvicorn
    print("✅ FastAPI导入成功")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("正在安装依赖...")
    import subprocess
    subprocess.run(["pip", "install", "fastapi", "uvicorn"])
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    import uvicorn

app = FastAPI(title="微博任务管理系统")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def root():
    return {"message": "微博任务管理系统", "status": "running"}

@app.get("/health")
def health():
    return {"status": "healthy", "service": "wb_backend"}

@app.get("/test")
def test():
    return {"message": "测试成功", "status": "ok"}

if __name__ == "__main__":
    print("🚀 启动服务器...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

# 3. 直接在现有容器中运行
echo "3. 在现有容器中运行应用..."

# 复制文件到容器
docker cp run_app.py wb_backend:/app/run_app.py 2>/dev/null || echo "容器不存在，将创建新容器"

# 如果容器存在，在其中运行
if docker ps -a | grep -q wb_backend; then
    echo "在现有容器中运行..."
    docker exec -d wb_backend python /app/run_app.py
else
    echo "创建新容器运行..."
    docker run -d \
        --name wb_backend_new \
        --network fastapiproject_wb_network \
        -p 8000:8000 \
        -v $(pwd):/app \
        -w /app \
        python:3.9-slim \
        python run_app.py
fi

# 4. 等待启动
echo "4. 等待服务启动..."
sleep 10

# 5. 测试服务
echo "5. 测试服务..."
for i in {1..8}; do
    if curl -s http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ 服务启动成功！"
        echo ""
        echo "测试响应:"
        curl -s http://localhost:8000/health
        echo ""
        curl -s http://localhost:8000/test
        break
    fi
    echo "等待服务启动... ($i/8)"
    sleep 3
done

echo ""
echo "=== 启动完成 ==="
echo ""
echo "🌐 访问地址:"
echo "  http://***********:8000/"
echo "  http://***********:8000/health"
echo "  http://***********:8000/docs"
