#!/bin/bash

# 最终解决方案 - 解决DNS和网络问题

echo "=== 最终Docker构建解决方案 ==="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root权限运行此脚本"
    echo "sudo $0"
    exit 1
fi

# 1. 备份并修复DNS配置
echo "1. 修复DNS配置..."
cp /etc/resolv.conf /etc/resolv.conf.backup.$(date +%Y%m%d_%H%M%S)

cat > /etc/resolv.conf << EOF
nameserver *******
nameserver 114.114.114.114
nameserver 223.5.5.5
nameserver 1.1.1.1
EOF

echo "DNS配置已更新"

# 2. 重启网络服务
echo "2. 重启网络服务..."
systemctl restart systemd-resolved 2>/dev/null || true
systemctl restart NetworkManager 2>/dev/null || true

# 3. 测试网络连接
echo "3. 测试网络连接..."
if ping -c 2 ******* >/dev/null 2>&1; then
    echo "✅ 网络连接正常"
else
    echo "❌ 网络连接异常，请检查网络配置"
    exit 1
fi

# 4. 测试域名解析
echo "4. 测试域名解析..."
if nslookup pypi.org >/dev/null 2>&1; then
    echo "✅ 域名解析正常"
else
    echo "❌ 域名解析仍有问题"
fi

# 5. 配置Docker DNS
echo "5. 配置Docker DNS..."
mkdir -p /etc/docker

cat > /etc/docker/daemon.json << EOF
{
    "dns": ["*******", "114.114.114.114", "223.5.5.5"],
    "registry-mirrors": [
        "https://docker.mirrors.ustc.edu.cn",
        "https://hub-mirror.c.163.com"
    ]
}
EOF

# 6. 重启Docker服务
echo "6. 重启Docker服务..."
systemctl restart docker
sleep 10

# 7. 验证Docker状态
echo "7. 验证Docker状态..."
if docker info >/dev/null 2>&1; then
    echo "✅ Docker服务正常"
else
    echo "❌ Docker服务异常"
    exit 1
fi

# 8. 清理Docker环境
echo "8. 清理Docker环境..."
docker system prune -f >/dev/null 2>&1

# 9. 尝试构建
echo "9. 开始构建..."

# 方法1: 使用主机网络和DNS
echo "方法1: 主机网络模式构建..."
export DOCKER_BUILDKIT=0

cat > Dockerfile.final << 'EOF'
FROM python:3.9-slim

# 设置DNS
RUN echo "nameserver *******" > /etc/resolv.conf

WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_DEFAULT_TIMEOUT=600 \
    PIP_RETRIES=10

# 复制requirements
COPY requirements.txt .

# 安装依赖
RUN pip install --no-cache-dir \
    --index-url https://pypi.tuna.tsinghua.edu.cn/simple \
    --trusted-host pypi.tuna.tsinghua.edu.cn \
    -r requirements.txt

# 复制代码
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF

if docker build -f Dockerfile.final --network=host --dns=******* -t wb_backend:latest .; then
    echo "✅ 方法1构建成功！"
    rm -f Dockerfile.final
    exit 0
fi

echo "方法1失败，尝试方法2..."

# 方法2: 离线构建
echo "方法2: 离线构建..."

# 在宿主机下载依赖
echo "在宿主机下载Python依赖..."
mkdir -p wheels

# 确保pip3可用
if ! command -v pip3 >/dev/null 2>&1; then
    echo "安装pip3..."
    yum install -y python3-pip 2>/dev/null || \
    dnf install -y python3-pip 2>/dev/null || \
    apt-get update && apt-get install -y python3-pip
fi

# 下载依赖
pip3 download -d wheels \
    --index-url https://pypi.tuna.tsinghua.edu.cn/simple \
    --trusted-host pypi.tuna.tsinghua.edu.cn \
    -r requirements.txt

if [ -d "wheels" ] && [ "$(ls -A wheels)" ]; then
    echo "✅ 依赖下载成功"
    
    cat > Dockerfile.offline_final << 'EOF'
FROM python:3.9-slim
WORKDIR /app
COPY wheels/ ./wheels/
COPY requirements.txt .
RUN pip install --no-index --find-links wheels -r requirements.txt
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
EOF
    
    if docker build -f Dockerfile.offline_final -t wb_backend:latest .; then
        echo "✅ 方法2离线构建成功！"
        rm -f Dockerfile.final Dockerfile.offline_final
        exit 0
    fi
fi

echo "❌ 所有方法都失败了"
echo ""
echo "请手动检查："
echo "1. 网络连接: ping *******"
echo "2. DNS解析: nslookup pypi.org"
echo "3. Docker状态: docker info"
echo "4. 防火墙状态: systemctl status firewalld"

rm -f Dockerfile.final Dockerfile.offline_final
exit 1
