"""
配置管理器
用于保存和加载用户配置
"""

import json
import os
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

class ConfigManager:
    def __init__(self, config_file: str = "user_config.json"):
        self.config_file = config_file
        self.default_config = {
            "server": {
                "host": "localhost",
                "port": 8000,
                "use_https": False
            },
            "ui": {
                "auto_refresh": True,
                "refresh_interval": 5,
                "theme": "default"
            },
            "advanced": {
                "request_timeout": 10,
                "websocket_timeout": 30,
                "max_retry_count": 3
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置，确保所有必要的键都存在
                    return self._merge_config(self.default_config, config)
            else:
                logger.info(f"配置文件 {self.config_file} 不存在，使用默认配置")
                return self.default_config.copy()
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return self.default_config.copy()
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            logger.info(f"配置已保存到 {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def _merge_config(self, default: Dict, user: Dict) -> Dict:
        """合并默认配置和用户配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def get(self, key: str, default=None):
        """获取配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        value = self.config
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """设置配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        config = self.config
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        config[keys[-1]] = value
    
    def get_api_base_url(self) -> str:
        """获取API基础URL"""
        host = self.get('server.host', 'localhost')
        port = self.get('server.port', 8000)
        use_https = self.get('server.use_https', False)
        protocol = 'https' if use_https else 'http'
        return f"{protocol}://{host}:{port}"
    
    def get_websocket_url(self) -> str:
        """获取WebSocket URL"""
        host = self.get('server.host', 'localhost')
        port = self.get('server.port', 8000)
        use_https = self.get('server.use_https', False)
        protocol = 'wss' if use_https else 'ws'
        return f"{protocol}://{host}:{port}"
    
    def update_server_config(self, host: str, port: int, use_https: bool = False):
        """更新服务器配置"""
        self.set('server.host', host)
        self.set('server.port', port)
        self.set('server.use_https', use_https)
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self.default_config.copy()
    
    def export_config(self, file_path: str) -> bool:
        """导出配置到指定文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path: str) -> bool:
        """从指定文件导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
            self.config = self._merge_config(self.default_config, imported_config)
            return True
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return False

# 全局配置管理器实例
config_manager = ConfigManager()
